using System;

namespace InventoryManagement.Services.Validation
{
    /// <summary>
    /// Represents a data integrity issue found during validation
    /// </summary>
    public class DataIntegrityIssue
    {
        /// <summary>
        /// Type of entity with the integrity issue
        /// </summary>
        public string EntityType { get; set; }
        
        /// <summary>
        /// Property or field with the integrity issue
        /// </summary>
        public string PropertyName { get; set; }
        
        /// <summary>
        /// Identifier for the affected entity (if applicable)
        /// </summary>
        public string EntityId { get; set; }
        
        /// <summary>
        /// Name of the rule that identified the issue
        /// </summary>
        public string RuleName { get; set; }
        
        /// <summary>
        /// Severity level of the issue
        /// </summary>
        public IssueSeverity Severity { get; set; } = IssueSeverity.Warning;
        
        /// <summary>
        /// User-friendly message describing the issue
        /// </summary>
        public string Message { get; set; }
        
        /// <summary>
        /// Technical details about the issue
        /// </summary>
        public string Details { get; set; }
        
        /// <summary>
        /// When the issue was detected
        /// </summary>
        public DateTime DetectedTime { get; set; } = DateTime.Now;
        
        /// <summary>
        /// Suggestions for resolving the issue
        /// </summary>
        public string ResolutionSuggestion { get; set; }
        
        /// <summary>
        /// SQL query or command that can help fix the issue (if applicable)
        /// </summary>
        public string RemediationQuery { get; set; }
        
        /// <summary>
        /// Whether this issue can be automatically fixed
        /// </summary>
        public bool CanAutoFix { get; set; }
        
        /// <summary>
        /// Whether this issue has been resolved
        /// </summary>
        public bool IsResolved { get; set; }
    }
    
    /// <summary>
    /// Severity levels for data integrity issues
    /// </summary>
    public enum IssueSeverity
    {
        /// <summary>
        /// Informational item, not an actual issue
        /// </summary>
        Info = 0,
        
        /// <summary>
        /// Minor issue that should be addressed but doesn't affect functionality
        /// </summary>
        Warning = 1,
        
        /// <summary>
        /// Significant issue that could affect system functionality
        /// </summary>
        Error = 2,
        
        /// <summary>
        /// Critical issue that must be fixed to ensure data integrity
        /// </summary>
        Critical = 3
    }
}
