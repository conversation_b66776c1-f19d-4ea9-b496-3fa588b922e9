using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text.Json;
using System.Threading.Tasks;
using System.Windows.Input;
using Microsoft.Extensions.Logging;

namespace InventoryManagement.Infrastructure.Shortcuts
{
    /// <summary>
    /// Manages keyboard shortcuts for the application
    /// </summary>
    public class ShortcutManager : IShortcutManager
    {
        private readonly ILogger<ShortcutManager> _logger;
        private readonly string _shortcutsFilePath;
        private Dictionary<string, ShortcutDefinition> _shortcuts;
        private readonly Dictionary<string, Action<object>> _shortcutActions;
        
        public ShortcutManager(ILogger<ShortcutManager> logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _shortcutsFilePath = Path.Combine(AppContext.BaseDirectory, "App_Data", "UserShortcuts.json");
            _shortcuts = new Dictionary<string, ShortcutDefinition>();
            _shortcutActions = new Dictionary<string, Action<object>>();
            
            LoadShortcuts();
        }
        
        /// <summary>
        /// Gets a dictionary of all registered shortcuts
        /// </summary>
        public IReadOnlyDictionary<string, ShortcutDefinition> Shortcuts => _shortcuts;
        
        /// <summary>
        /// Registers an action to be executed when a shortcut is triggered
        /// </summary>
        /// <param name="shortcutName">Name of the shortcut</param>
        /// <param name="action">Action to execute</param>
        public void RegisterShortcutAction(string shortcutName, Action<object> action)
        {
            if (string.IsNullOrEmpty(shortcutName))
            {
                throw new ArgumentNullException(nameof(shortcutName));
            }
            
            _shortcutActions[shortcutName] = action ?? throw new ArgumentNullException(nameof(action));
            _logger.LogDebug("Registered action for shortcut: {ShortcutName}", shortcutName);
        }
        
        /// <summary>
        /// Handles a key event and executes the corresponding shortcut action if found
        /// </summary>
        /// <param name="key">Key that was pressed</param>
        /// <param name="modifiers">Modifier keys that were pressed</param>
        /// <param name="context">Optional context to pass to the action</param>
        /// <returns>True if a shortcut was triggered, false otherwise</returns>
        public bool HandleKeyEvent(Key key, ModifierKeys modifiers, object context = null)
        {
            var shortcutKey = $"{modifiers}+{key}";
            
            var shortcut = _shortcuts.Values.FirstOrDefault(s => 
                s.IsActive && 
                s.KeyCombination.Equals(shortcutKey, StringComparison.OrdinalIgnoreCase));
            
            if (shortcut != null && _shortcutActions.TryGetValue(shortcut.Name, out var action))
            {
                _logger.LogDebug("Executing shortcut: {ShortcutName}", shortcut.Name);
                action(context);
                return true;
            }
            
            return false;
        }
        
        /// <summary>
        /// Adds or updates a shortcut definition
        /// </summary>
        /// <param name="shortcut">Shortcut definition</param>
        public async Task SaveShortcutAsync(ShortcutDefinition shortcut)
        {
            if (shortcut == null)
            {
                throw new ArgumentNullException(nameof(shortcut));
            }
            
            _shortcuts[shortcut.Name] = shortcut;
            await SaveShortcutsAsync();
            _logger.LogInformation("Saved shortcut: {ShortcutName} = {KeyCombination}", 
                shortcut.Name, shortcut.KeyCombination);
        }
        
        /// <summary>
        /// Resets all shortcuts to default values
        /// </summary>
        public async Task ResetToDefaultsAsync()
        {
            _shortcuts = GetDefaultShortcuts();
            await SaveShortcutsAsync();
            _logger.LogInformation("Reset all shortcuts to defaults");
        }
        
        /// <summary>
        /// Gets the default keyboard shortcuts
        /// </summary>
        private Dictionary<string, ShortcutDefinition> GetDefaultShortcuts()
        {
            return new Dictionary<string, ShortcutDefinition>
            {
                ["NewInventoryItem"] = new ShortcutDefinition
                {
                    Name = "NewInventoryItem",
                    DisplayName = "New Inventory Item",
                    KeyCombination = "Ctrl+Alt+N",
                    Description = "Create a new inventory item",
                    Category = "Inventory",
                    IsActive = true
                },
                ["QuickSale"] = new ShortcutDefinition
                {
                    Name = "QuickSale",
                    DisplayName = "Quick Sale",
                    KeyCombination = "F4",
                    Description = "Process a quick sale",
                    Category = "Sales",
                    IsActive = true
                },
                ["BarcodeSearch"] = new ShortcutDefinition
                {
                    Name = "BarcodeSearch",
                    DisplayName = "Barcode Search",
                    KeyCombination = "Ctrl+B",
                    Description = "Search by barcode",
                    Category = "Search",
                    IsActive = true
                },
                ["InventoryCount"] = new ShortcutDefinition
                {
                    Name = "InventoryCount",
                    DisplayName = "Start Inventory Count",
                    KeyCombination = "Ctrl+Alt+I",
                    Description = "Start an inventory count session",
                    Category = "Inventory",
                    IsActive = true
                },
                ["PrintReceipt"] = new ShortcutDefinition
                {
                    Name = "PrintReceipt",
                    DisplayName = "Print Receipt",
                    KeyCombination = "Ctrl+P",
                    Description = "Print the current receipt",
                    Category = "Printing",
                    IsActive = true
                },
                ["SaveTransaction"] = new ShortcutDefinition
                {
                    Name = "SaveTransaction",
                    DisplayName = "Save Transaction",
                    KeyCombination = "Ctrl+S",
                    Description = "Save the current transaction",
                    Category = "Transactions",
                    IsActive = true
                },
                ["Help"] = new ShortcutDefinition
                {
                    Name = "Help",
                    DisplayName = "Show Help",
                    KeyCombination = "F1",
                    Description = "Show context-sensitive help",
                    Category = "General",
                    IsActive = true
                }
            };
        }
        
        /// <summary>
        /// Loads shortcuts from the saved file or creates defaults if not found
        /// </summary>
        private void LoadShortcuts()
        {
            try
            {
                if (File.Exists(_shortcutsFilePath))
                {
                    var json = File.ReadAllText(_shortcutsFilePath);
                    var loadedShortcuts = JsonSerializer.Deserialize<Dictionary<string, ShortcutDefinition>>(json);
                    
                    if (loadedShortcuts != null && loadedShortcuts.Count > 0)
                    {
                        _shortcuts = loadedShortcuts;
                        _logger.LogInformation("Loaded {Count} keyboard shortcuts", _shortcuts.Count);
                        return;
                    }
                }
                
                // No shortcuts found, create defaults
                _shortcuts = GetDefaultShortcuts();
                _logger.LogInformation("Created default keyboard shortcuts");
                
                // Save the default shortcuts
                File.WriteAllText(_shortcutsFilePath, 
                    JsonSerializer.Serialize(_shortcuts, new JsonSerializerOptions { WriteIndented = true }));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading keyboard shortcuts");
                _shortcuts = GetDefaultShortcuts();
            }
        }
        
        /// <summary>
        /// Saves shortcuts to the file
        /// </summary>
        private async Task SaveShortcutsAsync()
        {
            try
            {
                Directory.CreateDirectory(Path.GetDirectoryName(_shortcutsFilePath));
                var json = JsonSerializer.Serialize(_shortcuts, new JsonSerializerOptions
                {
                    WriteIndented = true
                });
                
                await File.WriteAllTextAsync(_shortcutsFilePath, json);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error saving keyboard shortcuts");
            }
        }
    }
}
