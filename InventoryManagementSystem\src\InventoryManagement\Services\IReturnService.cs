using InventoryManagement.Models;

namespace InventoryManagement.Services
{
    /// <summary>
    /// Interface for return and refund processing services
    /// </summary>
    public interface IReturnService
    {
        /// <summary>
        /// Process a return transaction
        /// </summary>
        /// <param name="returnRequest">Return request details</param>
        /// <param name="userId">User processing the return</param>
        /// <returns>Return processing result</returns>
        Task<ReturnProcessingResult> ProcessReturnAsync(ReturnRequest returnRequest, int userId);

        /// <summary>
        /// Get return transaction by ID
        /// </summary>
        /// <param name="returnId">Return transaction ID</param>
        /// <returns>Return transaction or null</returns>
        Task<ReturnTransaction?> GetReturnByIdAsync(int returnId);

        /// <summary>
        /// Get return transaction by return number
        /// </summary>
        /// <param name="returnNumber">Return number</param>
        /// <returns>Return transaction or null</returns>
        Task<ReturnTransaction?> GetReturnByNumberAsync(string returnNumber);

        /// <summary>
        /// Get all returns for a specific transaction
        /// </summary>
        /// <param name="originalTransactionId">Original transaction ID</param>
        /// <returns>List of return transactions</returns>
        Task<List<ReturnTransaction>> GetReturnsByTransactionAsync(int originalTransactionId);

        /// <summary>
        /// Get returns by date range
        /// </summary>
        /// <param name="fromDate">Start date</param>
        /// <param name="toDate">End date</param>
        /// <param name="status">Return status filter (optional)</param>
        /// <returns>List of return transactions</returns>
        Task<List<ReturnTransaction>> GetReturnsByDateRangeAsync(DateTime fromDate, DateTime toDate, ReturnStatus? status = null);

        /// <summary>
        /// Get returns requiring approval
        /// </summary>
        /// <returns>List of returns pending approval</returns>
        Task<List<ReturnTransaction>> GetReturnsRequiringApprovalAsync();

        /// <summary>
        /// Approve a return transaction
        /// </summary>
        /// <param name="returnId">Return transaction ID</param>
        /// <param name="approverId">Manager approving the return</param>
        /// <param name="notes">Approval notes</param>
        /// <returns>True if approved successfully</returns>
        Task<bool> ApproveReturnAsync(int returnId, int approverId, string notes = "");

        /// <summary>
        /// Reject a return transaction
        /// </summary>
        /// <param name="returnId">Return transaction ID</param>
        /// <param name="rejectedById">Manager rejecting the return</param>
        /// <param name="reason">Rejection reason</param>
        /// <returns>True if rejected successfully</returns>
        Task<bool> RejectReturnAsync(int returnId, int rejectedById, string reason);

        /// <summary>
        /// Complete a return transaction (process refund)
        /// </summary>
        /// <param name="returnId">Return transaction ID</param>
        /// <param name="userId">User completing the return</param>
        /// <returns>Return completion result</returns>
        Task<ReturnCompletionResult> CompleteReturnAsync(int returnId, int userId);

        /// <summary>
        /// Restock returned items
        /// </summary>
        /// <param name="returnId">Return transaction ID</param>
        /// <param name="userId">User performing restock</param>
        /// <returns>True if restocked successfully</returns>
        Task<bool> RestockReturnedItemsAsync(int returnId, int userId);

        /// <summary>
        /// Validate if a transaction can be returned
        /// </summary>
        /// <param name="transactionId">Original transaction ID</param>
        /// <param name="returnItems">Items to be returned</param>
        /// <returns>Validation result</returns>
        Task<ReturnValidationResult> ValidateReturnAsync(int transactionId, List<ReturnItemRequest> returnItems);

        /// <summary>
        /// Calculate refund amount for return items
        /// </summary>
        /// <param name="returnItems">Items to be returned</param>
        /// <param name="originalTransaction">Original transaction</param>
        /// <returns>Calculated refund amount</returns>
        Task<decimal> CalculateRefundAmountAsync(List<ReturnItemRequest> returnItems, Transaction originalTransaction);

        /// <summary>
        /// Get return statistics for a date range
        /// </summary>
        /// <param name="fromDate">Start date</param>
        /// <param name="toDate">End date</param>
        /// <returns>Return statistics</returns>
        Task<ReturnStatistics> GetReturnStatisticsAsync(DateTime fromDate, DateTime toDate);

        /// <summary>
        /// Check if return is within allowed time period
        /// </summary>
        /// <param name="transactionDate">Original transaction date</param>
        /// <param name="allowedDays">Allowed return period in days</param>
        /// <returns>True if within allowed period</returns>
        bool IsWithinReturnPeriod(DateTime transactionDate, int allowedDays = 30);

        /// <summary>
        /// Generate return receipt
        /// </summary>
        /// <param name="returnId">Return transaction ID</param>
        /// <returns>Return receipt content</returns>
        Task<string> GenerateReturnReceiptAsync(int returnId);
    }

    /// <summary>
    /// Return request model
    /// </summary>
    public class ReturnRequest
    {
        public int OriginalTransactionId { get; set; }
        public int? CustomerId { get; set; }
        public string ReturnReason { get; set; } = string.Empty;
        public string Notes { get; set; } = string.Empty;
        public string RefundMethod { get; set; } = RefundMethods.Cash;
        public List<ReturnItemRequest> Items { get; set; } = new List<ReturnItemRequest>();
        public bool RequiresApproval { get; set; } = false;
    }

    /// <summary>
    /// Return item request model
    /// </summary>
    public class ReturnItemRequest
    {
        public int ItemId { get; set; }
        public int Quantity { get; set; }
        public decimal OriginalPrice { get; set; }
        public decimal RefundPricePerUnit { get; set; }
        public string ItemCondition { get; set; } = ItemConditions.Good;
        public bool CanRestock { get; set; } = true;
        public string ItemReturnReason { get; set; } = string.Empty;
        public int? RestockLocationId { get; set; }
    }

    /// <summary>
    /// Return processing result
    /// </summary>
    public class ReturnProcessingResult
    {
        public bool IsSuccessful { get; set; }
        public string Message { get; set; } = string.Empty;
        public ReturnTransaction? ReturnTransaction { get; set; }
        public string ReturnNumber { get; set; } = string.Empty;
        public decimal RefundAmount { get; set; }
        public bool RequiresApproval { get; set; }
        public List<string> Warnings { get; set; } = new List<string>();
        public List<string> Errors { get; set; } = new List<string>();
    }

    /// <summary>
    /// Return completion result
    /// </summary>
    public class ReturnCompletionResult
    {
        public bool IsSuccessful { get; set; }
        public string Message { get; set; } = string.Empty;
        public decimal RefundAmount { get; set; }
        public string RefundMethod { get; set; } = string.Empty;
        public string RefundReceiptNumber { get; set; } = string.Empty;
        public bool ItemsRestocked { get; set; }
        public List<string> Errors { get; set; } = new List<string>();
    }

    /// <summary>
    /// Return validation result
    /// </summary>
    public class ReturnValidationResult
    {
        public bool IsValid { get; set; }
        public List<string> Errors { get; set; } = new List<string>();
        public List<string> Warnings { get; set; } = new List<string>();
        public bool IsWithinReturnPeriod { get; set; }
        public bool RequiresManagerApproval { get; set; }
        public decimal MaxRefundAmount { get; set; }
    }

    /// <summary>
    /// Return statistics
    /// </summary>
    public class ReturnStatistics
    {
        public int TotalReturns { get; set; }
        public decimal TotalRefundAmount { get; set; }
        public int PendingReturns { get; set; }
        public int ApprovedReturns { get; set; }
        public int RejectedReturns { get; set; }
        public int CompletedReturns { get; set; }
        public decimal AverageRefundAmount { get; set; }
        public Dictionary<string, int> ReturnReasons { get; set; } = new Dictionary<string, int>();
        public Dictionary<string, decimal> RefundMethods { get; set; } = new Dictionary<string, decimal>();
    }
}
