using System;

namespace InventoryManagement.Services.Offline
{
    /// <summary>
    /// Event arguments for connection status change events
    /// </summary>
    public class ConnectionStatusChangedEventArgs : EventArgs
    {
        /// <summary>
        /// Gets or sets a value indicating whether the application is online
        /// </summary>
        public bool IsOnline { get; set; }
        
        /// <summary>
        /// Gets or sets the timestamp when the connection status changed
        /// </summary>
        public DateTime Timestamp { get; set; }
        
        /// <summary>
        /// Gets or sets additional information about the connection status
        /// </summary>
        public string AdditionalInfo { get; set; }
        
        /// <summary>
        /// Creates a new instance of ConnectionStatusChangedEventArgs
        /// </summary>
        /// <param name="isOnline">Whether the application is online</param>
        /// <param name="additionalInfo">Additional information</param>
        public ConnectionStatusChangedEventArgs(bool isOnline, string additionalInfo = null)
        {
            IsOnline = isOnline;
            Timestamp = DateTime.Now;
            AdditionalInfo = additionalInfo;
        }
    }
}
