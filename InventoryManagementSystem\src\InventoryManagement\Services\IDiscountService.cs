using InventoryManagement.Models;

namespace InventoryManagement.Services
{
    /// <summary>
    /// Interface for discount management and calculation services
    /// </summary>
    public interface IDiscountService
    {
        /// <summary>
        /// Get all active discounts
        /// </summary>
        /// <returns>List of active discounts</returns>
        Task<List<Discount>> GetActiveDiscountsAsync();

        /// <summary>
        /// Get discount by ID
        /// </summary>
        /// <param name="discountId">Discount ID</param>
        /// <returns>Discount or null if not found</returns>
        Task<Discount?> GetDiscountByIdAsync(int discountId);

        /// <summary>
        /// Get discount by code
        /// </summary>
        /// <param name="discountCode">Discount code</param>
        /// <returns>Discount or null if not found</returns>
        Task<Discount?> GetDiscountByCodeAsync(string discountCode);

        /// <summary>
        /// Get applicable discounts for a transaction
        /// </summary>
        /// <param name="transactionItems">Items in the transaction</param>
        /// <param name="customerType">Customer type</param>
        /// <param name="totalAmount">Total transaction amount</param>
        /// <returns>List of applicable discounts</returns>
        Task<List<Discount>> GetApplicableDiscountsAsync(List<TransactionItem> transactionItems, string customerType, decimal totalAmount);

        /// <summary>
        /// Calculate discount amount for a transaction
        /// </summary>
        /// <param name="discount">Discount to apply</param>
        /// <param name="transactionItems">Items in the transaction</param>
        /// <param name="totalAmount">Total transaction amount</param>
        /// <returns>Discount calculation result</returns>
        Task<DiscountCalculationResult> CalculateDiscountAsync(Discount discount, List<TransactionItem> transactionItems, decimal totalAmount);

        /// <summary>
        /// Apply discount to a transaction
        /// </summary>
        /// <param name="transactionId">Transaction ID</param>
        /// <param name="discountId">Discount ID</param>
        /// <param name="appliedByUserId">User applying the discount</param>
        /// <param name="reason">Reason for applying discount</param>
        /// <param name="itemId">Specific item ID (for item-level discounts)</param>
        /// <returns>Discount application result</returns>
        Task<DiscountApplicationResult> ApplyDiscountAsync(int transactionId, int discountId, int appliedByUserId, string reason = "", int? itemId = null);

        /// <summary>
        /// Remove discount from a transaction
        /// </summary>
        /// <param name="discountApplicationId">Discount application ID</param>
        /// <param name="userId">User removing the discount</param>
        /// <returns>True if removed successfully</returns>
        Task<bool> RemoveDiscountAsync(int discountApplicationId, int userId);

        /// <summary>
        /// Create a new discount
        /// </summary>
        /// <param name="discount">Discount to create</param>
        /// <param name="userId">User creating the discount</param>
        /// <returns>Created discount</returns>
        Task<Discount> CreateDiscountAsync(Discount discount, int userId);

        /// <summary>
        /// Update an existing discount
        /// </summary>
        /// <param name="discount">Discount to update</param>
        /// <param name="userId">User updating the discount</param>
        /// <returns>Updated discount</returns>
        Task<Discount> UpdateDiscountAsync(Discount discount, int userId);

        /// <summary>
        /// Delete a discount
        /// </summary>
        /// <param name="discountId">Discount ID</param>
        /// <param name="userId">User deleting the discount</param>
        /// <returns>True if deleted successfully</returns>
        Task<bool> DeleteDiscountAsync(int discountId, int userId);

        /// <summary>
        /// Validate discount application
        /// </summary>
        /// <param name="discount">Discount to validate</param>
        /// <param name="transactionItems">Transaction items</param>
        /// <param name="customerType">Customer type</param>
        /// <param name="userId">User applying the discount</param>
        /// <returns>Validation result</returns>
        Task<DiscountValidationResult> ValidateDiscountApplicationAsync(Discount discount, List<TransactionItem> transactionItems, string customerType, int userId);

        /// <summary>
        /// Get discount usage statistics
        /// </summary>
        /// <param name="discountId">Discount ID</param>
        /// <param name="fromDate">Start date</param>
        /// <param name="toDate">End date</param>
        /// <returns>Usage statistics</returns>
        Task<DiscountUsageStatistics> GetDiscountUsageStatisticsAsync(int discountId, DateTime fromDate, DateTime toDate);

        /// <summary>
        /// Get discount applications for a transaction
        /// </summary>
        /// <param name="transactionId">Transaction ID</param>
        /// <returns>List of discount applications</returns>
        Task<List<DiscountApplication>> GetTransactionDiscountsAsync(int transactionId);

        /// <summary>
        /// Check if user has permission to apply discount
        /// </summary>
        /// <param name="discount">Discount to check</param>
        /// <param name="userRole">User role</param>
        /// <returns>True if user can apply discount</returns>
        bool CanUserApplyDiscount(Discount discount, string userRole);

        /// <summary>
        /// Get discount recommendations for a transaction
        /// </summary>
        /// <param name="transactionItems">Transaction items</param>
        /// <param name="customerType">Customer type</param>
        /// <param name="totalAmount">Total amount</param>
        /// <returns>List of recommended discounts</returns>
        Task<List<DiscountRecommendation>> GetDiscountRecommendationsAsync(List<TransactionItem> transactionItems, string customerType, decimal totalAmount);
    }

    /// <summary>
    /// Discount calculation result
    /// </summary>
    public class DiscountCalculationResult
    {
        public bool IsValid { get; set; }
        public decimal DiscountAmount { get; set; }
        public decimal FinalAmount { get; set; }
        public string Message { get; set; } = string.Empty;
        public List<string> Warnings { get; set; } = new List<string>();
        public List<string> Errors { get; set; } = new List<string>();
        public Dictionary<int, decimal> ItemDiscounts { get; set; } = new Dictionary<int, decimal>(); // ItemId -> Discount Amount
    }

    /// <summary>
    /// Discount application result
    /// </summary>
    public class DiscountApplicationResult
    {
        public bool IsSuccessful { get; set; }
        public string Message { get; set; } = string.Empty;
        public DiscountApplication? DiscountApplication { get; set; }
        public decimal DiscountAmount { get; set; }
        public bool RequiredApproval { get; set; }
        public List<string> Errors { get; set; } = new List<string>();
    }

    /// <summary>
    /// Discount validation result
    /// </summary>
    public class DiscountValidationResult
    {
        public bool IsValid { get; set; }
        public bool RequiresApproval { get; set; }
        public List<string> Errors { get; set; } = new List<string>();
        public List<string> Warnings { get; set; } = new List<string>();
        public decimal MaxDiscountAmount { get; set; }
    }

    /// <summary>
    /// Discount usage statistics
    /// </summary>
    public class DiscountUsageStatistics
    {
        public int TotalUsages { get; set; }
        public decimal TotalDiscountAmount { get; set; }
        public decimal AverageDiscountAmount { get; set; }
        public int UniqueCustomers { get; set; }
        public Dictionary<string, int> UsageByDay { get; set; } = new Dictionary<string, int>();
        public Dictionary<string, decimal> DiscountByCategory { get; set; } = new Dictionary<string, decimal>();
    }

    /// <summary>
    /// Discount recommendation
    /// </summary>
    public class DiscountRecommendation
    {
        public Discount Discount { get; set; } = new Discount();
        public decimal PotentialSavings { get; set; }
        public string RecommendationReason { get; set; } = string.Empty;
        public int Priority { get; set; }
        public bool RequiresApproval { get; set; }
    }
}
