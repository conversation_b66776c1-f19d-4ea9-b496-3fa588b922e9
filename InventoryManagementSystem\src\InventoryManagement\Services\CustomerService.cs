using InventoryManagement.DataAccess.Repositories;
using InventoryManagement.Models;
using Microsoft.Extensions.Logging;

namespace InventoryManagement.Services
{
    /// <summary>
    /// Service for customer management operations
    /// </summary>
    public class CustomerService : ICustomerService
    {
        private readonly ICustomerRepository _customerRepository;
        private readonly ILogger<CustomerService> _logger;
        private readonly IAuditService _auditService;

        public CustomerService(
            ICustomerRepository customerRepository,
            ILogger<CustomerService> logger,
            IAuditService auditService)
        {
            _customerRepository = customerRepository;
            _logger = logger;
            _auditService = auditService;
        }

        public async Task<Customer?> GetCustomerByIdAsync(int id)
        {
            try
            {
                return await _customerRepository.GetByIdAsync(id);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting customer by ID {CustomerId}", id);
                throw;
            }
        }

        public async Task<Customer?> GetCustomerByNumberAsync(string customerNumber)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(customerNumber))
                    return null;

                return await _customerRepository.GetByCustomerNumberAsync(customerNumber);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting customer by number {CustomerNumber}", customerNumber);
                throw;
            }
        }

        public async Task<Customer?> GetCustomerByPhoneAsync(string phoneNumber)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(phoneNumber))
                    return null;

                return await _customerRepository.GetByPhoneAsync(phoneNumber);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting customer by phone {PhoneNumber}", phoneNumber);
                throw;
            }
        }

        public async Task<Customer?> GetCustomerByEmailAsync(string email)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(email))
                    return null;

                return await _customerRepository.GetByEmailAsync(email);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting customer by email {Email}", email);
                throw;
            }
        }

        public async Task<List<Customer>> GetAllCustomersAsync()
        {
            try
            {
                return await _customerRepository.GetAllAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting all customers");
                throw;
            }
        }

        public async Task<List<Customer>> SearchCustomersAsync(string searchTerm)
        {
            try
            {
                return await _customerRepository.SearchAsync(searchTerm);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error searching customers with term {SearchTerm}", searchTerm);
                throw;
            }
        }

        public async Task<List<Customer>> GetCustomersByTypeAsync(string customerType)
        {
            try
            {
                return await _customerRepository.GetByTypeAsync(customerType);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting customers by type {CustomerType}", customerType);
                throw;
            }
        }

        public async Task<List<Customer>> GetTopCustomersAsync(int count = 10)
        {
            try
            {
                return await _customerRepository.GetTopCustomersAsync(count);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting top {Count} customers", count);
                throw;
            }
        }

        public async Task<Customer> CreateCustomerAsync(Customer customer, int userId)
        {
            try
            {
                // Validate customer data
                await ValidateCustomerAsync(customer);

                // Check for duplicates
                if (!string.IsNullOrEmpty(customer.Email))
                {
                    var existingByEmail = await _customerRepository.GetByEmailAsync(customer.Email);
                    if (existingByEmail != null)
                        throw new InvalidOperationException($"Customer with email {customer.Email} already exists");
                }

                if (!string.IsNullOrEmpty(customer.PhoneNumber))
                {
                    var existingByPhone = await _customerRepository.GetByPhoneAsync(customer.PhoneNumber);
                    if (existingByPhone != null)
                        throw new InvalidOperationException($"Customer with phone {customer.PhoneNumber} already exists");
                }

                var createdCustomer = await _customerRepository.CreateAsync(customer);

                // Log the creation
                await _auditService.LogEventAsync(
                    "Customer",
                    $"Created customer: {customer.Name} ({customer.CustomerNumber})",
                    userId);

                _logger.LogInformation("Created customer {CustomerName} with ID {CustomerId}", customer.Name, createdCustomer.Id);
                return createdCustomer;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating customer {CustomerName}", customer.Name);
                throw;
            }
        }

        public async Task<Customer> UpdateCustomerAsync(Customer customer, int userId)
        {
            try
            {
                // Validate customer data
                await ValidateCustomerAsync(customer);

                // Check if customer exists
                var existingCustomer = await _customerRepository.GetByIdAsync(customer.Id);
                if (existingCustomer == null)
                    throw new InvalidOperationException($"Customer with ID {customer.Id} not found");

                // Check for duplicates (excluding current customer)
                if (!string.IsNullOrEmpty(customer.Email))
                {
                    var existingByEmail = await _customerRepository.GetByEmailAsync(customer.Email);
                    if (existingByEmail != null && existingByEmail.Id != customer.Id)
                        throw new InvalidOperationException($"Another customer with email {customer.Email} already exists");
                }

                if (!string.IsNullOrEmpty(customer.PhoneNumber))
                {
                    var existingByPhone = await _customerRepository.GetByPhoneAsync(customer.PhoneNumber);
                    if (existingByPhone != null && existingByPhone.Id != customer.Id)
                        throw new InvalidOperationException($"Another customer with phone {customer.PhoneNumber} already exists");
                }

                var updatedCustomer = await _customerRepository.UpdateAsync(customer);

                // Log the update
                await _auditService.LogEventAsync(
                    "Customer",
                    $"Updated customer: {customer.Name} ({customer.CustomerNumber})",
                    userId);

                _logger.LogInformation("Updated customer {CustomerName} with ID {CustomerId}", customer.Name, customer.Id);
                return updatedCustomer;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating customer {CustomerId}", customer.Id);
                throw;
            }
        }

        public async Task<bool> DeleteCustomerAsync(int customerId, int userId)
        {
            try
            {
                var customer = await _customerRepository.GetByIdAsync(customerId);
                if (customer == null)
                    return false;

                // Check if customer has pending transactions or credit
                if (customer.CurrentCreditBalance > 0)
                    throw new InvalidOperationException("Cannot delete customer with outstanding credit balance");

                var result = await _customerRepository.DeleteAsync(customerId);

                if (result)
                {
                    // Log the deletion
                    await _auditService.LogEventAsync(
                        "Customer",
                        $"Deleted customer: {customer.Name} ({customer.CustomerNumber})",
                        userId);

                    _logger.LogInformation("Deleted customer {CustomerName} with ID {CustomerId}", customer.Name, customerId);
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting customer {CustomerId}", customerId);
                throw;
            }
        }

        public async Task<List<Transaction>> GetCustomerTransactionHistoryAsync(int customerId, DateTime? fromDate = null, DateTime? toDate = null)
        {
            try
            {
                return await _customerRepository.GetCustomerTransactionsAsync(customerId, fromDate, toDate);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting transaction history for customer {CustomerId}", customerId);
                throw;
            }
        }

        public async Task<CustomerSummary> GetCustomerSummaryAsync(int customerId)
        {
            try
            {
                var customer = await _customerRepository.GetByIdAsync(customerId);
                if (customer == null)
                    throw new InvalidOperationException($"Customer with ID {customerId} not found");

                var transactions = await _customerRepository.GetCustomerTransactionsAsync(customerId);
                var totalPurchases = await _customerRepository.GetCustomerTotalPurchasesAsync(customerId);

                return new CustomerSummary
                {
                    Customer = customer,
                    TotalTransactions = transactions.Count,
                    TotalPurchases = totalPurchases,
                    LastTransactionDate = transactions.FirstOrDefault()?.TransactionDate,
                    AverageTransactionValue = transactions.Count > 0 ? totalPurchases / transactions.Count : 0,
                    PendingCreditAmount = customer.CurrentCreditBalance,
                    HasOverduePayments = customer.HasOverduePayments,
                    CreditRating = customer.CreditRating
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting customer summary for {CustomerId}", customerId);
                throw;
            }
        }

        public async Task<bool> ValidateCustomerAsync(Customer customer)
        {
            var errors = new List<string>();

            // Required field validation
            if (string.IsNullOrWhiteSpace(customer.Name))
                errors.Add("Customer name is required");

            // Email validation
            if (!string.IsNullOrEmpty(customer.Email) && !IsValidEmail(customer.Email))
                errors.Add("Invalid email format");

            // Phone validation
            if (!string.IsNullOrEmpty(customer.PhoneNumber) && !IsValidPhoneNumber(customer.PhoneNumber))
                errors.Add("Invalid phone number format");

            // Credit limit validation
            if (customer.CreditLimit < 0)
                errors.Add("Credit limit cannot be negative");

            if (customer.CurrentCreditBalance < 0)
                errors.Add("Current credit balance cannot be negative");

            if (errors.Any())
                throw new ArgumentException($"Customer validation failed: {string.Join(", ", errors)}");

            return true;
        }

        public async Task UpdateCustomerPurchaseTotalsAsync(int customerId)
        {
            try
            {
                await _customerRepository.UpdateCustomerTotalPurchasesAsync(customerId);
                _logger.LogInformation("Updated purchase totals for customer {CustomerId}", customerId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating purchase totals for customer {CustomerId}", customerId);
                throw;
            }
        }

        private bool IsValidEmail(string email)
        {
            try
            {
                var addr = new System.Net.Mail.MailAddress(email);
                return addr.Address == email;
            }
            catch
            {
                return false;
            }
        }

        private bool IsValidPhoneNumber(string phoneNumber)
        {
            // Simple phone number validation - adjust based on your requirements
            if (string.IsNullOrWhiteSpace(phoneNumber))
                return false;

            // Remove common formatting characters
            var cleanPhone = phoneNumber.Replace(" ", "").Replace("-", "").Replace("(", "").Replace(")", "").Replace("+", "");
            
            // Check if it's all digits and reasonable length
            return cleanPhone.All(char.IsDigit) && cleanPhone.Length >= 7 && cleanPhone.Length <= 15;
        }
    }

    /// <summary>
    /// Customer summary information
    /// </summary>
    public class CustomerSummary
    {
        public Customer Customer { get; set; } = new Customer();
        public int TotalTransactions { get; set; }
        public decimal TotalPurchases { get; set; }
        public DateTime? LastTransactionDate { get; set; }
        public decimal AverageTransactionValue { get; set; }
        public decimal PendingCreditAmount { get; set; }
        public bool HasOverduePayments { get; set; }
        public int CreditRating { get; set; }
    }
}
