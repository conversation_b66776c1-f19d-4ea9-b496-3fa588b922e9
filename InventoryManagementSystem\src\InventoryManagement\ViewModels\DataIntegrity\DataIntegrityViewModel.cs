using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Input;
using InventoryManagement.Infrastructure.Commands;
using InventoryManagement.Models.Database;
using InventoryManagement.Services.Database.Monitoring;
using InventoryManagement.Services.Validation;
using Microsoft.Extensions.Logging;

namespace InventoryManagement.ViewModels.DataIntegrity
{
    /// <summary>
    /// ViewModel for managing data integrity validation
    /// </summary>
    public class DataIntegrityViewModel : ViewModelBase
    {
        private readonly IDataIntegrityService _dataIntegrityService;
        private readonly IDatabaseHealthMonitor _databaseHealthMonitor;
        private readonly ILogger<DataIntegrityViewModel> _logger;
        
        private bool _isRunningCheck;
        private string _statusMessage;
        private DataIntegrityReport _currentReport;
        private DatabaseHealthReport _healthReport;
        private ObservableCollection<DataIntegrityIssueViewModel> _issues;
        private DataIntegrityIssueViewModel _selectedIssue;
        
        #region Properties
        
        /// <summary>
        /// Whether a validation check is currently running
        /// </summary>
        public bool IsRunningCheck
        {
            get => _isRunningCheck;
            set
            {
                if (_isRunningCheck != value)
                {
                    _isRunningCheck = value;
                    OnPropertyChanged(nameof(IsRunningCheck));
                    OnPropertyChanged(nameof(CanRunCheck));
                }
            }
        }
        
        /// <summary>
        /// Whether a validation check can be run
        /// </summary>
        public bool CanRunCheck => !IsRunningCheck;
        
        /// <summary>
        /// Status message for the current operation
        /// </summary>
        public string StatusMessage
        {
            get => _statusMessage;
            set
            {
                if (_statusMessage != value)
                {
                    _statusMessage = value;
                    OnPropertyChanged(nameof(StatusMessage));
                }
            }
        }
        
        /// <summary>
        /// Current data integrity report
        /// </summary>
        public DataIntegrityReport CurrentReport
        {
            get => _currentReport;
            set
            {
                if (_currentReport != value)
                {
                    _currentReport = value;
                    OnPropertyChanged(nameof(CurrentReport));
                    OnPropertyChanged(nameof(HasReport));
                }
            }
        }
        
        /// <summary>
        /// Current database health report
        /// </summary>
        public DatabaseHealthReport HealthReport
        {
            get => _healthReport;
            set
            {
                if (_healthReport != value)
                {
                    _healthReport = value;
                    OnPropertyChanged(nameof(HealthReport));
                    OnPropertyChanged(nameof(HasHealthReport));
                }
            }
        }
        
        /// <summary>
        /// Whether a report is available
        /// </summary>
        public bool HasReport => CurrentReport != null;
        
        /// <summary>
        /// Whether a health report is available
        /// </summary>
        public bool HasHealthReport => HealthReport != null;
        
        /// <summary>
        /// Issues found in the validation check
        /// </summary>
        public ObservableCollection<DataIntegrityIssueViewModel> Issues
        {
            get => _issues;
            set
            {
                if (_issues != value)
                {
                    _issues = value;
                    OnPropertyChanged(nameof(Issues));
                }
            }
        }
        
        /// <summary>
        /// Currently selected issue
        /// </summary>
        public DataIntegrityIssueViewModel SelectedIssue
        {
            get => _selectedIssue;
            set
            {
                if (_selectedIssue != value)
                {
                    _selectedIssue = value;
                    OnPropertyChanged(nameof(SelectedIssue));
                    OnPropertyChanged(nameof(CanViewDetails));
                    OnPropertyChanged(nameof(CanApplyFix));
                }
            }
        }
        
        /// <summary>
        /// Whether issue details can be viewed
        /// </summary>
        public bool CanViewDetails => SelectedIssue != null;
        
        /// <summary>
        /// Whether an automatic fix can be applied to the selected issue
        /// </summary>
        public bool CanApplyFix => SelectedIssue?.CanAutoFix == true;
        
        /// <summary>
        /// Count of critical issues
        /// </summary>
        public int CriticalIssueCount => CurrentReport?.CriticalIssueCount ?? 0;
        
        /// <summary>
        /// Count of error issues
        /// </summary>
        public int ErrorIssueCount => CurrentReport?.ErrorIssueCount ?? 0;
        
        /// <summary>
        /// Count of warning issues
        /// </summary>
        public int WarningIssueCount => CurrentReport?.WarningIssueCount ?? 0;
        
        #endregion
        
        #region Commands
        
        /// <summary>
        /// Command to run a data integrity check
        /// </summary>
        public ICommand RunCheckCommand { get; }
        
        /// <summary>
        /// Command to run a database health check
        /// </summary>
        public ICommand RunDatabaseHealthCheckCommand { get; }
        
        /// <summary>
        /// Command to run database maintenance
        /// </summary>
        public ICommand RunMaintenanceCommand { get; }
        
        /// <summary>
        /// Command to view details of the selected issue
        /// </summary>
        public ICommand ViewDetailsCommand { get; }
        
        /// <summary>
        /// Command to apply an automatic fix to the selected issue
        /// </summary>
        public ICommand ApplyFixCommand { get; }
        
        /// <summary>
        /// Command to export the report
        /// </summary>
        public ICommand ExportReportCommand { get; }
        
        #endregion
        
        /// <summary>
        /// Creates a new instance of the DataIntegrityViewModel
        /// </summary>
        public DataIntegrityViewModel(
            IDataIntegrityService dataIntegrityService,
            IDatabaseHealthMonitor databaseHealthMonitor,
            ILogger<DataIntegrityViewModel> logger)
        {
            _dataIntegrityService = dataIntegrityService ?? throw new ArgumentNullException(nameof(dataIntegrityService));
            _databaseHealthMonitor = databaseHealthMonitor ?? throw new ArgumentNullException(nameof(databaseHealthMonitor));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            
            Issues = new ObservableCollection<DataIntegrityIssueViewModel>();
            
            RunCheckCommand = new AsyncRelayCommand(RunDataIntegrityCheckAsync, _ => CanRunCheck);
            RunDatabaseHealthCheckCommand = new AsyncRelayCommand(RunDatabaseHealthCheckAsync, _ => CanRunCheck);
            RunMaintenanceCommand = new AsyncRelayCommand(RunDatabaseMaintenanceAsync, _ => CanRunCheck);
            ViewDetailsCommand = new RelayCommand(_ => ShowIssueDetails(), _ => CanViewDetails);
            ApplyFixCommand = new AsyncRelayCommand(ApplyFixAsync, _ => CanApplyFix);
            ExportReportCommand = new AsyncRelayCommand(ExportReportAsync, _ => HasReport);
            
            StatusMessage = "Ready to run data integrity check";
        }
        
        /// <summary>
        /// Runs a data integrity check
        /// </summary>
        private async Task RunDataIntegrityCheckAsync(object parameter)
        {
            try
            {
                IsRunningCheck = true;
                StatusMessage = "Running data integrity check...";
                
                // Clear previous results
                Issues.Clear();
                CurrentReport = null;
                
                // Run the integrity check
                var report = await _dataIntegrityService.VerifyDataIntegrityAsync();
                CurrentReport = report;
                
                // Create issue view models
                foreach (var issue in report.Issues.OrderByDescending(i => (int)i.Severity))
                {
                    Issues.Add(new DataIntegrityIssueViewModel(issue));
                }
                
                OnPropertyChanged(nameof(CriticalIssueCount));
                OnPropertyChanged(nameof(ErrorIssueCount));
                OnPropertyChanged(nameof(WarningIssueCount));
                
                if (report.Success)
                {
                    StatusMessage = $"Check completed. Found {report.TotalIssueCount} issues " +
                                    $"({report.CriticalIssueCount} critical, {report.ErrorIssueCount} errors, " +
                                    $"{report.WarningIssueCount} warnings).";
                }
                else
                {
                    StatusMessage = $"Check failed: {report.ErrorMessage}";
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error running data integrity check");
                StatusMessage = $"Error: {ex.Message}";
            }
            finally
            {
                IsRunningCheck = false;
            }
        }
        
        /// <summary>
        /// Runs a database health check
        /// </summary>
        private async Task RunDatabaseHealthCheckAsync(object parameter)
        {
            try
            {
                IsRunningCheck = true;
                StatusMessage = "Running database health check...";
                
                // Run the health check
                HealthReport = await _databaseHealthMonitor.GetDatabaseHealthReportAsync();
                
                StatusMessage = $"Database health check completed. Health score: {HealthReport.HealthScore}/100 ({HealthReport.HealthStatus})";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error running database health check");
                StatusMessage = $"Error: {ex.Message}";
            }
            finally
            {
                IsRunningCheck = false;
            }
        }
        
        /// <summary>
        /// Runs database maintenance
        /// </summary>
        private async Task RunDatabaseMaintenanceAsync(object parameter)
        {
            try
            {
                IsRunningCheck = true;
                StatusMessage = "Running database maintenance...";
                
                // Run maintenance
                var result = await _databaseHealthMonitor.PerformMaintenanceAsync();
                
                if (result.Success)
                {
                    var operations = string.Join(", ", result.Operations);
                    StatusMessage = $"Maintenance completed successfully in {result.Duration.TotalSeconds:F1}s. " +
                                    $"Operations performed: {operations}";
                }
                else
                {
                    StatusMessage = $"Maintenance failed: {result.ErrorMessage}";
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error running database maintenance");
                StatusMessage = $"Error: {ex.Message}";
            }
            finally
            {
                IsRunningCheck = false;
            }
        }
        
        /// <summary>
        /// Shows details of the selected issue
        /// </summary>
        private void ShowIssueDetails()
        {
            if (SelectedIssue == null)
            {
                return;
            }
            
            // In a real implementation, this would show a dialog or navigate to a details view
            // For now, we'll just log the action
            _logger.LogInformation("Showing details for issue: {IssueMessage}", SelectedIssue.Message);
        }
        
        /// <summary>
        /// Applies an automatic fix to the selected issue
        /// </summary>
        private async Task ApplyFixAsync(object parameter)
        {
            if (SelectedIssue == null || !SelectedIssue.CanAutoFix)
            {
                return;
            }
            
            try
            {
                IsRunningCheck = true;
                StatusMessage = $"Applying fix for issue: {SelectedIssue.Message}...";
                
                // In a real implementation, this would apply the fix using the RemediationQuery
                // or other automated fix logic
                
                // Simulate a successful fix
                await Task.Delay(1000);
                
                SelectedIssue.IsResolved = true;
                StatusMessage = $"Fix applied successfully to issue: {SelectedIssue.Message}";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error applying fix to issue");
                StatusMessage = $"Error applying fix: {ex.Message}";
            }
            finally
            {
                IsRunningCheck = false;
            }
        }
        
        /// <summary>
        /// Exports the current report to a file
        /// </summary>
        private async Task ExportReportAsync(object parameter)
        {
            if (CurrentReport == null)
            {
                return;
            }
            
            try
            {
                IsRunningCheck = true;
                StatusMessage = "Exporting report...";
                
                // In a real implementation, this would export the report to a file
                // using a service like the DataExportService
                
                // Simulate a successful export
                await Task.Delay(1000);
                
                StatusMessage = "Report exported successfully to App_Data\\Reports\\DataIntegrityReport.html";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error exporting report");
                StatusMessage = $"Error exporting report: {ex.Message}";
            }
            finally
            {
                IsRunningCheck = false;
            }
        }
    }
}
