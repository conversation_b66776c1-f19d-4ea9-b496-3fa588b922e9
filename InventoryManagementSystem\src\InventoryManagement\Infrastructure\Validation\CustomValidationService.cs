using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text.Json;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;

namespace InventoryManagement.Infrastructure.Validation
{
    /// <summary>
    /// Service for managing custom user-defined validation rules
    /// </summary>
    public class CustomValidationService : ICustomValidationService
    {
        private readonly ILogger<CustomValidationService> _logger;
        private readonly string _rulesFilePath;
        private List<CustomValidationRule> _rules;
        
        public CustomValidationService(ILogger<CustomValidationService> logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _rulesFilePath = Path.Combine(AppContext.BaseDirectory, "App_Data", "CustomValidationRules.json");
            _rules = new List<CustomValidationRule>();
            
            LoadRules();
        }
        
        /// <summary>
        /// Gets all active validation rules
        /// </summary>
        public IReadOnlyList<CustomValidationRule> GetActiveRules()
        {
            return _rules.Where(r => r.IsActive).ToList();
        }
        
        /// <summary>
        /// Gets all rules for a specific entity type
        /// </summary>
        public IReadOnlyList<CustomValidationRule> GetRulesForEntityType(string entityType)
        {
            return _rules
                .Where(r => r.IsActive && r.EntityType == entityType)
                .ToList();
        }
        
        /// <summary>
        /// Gets all rules for a specific entity property
        /// </summary>
        public IReadOnlyList<CustomValidationRule> GetRulesForProperty(string entityType, string propertyName)
        {
            return _rules
                .Where(r => r.IsActive && r.EntityType == entityType && r.PropertyName == propertyName)
                .ToList();
        }
        
        /// <summary>
        /// Adds a new validation rule
        /// </summary>
        public async Task<CustomValidationRule> AddRuleAsync(CustomValidationRule rule)
        {
            if (rule == null)
            {
                throw new ArgumentNullException(nameof(rule));
            }
            
            _rules.Add(rule);
            await SaveRulesAsync();
            _logger.LogInformation("Added custom validation rule: {RuleName}", rule.Name);
            
            return rule;
        }
        
        /// <summary>
        /// Updates an existing validation rule
        /// </summary>
        public async Task<bool> UpdateRuleAsync(CustomValidationRule rule)
        {
            if (rule == null)
            {
                throw new ArgumentNullException(nameof(rule));
            }
            
            var existingRule = _rules.FirstOrDefault(r => r.Id == rule.Id);
            if (existingRule == null)
            {
                _logger.LogWarning("Attempted to update non-existent rule: {RuleId}", rule.Id);
                return false;
            }
            
            // Update the existing rule with new values
            existingRule.Name = rule.Name;
            existingRule.Description = rule.Description;
            existingRule.ErrorMessage = rule.ErrorMessage;
            existingRule.ValidationExpression = rule.ValidationExpression;
            existingRule.Severity = rule.Severity;
            existingRule.IsActive = rule.IsActive;
            existingRule.ModifiedBy = rule.ModifiedBy;
            existingRule.ModifiedDate = DateTime.Now;
            
            await SaveRulesAsync();
            _logger.LogInformation("Updated custom validation rule: {RuleName}", rule.Name);
            
            return true;
        }
        
        /// <summary>
        /// Deactivates a validation rule
        /// </summary>
        public async Task<bool> DeactivateRuleAsync(Guid ruleId, string modifiedBy)
        {
            var rule = _rules.FirstOrDefault(r => r.Id == ruleId);
            if (rule == null)
            {
                _logger.LogWarning("Attempted to deactivate non-existent rule: {RuleId}", ruleId);
                return false;
            }
            
            rule.IsActive = false;
            rule.ModifiedBy = modifiedBy;
            rule.ModifiedDate = DateTime.Now;
            
            await SaveRulesAsync();
            _logger.LogInformation("Deactivated custom validation rule: {RuleName}", rule.Name);
            
            return true;
        }
        
        /// <summary>
        /// Loads validation rules from storage
        /// </summary>
        private void LoadRules()
        {
            try
            {
                if (File.Exists(_rulesFilePath))
                {
                    var json = File.ReadAllText(_rulesFilePath);
                    _rules = JsonSerializer.Deserialize<List<CustomValidationRule>>(json) ?? new List<CustomValidationRule>();
                    _logger.LogInformation("Loaded {Count} custom validation rules", _rules.Count);
                }
                else
                {
                    _logger.LogInformation("No custom validation rules file found, starting with empty rule set");
                    _rules = new List<CustomValidationRule>();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading custom validation rules");
                _rules = new List<CustomValidationRule>();
            }
        }
        
        /// <summary>
        /// Saves validation rules to storage
        /// </summary>
        private async Task SaveRulesAsync()
        {
            try
            {
                Directory.CreateDirectory(Path.GetDirectoryName(_rulesFilePath));
                var json = JsonSerializer.Serialize(_rules, new JsonSerializerOptions
                {
                    WriteIndented = true
                });
                
                await File.WriteAllTextAsync(_rulesFilePath, json);
                _logger.LogInformation("Saved {Count} custom validation rules", _rules.Count);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error saving custom validation rules");
            }
        }
    }
}
