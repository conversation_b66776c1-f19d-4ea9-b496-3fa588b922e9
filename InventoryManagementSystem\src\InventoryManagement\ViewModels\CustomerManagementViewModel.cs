using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Windows.Data;
using System.Windows.Input;
using InventoryManagement.Models;
using InventoryManagement.Services;
using InventoryManagement.ViewModels.Base;
using Microsoft.Extensions.Logging;

namespace InventoryManagement.ViewModels
{
    /// <summary>
    /// ViewModel for customer management operations
    /// </summary>
    public class CustomerManagementViewModel : BaseViewModel
    {
        private readonly ICustomerService _customerService;
        private readonly ILogger<CustomerManagementViewModel> _logger;

        // Collections
        private ObservableCollection<Customer> _customers;
        private ObservableCollection<Transaction> _customerTransactions;
        private ICollectionView _customersView;

        // Selected items
        private Customer? _selectedCustomer;
        private Transaction? _selectedTransaction;

        // Search and filter
        private string _searchText = string.Empty;
        private string _selectedCustomerType = "All";
        private bool _showActiveOnly = true;

        // Customer details
        private Customer _customerDetails;
        private bool _isEditingCustomer = false;
        private bool _isNewCustomer = false;

        // Customer summary
        private CustomerSummary? _customerSummary;

        public CustomerManagementViewModel(
            ICustomerService customerService,
            ILogger<CustomerManagementViewModel> logger)
        {
            _customerService = customerService;
            _logger = logger;

            // Initialize collections
            _customers = new ObservableCollection<Customer>();
            _customerTransactions = new ObservableCollection<Transaction>();
            _customerDetails = new Customer();

            // Initialize collection view for filtering
            _customersView = CollectionViewSource.GetDefaultView(_customers);
            _customersView.Filter = CustomerFilter;

            // Initialize commands
            InitializeCommands();

            // Load initial data
            _ = LoadCustomersAsync();
        }

        #region Properties

        public ObservableCollection<Customer> Customers
        {
            get => _customers;
            set => SetProperty(ref _customers, value);
        }

        public ObservableCollection<Transaction> CustomerTransactions
        {
            get => _customerTransactions;
            set => SetProperty(ref _customerTransactions, value);
        }

        public ICollectionView CustomersView
        {
            get => _customersView;
            set => SetProperty(ref _customersView, value);
        }

        public Customer? SelectedCustomer
        {
            get => _selectedCustomer;
            set
            {
                if (SetProperty(ref _selectedCustomer, value))
                {
                    OnPropertyChanged(nameof(IsCustomerSelected));
                    OnPropertyChanged(nameof(CanEditCustomer));
                    OnPropertyChanged(nameof(CanDeleteCustomer));
                    
                    if (value != null)
                    {
                        CustomerDetails = value.Clone();
                        _ = LoadCustomerDetailsAsync(value.Id);
                    }
                    else
                    {
                        CustomerDetails = new Customer();
                        CustomerSummary = null;
                        CustomerTransactions.Clear();
                    }

                    UpdateCommandStates();
                }
            }
        }

        public Transaction? SelectedTransaction
        {
            get => _selectedTransaction;
            set => SetProperty(ref _selectedTransaction, value);
        }

        public string SearchText
        {
            get => _searchText;
            set
            {
                if (SetProperty(ref _searchText, value))
                {
                    _customersView.Refresh();
                }
            }
        }

        public string SelectedCustomerType
        {
            get => _selectedCustomerType;
            set
            {
                if (SetProperty(ref _selectedCustomerType, value))
                {
                    _customersView.Refresh();
                }
            }
        }

        public bool ShowActiveOnly
        {
            get => _showActiveOnly;
            set
            {
                if (SetProperty(ref _showActiveOnly, value))
                {
                    _customersView.Refresh();
                }
            }
        }

        public Customer CustomerDetails
        {
            get => _customerDetails;
            set => SetProperty(ref _customerDetails, value);
        }

        public bool IsEditingCustomer
        {
            get => _isEditingCustomer;
            set
            {
                if (SetProperty(ref _isEditingCustomer, value))
                {
                    OnPropertyChanged(nameof(CanEditCustomerDetails));
                    UpdateCommandStates();
                }
            }
        }

        public bool IsNewCustomer
        {
            get => _isNewCustomer;
            set => SetProperty(ref _isNewCustomer, value);
        }

        public CustomerSummary? CustomerSummary
        {
            get => _customerSummary;
            set => SetProperty(ref _customerSummary, value);
        }

        public bool IsCustomerSelected => SelectedCustomer != null;
        public bool CanEditCustomer => IsCustomerSelected && !IsEditingCustomer;
        public bool CanDeleteCustomer => IsCustomerSelected && !IsEditingCustomer;
        public bool CanEditCustomerDetails => IsEditingCustomer || IsNewCustomer;

        public List<string> CustomerTypes { get; } = new List<string>
        {
            "All", "Regular", "VIP", "Wholesale", "Employee"
        };

        #endregion

        #region Commands

        public ICommand LoadCustomersCommand { get; private set; } = null!;
        public ICommand SearchCustomersCommand { get; private set; } = null!;
        public ICommand AddCustomerCommand { get; private set; } = null!;
        public ICommand EditCustomerCommand { get; private set; } = null!;
        public ICommand DeleteCustomerCommand { get; private set; } = null!;
        public ICommand SaveCustomerCommand { get; private set; } = null!;
        public ICommand CancelEditCommand { get; private set; } = null!;
        public ICommand ViewTransactionCommand { get; private set; } = null!;
        public ICommand ExportCustomersCommand { get; private set; } = null!;
        public ICommand RefreshCommand { get; private set; } = null!;

        private void InitializeCommands()
        {
            LoadCustomersCommand = new RelayCommand(async () => await LoadCustomersAsync());
            SearchCustomersCommand = new RelayCommand(async () => await SearchCustomersAsync());
            AddCustomerCommand = new RelayCommand(() => StartAddCustomer());
            EditCustomerCommand = new RelayCommand(() => StartEditCustomer(), () => CanEditCustomer);
            DeleteCustomerCommand = new RelayCommand(async () => await DeleteCustomerAsync(), () => CanDeleteCustomer);
            SaveCustomerCommand = new RelayCommand(async () => await SaveCustomerAsync(), () => CanEditCustomerDetails);
            CancelEditCommand = new RelayCommand(() => CancelEdit(), () => IsEditingCustomer || IsNewCustomer);
            ViewTransactionCommand = new RelayCommand(() => ViewTransaction(), () => SelectedTransaction != null);
            ExportCustomersCommand = new RelayCommand(async () => await ExportCustomersAsync());
            RefreshCommand = new RelayCommand(async () => await RefreshDataAsync());
        }

        private void UpdateCommandStates()
        {
            ((RelayCommand)EditCustomerCommand).RaiseCanExecuteChanged();
            ((RelayCommand)DeleteCustomerCommand).RaiseCanExecuteChanged();
            ((RelayCommand)SaveCustomerCommand).RaiseCanExecuteChanged();
            ((RelayCommand)CancelEditCommand).RaiseCanExecuteChanged();
            ((RelayCommand)ViewTransactionCommand).RaiseCanExecuteChanged();
        }

        #endregion

        #region Methods

        private async Task LoadCustomersAsync()
        {
            try
            {
                IsLoading = true;

                var customers = await _customerService.GetAllCustomersAsync();
                
                Customers.Clear();
                foreach (var customer in customers)
                {
                    Customers.Add(customer);
                }

                _logger.LogInformation("Loaded {Count} customers", customers.Count);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading customers");
                ShowErrorMessage("Failed to load customers: " + ex.Message);
            }
            finally
            {
                IsLoading = false;
            }
        }

        private async Task SearchCustomersAsync()
        {
            try
            {
                IsLoading = true;

                if (string.IsNullOrWhiteSpace(SearchText))
                {
                    await LoadCustomersAsync();
                    return;
                }

                var customers = await _customerService.SearchCustomersAsync(SearchText);
                
                Customers.Clear();
                foreach (var customer in customers)
                {
                    Customers.Add(customer);
                }

                _logger.LogInformation("Found {Count} customers matching '{SearchText}'", customers.Count, SearchText);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error searching customers");
                ShowErrorMessage("Failed to search customers: " + ex.Message);
            }
            finally
            {
                IsLoading = false;
            }
        }

        private async Task LoadCustomerDetailsAsync(int customerId)
        {
            try
            {
                // Load customer summary
                CustomerSummary = await _customerService.GetCustomerSummaryAsync(customerId);

                // Load customer transactions
                var transactions = await _customerService.GetCustomerTransactionHistoryAsync(customerId);
                
                CustomerTransactions.Clear();
                foreach (var transaction in transactions.Take(50)) // Limit to recent 50 transactions
                {
                    CustomerTransactions.Add(transaction);
                }

                _logger.LogInformation("Loaded details for customer {CustomerId}", customerId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading customer details for {CustomerId}", customerId);
                ShowErrorMessage("Failed to load customer details: " + ex.Message);
            }
        }

        private void StartAddCustomer()
        {
            CustomerDetails = new Customer
            {
                CustomerType = "Regular",
                IsActive = true,
                CreatedDate = DateTime.Now
            };
            IsNewCustomer = true;
            IsEditingCustomer = true;
            SelectedCustomer = null;
        }

        private void StartEditCustomer()
        {
            if (SelectedCustomer != null)
            {
                CustomerDetails = SelectedCustomer.Clone();
                IsEditingCustomer = true;
                IsNewCustomer = false;
            }
        }

        private async Task SaveCustomerAsync()
        {
            try
            {
                IsLoading = true;

                Customer savedCustomer;
                if (IsNewCustomer)
                {
                    savedCustomer = await _customerService.CreateCustomerAsync(CustomerDetails, GetCurrentUserId());
                    Customers.Add(savedCustomer);
                    ShowSuccessMessage($"Customer '{savedCustomer.Name}' created successfully");
                }
                else
                {
                    savedCustomer = await _customerService.UpdateCustomerAsync(CustomerDetails, GetCurrentUserId());
                    
                    // Update the customer in the collection
                    var existingCustomer = Customers.FirstOrDefault(c => c.Id == savedCustomer.Id);
                    if (existingCustomer != null)
                    {
                        var index = Customers.IndexOf(existingCustomer);
                        Customers[index] = savedCustomer;
                    }
                    
                    ShowSuccessMessage($"Customer '{savedCustomer.Name}' updated successfully");
                }

                SelectedCustomer = savedCustomer;
                IsEditingCustomer = false;
                IsNewCustomer = false;

                _logger.LogInformation("Saved customer {CustomerName} with ID {CustomerId}", savedCustomer.Name, savedCustomer.Id);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error saving customer");
                ShowErrorMessage("Failed to save customer: " + ex.Message);
            }
            finally
            {
                IsLoading = false;
            }
        }

        private async Task DeleteCustomerAsync()
        {
            if (SelectedCustomer == null) return;

            try
            {
                var result = ShowConfirmationDialog(
                    $"Are you sure you want to delete customer '{SelectedCustomer.Name}'?",
                    "Confirm Delete");

                if (result)
                {
                    IsLoading = true;

                    var success = await _customerService.DeleteCustomerAsync(SelectedCustomer.Id, GetCurrentUserId());
                    if (success)
                    {
                        Customers.Remove(SelectedCustomer);
                        SelectedCustomer = null;
                        ShowSuccessMessage("Customer deleted successfully");
                    }
                    else
                    {
                        ShowErrorMessage("Failed to delete customer");
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting customer {CustomerId}", SelectedCustomer?.Id);
                ShowErrorMessage("Failed to delete customer: " + ex.Message);
            }
            finally
            {
                IsLoading = false;
            }
        }

        private void CancelEdit()
        {
            if (SelectedCustomer != null)
            {
                CustomerDetails = SelectedCustomer.Clone();
            }
            else
            {
                CustomerDetails = new Customer();
            }
            
            IsEditingCustomer = false;
            IsNewCustomer = false;
        }

        private void ViewTransaction()
        {
            if (SelectedTransaction != null)
            {
                // Navigate to transaction details view
                // This would typically use a navigation service
                ShowInfoMessage($"Viewing transaction {SelectedTransaction.Id}");
            }
        }

        private async Task ExportCustomersAsync()
        {
            try
            {
                IsLoading = true;

                // Export customers to CSV or Excel
                // This would typically use an export service
                ShowSuccessMessage("Customers exported successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error exporting customers");
                ShowErrorMessage("Failed to export customers: " + ex.Message);
            }
            finally
            {
                IsLoading = false;
            }
        }

        private async Task RefreshDataAsync()
        {
            await LoadCustomersAsync();
            if (SelectedCustomer != null)
            {
                await LoadCustomerDetailsAsync(SelectedCustomer.Id);
            }
        }

        private bool CustomerFilter(object item)
        {
            if (item is not Customer customer) return false;

            // Active filter
            if (ShowActiveOnly && !customer.IsActive) return false;

            // Customer type filter
            if (SelectedCustomerType != "All" && customer.CustomerType != SelectedCustomerType) return false;

            // Search text filter
            if (!string.IsNullOrWhiteSpace(SearchText))
            {
                var searchLower = SearchText.ToLower();
                return customer.Name.ToLower().Contains(searchLower) ||
                       customer.CustomerNumber.ToLower().Contains(searchLower) ||
                       customer.PhoneNumber.Contains(SearchText) ||
                       customer.Email.ToLower().Contains(searchLower);
            }

            return true;
        }

        private int GetCurrentUserId()
        {
            // This should come from the current user context
            return 1; // Placeholder
        }

        #endregion
    }
}
