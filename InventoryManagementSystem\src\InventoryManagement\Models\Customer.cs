using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;

namespace InventoryManagement.Models
{
    /// <summary>
    /// Model for a customer in the inventory system
    /// </summary>
    public class Customer
    {
        [Key]
        public int Id { get; set; }
        
        [Required]
        [StringLength(100)]
        public string Name { get; set; }
        
        [StringLength(100)]
        public string Email { get; set; }
        
        [StringLength(20)]
        public string PhoneNumber { get; set; }
        
        [StringLength(200)]
        public string Address { get; set; }
        
        [StringLength(50)]
        public string City { get; set; }
        
        [StringLength(50)]
        public string Country { get; set; }
        
        [StringLength(20)]
        public string PostalCode { get; set; }
        
        public bool IsActive { get; set; } = true;
        
        public DateTime CreatedDate { get; set; } = DateTime.Now;
        
        [StringLength(100)]
        public string CustomerNumber { get; set; }
        
        public decimal TotalPurchases { get; set; } = 0;
        
        public int LoyaltyPoints { get; set; } = 0;
        
        [StringLength(50)]
        public string CustomerType { get; set; } = "Regular";
        
        [Column(TypeName = "decimal(18,2)")]
        public decimal CreditLimit { get; set; } = 0;
        
        [Column(TypeName = "decimal(18,2)")]
        public decimal CurrentCreditBalance { get; set; } = 0;
        
        /// <summary>
        /// Date of last credit payment made by customer
        /// </summary>
        public DateTime? LastCreditPaymentDate { get; set; }
        
        /// <summary>
        /// Whether customer has any overdue payments
        /// </summary>
        public bool HasOverduePayments { get; set; } = false;
        
        /// <summary>
        /// Credit rating from 0-100 (higher is better)
        /// </summary>
        public int CreditRating { get; set; } = 70;
        
        [StringLength(500)]
        public string Notes { get; set; }
        
        [InverseProperty("Customer")]
        public List<Transaction> Transactions { get; set; } = new List<Transaction>();
        
        [InverseProperty("Customer")]
        public List<CreditTransaction> CreditTransactions { get; set; } = new List<CreditTransaction>();
        
        [InverseProperty("Customer")]
        public List<FinancialTransaction> FinancialTransactions { get; set; } = new List<FinancialTransaction>();
        
        /// <summary>
        /// Gets active credit transactions with pending payment
        /// </summary>
        /// <returns>List of pending credit transactions</returns>
        public List<CreditTransaction> GetPendingCreditTransactions()
        {
            return CreditTransactions?.Where(ct => ct.IsActive && ct.Status == CreditStatus.Pending).ToList() ?? new List<CreditTransaction>();
        }
        
        /// <summary>
        /// Gets overdue credit transactions
        /// </summary>
        /// <returns>List of overdue credit transactions</returns>
        public List<CreditTransaction> GetOverdueCreditTransactions()
        {
            return CreditTransactions?.Where(ct => ct.IsActive && ct.Status == CreditStatus.Pending && ct.IsOverdue()).ToList() ?? new List<CreditTransaction>();
        }
        
        /// <summary>
        /// Gets credit transactions due today
        /// </summary>
        /// <returns>List of credit transactions due today</returns>
        public List<CreditTransaction> GetCreditTransactionsDueToday()
        {
            return CreditTransactions?.Where(ct => ct.IsActive && ct.Status == CreditStatus.Pending && ct.IsDueToday()).ToList() ?? new List<CreditTransaction>();
        }

        /// <summary>
        /// Creates a deep copy of the customer
        /// </summary>
        /// <returns>Cloned customer object</returns>
        public Customer Clone()
        {
            return new Customer
            {
                Id = this.Id,
                CustomerNumber = this.CustomerNumber,
                Name = this.Name,
                PhoneNumber = this.PhoneNumber,
                Email = this.Email,
                Address = this.Address,
                City = this.City,
                Country = this.Country,
                PostalCode = this.PostalCode,
                CustomerType = this.CustomerType,
                CreditLimit = this.CreditLimit,
                CurrentCreditBalance = this.CurrentCreditBalance,
                LoyaltyPoints = this.LoyaltyPoints,
                IsActive = this.IsActive,
                Notes = this.Notes,
                CreatedDate = this.CreatedDate,
                TotalPurchases = this.TotalPurchases,
                LastCreditPaymentDate = this.LastCreditPaymentDate,
                HasOverduePayments = this.HasOverduePayments,
                CreditRating = this.CreditRating
            };
        }
    }
}
