using InventoryManagement.Models;

namespace InventoryManagement.DataAccess.Repositories
{
    /// <summary>
    /// Interface for customer repository operations
    /// </summary>
    public interface ICustomerRepository
    {
        /// <summary>
        /// Get customer by ID
        /// </summary>
        /// <param name="id">Customer ID</param>
        /// <returns>Customer or null if not found</returns>
        Task<Customer?> GetByIdAsync(int id);

        /// <summary>
        /// Get customer by customer number
        /// </summary>
        /// <param name="customerNumber">Customer number</param>
        /// <returns>Customer or null if not found</returns>
        Task<Customer?> GetByCustomerNumberAsync(string customerNumber);

        /// <summary>
        /// Get customer by phone number
        /// </summary>
        /// <param name="phoneNumber">Phone number</param>
        /// <returns>Customer or null if not found</returns>
        Task<Customer?> GetByPhoneAsync(string phoneNumber);

        /// <summary>
        /// Get customer by email address
        /// </summary>
        /// <param name="email">Email address</param>
        /// <returns>Customer or null if not found</returns>
        Task<Customer?> GetByEmailAsync(string email);

        /// <summary>
        /// Get all active customers
        /// </summary>
        /// <returns>List of customers</returns>
        Task<List<Customer>> GetAllAsync();

        /// <summary>
        /// Search customers by name, customer number, phone, or email
        /// </summary>
        /// <param name="searchTerm">Search term</param>
        /// <returns>List of matching customers</returns>
        Task<List<Customer>> SearchAsync(string searchTerm);

        /// <summary>
        /// Get customers by type
        /// </summary>
        /// <param name="customerType">Customer type</param>
        /// <returns>List of customers</returns>
        Task<List<Customer>> GetByTypeAsync(string customerType);

        /// <summary>
        /// Get customers with overdue payments
        /// </summary>
        /// <returns>List of customers with overdue payments</returns>
        Task<List<Customer>> GetWithOverduePaymentsAsync();

        /// <summary>
        /// Get top customers by total purchases
        /// </summary>
        /// <param name="count">Number of customers to return</param>
        /// <returns>List of top customers</returns>
        Task<List<Customer>> GetTopCustomersAsync(int count = 10);

        /// <summary>
        /// Create a new customer
        /// </summary>
        /// <param name="customer">Customer to create</param>
        /// <returns>Created customer</returns>
        Task<Customer> CreateAsync(Customer customer);

        /// <summary>
        /// Update an existing customer
        /// </summary>
        /// <param name="customer">Customer to update</param>
        /// <returns>Updated customer</returns>
        Task<Customer> UpdateAsync(Customer customer);

        /// <summary>
        /// Delete a customer (soft delete)
        /// </summary>
        /// <param name="id">Customer ID</param>
        /// <returns>True if deleted successfully</returns>
        Task<bool> DeleteAsync(int id);

        /// <summary>
        /// Check if customer exists
        /// </summary>
        /// <param name="id">Customer ID</param>
        /// <returns>True if customer exists</returns>
        Task<bool> ExistsAsync(int id);

        /// <summary>
        /// Check if customer number already exists
        /// </summary>
        /// <param name="customerNumber">Customer number</param>
        /// <returns>True if customer number exists</returns>
        Task<bool> CustomerNumberExistsAsync(string customerNumber);

        /// <summary>
        /// Get customer transactions within date range
        /// </summary>
        /// <param name="customerId">Customer ID</param>
        /// <param name="fromDate">Start date (optional)</param>
        /// <param name="toDate">End date (optional)</param>
        /// <returns>List of transactions</returns>
        Task<List<Transaction>> GetCustomerTransactionsAsync(int customerId, DateTime? fromDate = null, DateTime? toDate = null);

        /// <summary>
        /// Get customer total purchases within date range
        /// </summary>
        /// <param name="customerId">Customer ID</param>
        /// <param name="fromDate">Start date (optional)</param>
        /// <param name="toDate">End date (optional)</param>
        /// <returns>Total purchase amount</returns>
        Task<decimal> GetCustomerTotalPurchasesAsync(int customerId, DateTime? fromDate = null, DateTime? toDate = null);

        /// <summary>
        /// Update customer's total purchases amount
        /// </summary>
        /// <param name="customerId">Customer ID</param>
        Task UpdateCustomerTotalPurchasesAsync(int customerId);
    }
}
