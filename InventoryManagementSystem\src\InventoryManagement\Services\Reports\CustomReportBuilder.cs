using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using InventoryManagement.Models;
using InventoryManagement.Models.Reports;
using Microsoft.Extensions.Logging;

namespace InventoryManagement.Services.Reports
{
    /// <summary>
    /// Service for building and managing custom reports defined by users
    /// </summary>
    public class CustomReportBuilder : ICustomReportBuilder
    {
        private readonly ILogger<CustomReportBuilder> _logger;
        private readonly IDataAccessService _dataAccess;
        private readonly IReportExporter _reportExporter;
        private readonly string _customReportsDirectory;
        
        public CustomReportBuilder(
            ILogger<CustomReportBuilder> logger,
            IDataAccessService dataAccess,
            IReportExporter reportExporter)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _dataAccess = dataAccess ?? throw new ArgumentNullException(nameof(dataAccess));
            _reportExporter = reportExporter ?? throw new ArgumentNullException(nameof(reportExporter));
            _customReportsDirectory = Path.Combine(AppContext.BaseDirectory, "App_Data", "CustomReports");
            
            Directory.CreateDirectory(_customReportsDirectory);
        }
        
        /// <summary>
        /// Gets a list of all available custom report definitions
        /// </summary>
        public async Task<List<CustomReportDefinition>> GetCustomReportsAsync()
        {
            try
            {
                var reports = new List<CustomReportDefinition>();
                
                foreach (var file in Directory.GetFiles(_customReportsDirectory, "*.json"))
                {
                    try
                    {
                        var json = await File.ReadAllTextAsync(file);
                        var report = System.Text.Json.JsonSerializer.Deserialize<CustomReportDefinition>(json);
                        
                        if (report != null)
                        {
                            reports.Add(report);
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Error loading custom report from {Path}", file);
                    }
                }
                
                return reports;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting custom reports");
                return new List<CustomReportDefinition>();
            }
        }
        
        /// <summary>
        /// Saves a custom report definition
        /// </summary>
        public async Task SaveCustomReportAsync(CustomReportDefinition report)
        {
            if (report == null)
            {
                throw new ArgumentNullException(nameof(report));
            }
            
            if (string.IsNullOrEmpty(report.Name))
            {
                throw new ArgumentException("Report name is required", nameof(report));
            }
            
            try
            {
                // Ensure a valid ID exists
                if (report.Id == Guid.Empty)
                {
                    report.Id = Guid.NewGuid();
                }
                
                // Set created/modified dates
                if (report.CreatedDate == DateTime.MinValue)
                {
                    report.CreatedDate = DateTime.Now;
                }
                
                report.ModifiedDate = DateTime.Now;
                
                // Save the report definition
                var filePath = Path.Combine(_customReportsDirectory, $"{report.Id}.json");
                var json = System.Text.Json.JsonSerializer.Serialize(report, new System.Text.Json.JsonSerializerOptions
                {
                    WriteIndented = true
                });
                
                await File.WriteAllTextAsync(filePath, json);
                _logger.LogInformation("Saved custom report: {ReportName} ({ReportId})", report.Name, report.Id);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error saving custom report: {ReportName}", report.Name);
                throw;
            }
        }
        
        /// <summary>
        /// Deletes a custom report definition
        /// </summary>
        public Task DeleteCustomReportAsync(Guid reportId)
        {
            try
            {
                var filePath = Path.Combine(_customReportsDirectory, $"{reportId}.json");
                
                if (File.Exists(filePath))
                {
                    File.Delete(filePath);
                    _logger.LogInformation("Deleted custom report: {ReportId}", reportId);
                }
                
                return Task.CompletedTask;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting custom report: {ReportId}", reportId);
                throw;
            }
        }
        
        /// <summary>
        /// Executes a custom report and returns the result
        /// </summary>
        public async Task<ReportResult> ExecuteCustomReportAsync(
            CustomReportDefinition report, 
            Dictionary<string, object> parameters = null)
        {
            if (report == null)
            {
                throw new ArgumentNullException(nameof(report));
            }
            
            try
            {
                _logger.LogInformation("Executing custom report: {ReportName}", report.Name);
                
                // Build the SQL query with parameters
                var query = report.SqlQuery;
                
                // Apply any parameter replacements
                if (parameters != null)
                {
                    foreach (var param in parameters)
                    {
                        var placeholder = $"@{param.Key}";
                        
                        if (query.Contains(placeholder))
                        {
                            var paramValue = param.Value?.ToString() ?? "NULL";
                            query = query.Replace(placeholder, paramValue);
                        }
                    }
                }
                
                // Execute the query
                var data = await _dataAccess.ExecuteQueryAsync(query);
                
                // Create the report result
                var result = new ReportResult
                {
                    ReportId = report.Id,
                    ReportName = report.Name,
                    GeneratedDate = DateTime.Now,
                    Parameters = parameters,
                    Data = data
                };
                
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error executing custom report: {ReportName}", report.Name);
                throw;
            }
        }
        
        /// <summary>
        /// Exports a report result to a file
        /// </summary>
        public async Task<string> ExportCustomReportAsync(
            ReportResult result,
            ExportFormat format,
            string outputPath = null)
        {
            if (result == null)
            {
                throw new ArgumentNullException(nameof(result));
            }
            
            try
            {
                // Generate default path if not provided
                if (string.IsNullOrEmpty(outputPath))
                {
                    var extension = format switch
                    {
                        ExportFormat.Excel => "xlsx",
                        ExportFormat.Pdf => "pdf",
                        ExportFormat.Csv => "csv",
                        _ => "txt"
                    };
                    
                    outputPath = Path.Combine(
                        Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments),
                        "Reports",
                        $"{result.ReportName}_{DateTime.Now:yyyyMMdd_HHmmss}.{extension}");
                    
                    Directory.CreateDirectory(Path.GetDirectoryName(outputPath));
                }
                
                // Export the report
                await _reportExporter.ExportAsync(result.Data, outputPath, format);
                _logger.LogInformation("Exported report: {ReportName} to {Path}", result.ReportName, outputPath);
                
                return outputPath;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error exporting report: {ReportName}", result.ReportName);
                throw;
            }
        }
    }
}
