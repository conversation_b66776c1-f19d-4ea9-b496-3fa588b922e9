using System.ComponentModel.DataAnnotations;

namespace InventoryManagement.Models.Reports
{
    /// <summary>
    /// Inventory report model containing inventory analytics and metrics
    /// </summary>
    public class InventoryReport
    {
        public DateTime ReportDate { get; set; } = DateTime.Now;
        public DateTime FromDate { get; set; }
        public DateTime ToDate { get; set; }
        public string ReportPeriod { get; set; } = string.Empty;
        public string GeneratedBy { get; set; } = string.Empty;

        // Summary metrics
        public int TotalItems { get; set; }
        public int ActiveItems { get; set; }
        public int InactiveItems { get; set; }
        public decimal TotalInventoryValue { get; set; }
        public decimal AverageItemValue { get; set; }
        public int TotalLocations { get; set; }

        // Stock level analytics
        public int ItemsInStock { get; set; }
        public int ItemsOutOfStock { get; set; }
        public int ItemsLowStock { get; set; }
        public int ItemsOverstocked { get; set; }

        // Movement analytics
        public int TotalMovements { get; set; }
        public int InboundMovements { get; set; }
        public int OutboundMovements { get; set; }
        public int TransferMovements { get; set; }
        public int AdjustmentMovements { get; set; }

        // Category breakdown
        public List<CategoryInventory> CategoryInventoryData { get; set; } = new List<CategoryInventory>();

        // Location breakdown
        public List<LocationInventory> LocationInventoryData { get; set; } = new List<LocationInventory>();

        // Stock alerts
        public List<LowStockItem> LowStockItems { get; set; } = new List<LowStockItem>();
        public List<OutOfStockItem> OutOfStockItems { get; set; } = new List<OutOfStockItem>();
        public List<OverstockedItem> OverstockedItems { get; set; } = new List<OverstockedItem>();

        // Movement details
        public List<InventoryMovementSummary> RecentMovements { get; set; } = new List<InventoryMovementSummary>();

        // Turnover analytics
        public List<ItemTurnover> ItemTurnoverData { get; set; } = new List<ItemTurnover>();
        public List<SlowMovingItem> SlowMovingItems { get; set; } = new List<SlowMovingItem>();
        public List<FastMovingItem> FastMovingItems { get; set; } = new List<FastMovingItem>();

        // Valuation
        public List<ItemValuation> ItemValuationData { get; set; } = new List<ItemValuation>();
        public decimal TotalCostValue { get; set; }
        public decimal TotalRetailValue { get; set; }
        public decimal PotentialProfit { get; set; }

        // Supplier analytics
        public List<SupplierInventory> SupplierInventoryData { get; set; } = new List<SupplierInventory>();

        // Aging analysis
        public List<InventoryAging> InventoryAgingData { get; set; } = new List<InventoryAging>();
    }

    /// <summary>
    /// Category inventory breakdown
    /// </summary>
    public class CategoryInventory
    {
        public string CategoryName { get; set; } = string.Empty;
        public int ItemCount { get; set; }
        public int TotalQuantity { get; set; }
        public decimal TotalValue { get; set; }
        public decimal AverageItemValue { get; set; }
        public int LowStockItems { get; set; }
        public int OutOfStockItems { get; set; }
        public decimal PercentageOfTotalValue { get; set; }
    }

    /// <summary>
    /// Location inventory breakdown
    /// </summary>
    public class LocationInventory
    {
        public int LocationId { get; set; }
        public string LocationName { get; set; } = string.Empty;
        public int ItemCount { get; set; }
        public int TotalQuantity { get; set; }
        public decimal TotalValue { get; set; }
        public int LowStockItems { get; set; }
        public int OutOfStockItems { get; set; }
        public decimal UtilizationPercentage { get; set; }
    }

    /// <summary>
    /// Low stock item alert
    /// </summary>
    public class LowStockItem
    {
        public int ItemId { get; set; }
        public string ItemName { get; set; } = string.Empty;
        public string ItemCode { get; set; } = string.Empty;
        public string Category { get; set; } = string.Empty;
        public int CurrentStock { get; set; }
        public int MinimumStock { get; set; }
        public int ReorderLevel { get; set; }
        public string LocationName { get; set; } = string.Empty;
        public int DaysUntilStockOut { get; set; }
        public decimal AverageDailySales { get; set; }
        public string SupplierName { get; set; } = string.Empty;
    }

    /// <summary>
    /// Out of stock item alert
    /// </summary>
    public class OutOfStockItem
    {
        public int ItemId { get; set; }
        public string ItemName { get; set; } = string.Empty;
        public string ItemCode { get; set; } = string.Empty;
        public string Category { get; set; } = string.Empty;
        public string LocationName { get; set; } = string.Empty;
        public DateTime LastStockDate { get; set; }
        public int DaysOutOfStock { get; set; }
        public decimal LostSalesEstimate { get; set; }
        public string SupplierName { get; set; } = string.Empty;
        public bool HasPendingOrder { get; set; }
    }

    /// <summary>
    /// Overstocked item information
    /// </summary>
    public class OverstockedItem
    {
        public int ItemId { get; set; }
        public string ItemName { get; set; } = string.Empty;
        public string ItemCode { get; set; } = string.Empty;
        public string Category { get; set; } = string.Empty;
        public int CurrentStock { get; set; }
        public int MaximumStock { get; set; }
        public int ExcessQuantity { get; set; }
        public decimal ExcessValue { get; set; }
        public string LocationName { get; set; } = string.Empty;
        public decimal AverageDailySales { get; set; }
        public int DaysOfSupply { get; set; }
    }

    /// <summary>
    /// Inventory movement summary
    /// </summary>
    public class InventoryMovementSummary
    {
        public DateTime MovementDate { get; set; }
        public string MovementType { get; set; } = string.Empty;
        public string ItemName { get; set; } = string.Empty;
        public string ItemCode { get; set; } = string.Empty;
        public int Quantity { get; set; }
        public string FromLocation { get; set; } = string.Empty;
        public string ToLocation { get; set; } = string.Empty;
        public string Reason { get; set; } = string.Empty;
        public string UserName { get; set; } = string.Empty;
        public decimal UnitCost { get; set; }
        public decimal TotalValue { get; set; }
    }

    /// <summary>
    /// Item turnover analytics
    /// </summary>
    public class ItemTurnover
    {
        public int ItemId { get; set; }
        public string ItemName { get; set; } = string.Empty;
        public string ItemCode { get; set; } = string.Empty;
        public string Category { get; set; } = string.Empty;
        public decimal TurnoverRatio { get; set; }
        public int DaysInInventory { get; set; }
        public int QuantitySold { get; set; }
        public decimal AverageInventory { get; set; }
        public decimal SalesVelocity { get; set; } // Units per day
        public string TurnoverCategory { get; set; } = string.Empty; // Fast, Medium, Slow
    }

    /// <summary>
    /// Slow moving item information
    /// </summary>
    public class SlowMovingItem
    {
        public int ItemId { get; set; }
        public string ItemName { get; set; } = string.Empty;
        public string ItemCode { get; set; } = string.Empty;
        public string Category { get; set; } = string.Empty;
        public int CurrentStock { get; set; }
        public DateTime LastSaleDate { get; set; }
        public int DaysSinceLastSale { get; set; }
        public decimal InventoryValue { get; set; }
        public int QuantitySoldLast30Days { get; set; }
        public int QuantitySoldLast90Days { get; set; }
        public string RecommendedAction { get; set; } = string.Empty;
    }

    /// <summary>
    /// Fast moving item information
    /// </summary>
    public class FastMovingItem
    {
        public int ItemId { get; set; }
        public string ItemName { get; set; } = string.Empty;
        public string ItemCode { get; set; } = string.Empty;
        public string Category { get; set; } = string.Empty;
        public int CurrentStock { get; set; }
        public decimal SalesVelocity { get; set; }
        public int QuantitySoldLast7Days { get; set; }
        public int QuantitySoldLast30Days { get; set; }
        public decimal TurnoverRatio { get; set; }
        public int ProjectedStockoutDays { get; set; }
        public string RecommendedAction { get; set; } = string.Empty;
    }

    /// <summary>
    /// Item valuation information
    /// </summary>
    public class ItemValuation
    {
        public int ItemId { get; set; }
        public string ItemName { get; set; } = string.Empty;
        public string ItemCode { get; set; } = string.Empty;
        public string Category { get; set; } = string.Empty;
        public int Quantity { get; set; }
        public decimal UnitCost { get; set; }
        public decimal UnitPrice { get; set; }
        public decimal TotalCostValue { get; set; }
        public decimal TotalRetailValue { get; set; }
        public decimal PotentialProfit { get; set; }
        public decimal ProfitMargin { get; set; }
        public string LocationName { get; set; } = string.Empty;
    }

    /// <summary>
    /// Supplier inventory breakdown
    /// </summary>
    public class SupplierInventory
    {
        public int SupplierId { get; set; }
        public string SupplierName { get; set; } = string.Empty;
        public int ItemCount { get; set; }
        public int TotalQuantity { get; set; }
        public decimal TotalValue { get; set; }
        public decimal AverageItemCost { get; set; }
        public int LowStockItems { get; set; }
        public int OutOfStockItems { get; set; }
        public decimal PercentageOfTotalInventory { get; set; }
    }

    /// <summary>
    /// Inventory aging analysis
    /// </summary>
    public class InventoryAging
    {
        public string AgeRange { get; set; } = string.Empty; // 0-30 days, 31-60 days, etc.
        public int ItemCount { get; set; }
        public int TotalQuantity { get; set; }
        public decimal TotalValue { get; set; }
        public decimal PercentageOfTotalValue { get; set; }
        public List<AgedItem> Items { get; set; } = new List<AgedItem>();
    }

    /// <summary>
    /// Aged item information
    /// </summary>
    public class AgedItem
    {
        public int ItemId { get; set; }
        public string ItemName { get; set; } = string.Empty;
        public string ItemCode { get; set; } = string.Empty;
        public int Quantity { get; set; }
        public DateTime LastMovementDate { get; set; }
        public int DaysInInventory { get; set; }
        public decimal Value { get; set; }
        public string LocationName { get; set; } = string.Empty;
    }

    /// <summary>
    /// Inventory report parameters
    /// </summary>
    public class InventoryReportParameters
    {
        public DateTime AsOfDate { get; set; } = DateTime.Now;
        public List<int> LocationIds { get; set; } = new List<int>();
        public List<string> Categories { get; set; } = new List<string>();
        public List<int> SupplierIds { get; set; } = new List<int>();
        public bool IncludeLowStockAlerts { get; set; } = true;
        public bool IncludeOutOfStockAlerts { get; set; } = true;
        public bool IncludeOverstockAlerts { get; set; } = true;
        public bool IncludeMovementHistory { get; set; } = false;
        public bool IncludeTurnoverAnalysis { get; set; } = true;
        public bool IncludeValuation { get; set; } = true;
        public bool IncludeAgingAnalysis { get; set; } = false;
        public int MovementHistoryDays { get; set; } = 30;
        public int TopItemsCount { get; set; } = 20;
        public InventoryReportGrouping Grouping { get; set; } = InventoryReportGrouping.Category;
    }

    /// <summary>
    /// Inventory report grouping options
    /// </summary>
    public enum InventoryReportGrouping
    {
        Category,
        Location,
        Supplier,
        TurnoverRate,
        StockLevel
    }

    /// <summary>
    /// Inventory report format
    /// </summary>
    public enum InventoryReportFormat
    {
        PDF,
        Excel,
        CSV,
        JSON
    }
}
