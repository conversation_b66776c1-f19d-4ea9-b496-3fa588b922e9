using System;
using InventoryManagement.Services.Database;
using InventoryManagement.Services.Database.Monitoring;
using InventoryManagement.Services.Validation;
using InventoryManagement.Services.Validation.Rules;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace InventoryManagement.Infrastructure.Extensions
{
    /// <summary>
    /// Extensions for registering data integrity and database health monitoring services
    /// </summary>
    public static class DataIntegrityServiceExtensions
    {
        /// <summary>
        /// Registers all data integrity and validation services
        /// </summary>
        public static IServiceCollection AddDataIntegrityServices(this IServiceCollection services, IConfiguration configuration)
        {
            if (services == null) throw new ArgumentNullException(nameof(services));
            
            // Get connection string from configuration
            string connectionString = configuration.GetConnectionString("DefaultConnection");
            
            // Register services
            services.AddSingleton<IDataIntegrityService, DataIntegrityService>();
            services.AddSingleton<IBackupVerificationService, BackupVerificationService>(
                provider => new BackupVerificationService(
                    connectionString, 
                    provider.GetRequiredService<Microsoft.Extensions.Logging.ILogger<BackupVerificationService>>()));
            
            // Register database health monitoring
            services.AddSingleton<IDatabaseHealthMonitor, DatabaseHealthMonitor>(
                provider => new DatabaseHealthMonitor(
                    connectionString,
                    provider.GetRequiredService<Microsoft.Extensions.Logging.ILogger<DatabaseHealthMonitor>>()));
            
            // Register integrity rules
            services.AddTransient<IDataIntegrityRule, InventoryConsistencyRule>();
            services.AddTransient<IDataIntegrityRule, OrphanedRecordsRule>();
            services.AddTransient<IDataIntegrityRule, BackupVerificationRule>();
            
            return services;
        }
        
        /// <summary>
        /// Configures the DataIntegrityService by registering all rules
        /// </summary>
        public static IServiceProvider ConfigureDataIntegrityService(this IServiceProvider serviceProvider)
        {
            var dataIntegrityService = serviceProvider.GetRequiredService<IDataIntegrityService>();
            var rules = serviceProvider.GetServices<IDataIntegrityRule>();
            
            foreach (var rule in rules)
            {
                dataIntegrityService.RegisterRule(rule);
            }
            
            return serviceProvider;
        }
    }
}
