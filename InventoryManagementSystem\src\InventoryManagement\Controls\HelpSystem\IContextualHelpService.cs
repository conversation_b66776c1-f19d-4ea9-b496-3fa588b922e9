using System.Threading.Tasks;
using System.Windows;

namespace InventoryManagement.Controls.HelpSystem
{
    /// <summary>
    /// Interface for providing context-sensitive help within the application
    /// </summary>
    public interface IContextualHelpService
    {
        /// <summary>
        /// Shows help content for the specified control context
        /// </summary>
        /// <param name="controlName">Name of the control needing help</param>
        /// <param name="contextKey">Optional additional context</param>
        Task ShowHelpAsync(string controlName, string contextKey = null);
        
        /// <summary>
        /// Shows context-sensitive help for the element under the cursor
        /// </summary>
        /// <param name="element">UI element requesting help</param>
        Task ShowHelpForElementAsync(FrameworkElement element);
    }
}
