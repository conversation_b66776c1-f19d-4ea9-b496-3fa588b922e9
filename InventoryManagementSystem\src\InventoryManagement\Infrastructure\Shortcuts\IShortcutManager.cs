using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Windows.Input;

namespace InventoryManagement.Infrastructure.Shortcuts
{
    /// <summary>
    /// Interface for managing keyboard shortcuts
    /// </summary>
    public interface IShortcutManager
    {
        /// <summary>
        /// Gets a dictionary of all registered shortcuts
        /// </summary>
        IReadOnlyDictionary<string, ShortcutDefinition> Shortcuts { get; }
        
        /// <summary>
        /// Registers an action to be executed when a shortcut is triggered
        /// </summary>
        /// <param name="shortcutName">Name of the shortcut</param>
        /// <param name="action">Action to execute</param>
        void RegisterShortcutAction(string shortcutName, Action<object> action);
        
        /// <summary>
        /// Handles a key event and executes the corresponding shortcut action if found
        /// </summary>
        /// <param name="key">Key that was pressed</param>
        /// <param name="modifiers">Modifier keys that were pressed</param>
        /// <param name="context">Optional context to pass to the action</param>
        /// <returns>True if a shortcut was triggered, false otherwise</returns>
        bool HandleKeyEvent(Key key, ModifierKeys modifiers, object context = null);
        
        /// <summary>
        /// Adds or updates a shortcut definition
        /// </summary>
        /// <param name="shortcut">Shortcut definition</param>
        Task SaveShortcutAsync(ShortcutDefinition shortcut);
        
        /// <summary>
        /// Resets all shortcuts to default values
        /// </summary>
        Task ResetToDefaultsAsync();
    }
}
