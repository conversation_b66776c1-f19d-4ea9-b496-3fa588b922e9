using InventoryManagement.DataAccess;
using InventoryManagement.Models;
using InventoryManagement.Models.Reports;
using InventoryManagement.Services.Export;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace InventoryManagement.Services.Reports
{
    /// <summary>
    /// Service for generating sales reports and analytics
    /// </summary>
    public class SalesReportService : ISalesReportService
    {
        private readonly ApplicationDbContext _context;
        private readonly ILogger<SalesReportService> _logger;
        private readonly IExportService _exportService;

        public SalesReportService(
            ApplicationDbContext context,
            ILogger<SalesReportService> logger,
            IExportService exportService)
        {
            _context = context;
            _logger = logger;
            _exportService = exportService;
        }

        public async Task<SalesReport> GenerateSalesReportAsync(SalesReportParameters parameters)
        {
            try
            {
                _logger.LogInformation("Generating sales report for period {FromDate} to {ToDate}", parameters.FromDate, parameters.ToDate);

                var report = new SalesReport
                {
                    FromDate = parameters.FromDate,
                    ToDate = parameters.ToDate,
                    ReportPeriod = $"{parameters.FromDate:yyyy-MM-dd} to {parameters.ToDate:yyyy-MM-dd}",
                    GeneratedBy = "System" // This should come from current user context
                };

                // Get base transaction data
                var transactions = await GetTransactionsForPeriodAsync(parameters.FromDate, parameters.ToDate, parameters.LocationIds, parameters.CashierIds);

                // Calculate summary metrics
                await CalculateSummaryMetricsAsync(report, transactions);

                // Generate detailed analytics
                if (parameters.IncludeHourlyBreakdown)
                {
                    report.HourlySalesData = await GetHourlySalesBreakdownAsync(parameters.FromDate, parameters.LocationIds);
                }

                report.DailySalesData = await GetSalesTrendsAsync(parameters.FromDate, parameters.ToDate, parameters.Grouping);
                report.TopSellingItems = await GetTopSellingItemsAsync(parameters.FromDate, parameters.ToDate, parameters.TopItemsCount);
                report.CategorySalesData = await GetSalesByCategoryAsync(parameters.FromDate, parameters.ToDate);

                if (parameters.IncludeCashierPerformance)
                {
                    report.CashierPerformanceData = await GetCashierPerformanceAsync(parameters.FromDate, parameters.ToDate, parameters.CashierIds);
                }

                if (parameters.IncludeCustomerAnalytics)
                {
                    var customerAnalytics = await GetCustomerAnalyticsAsync(parameters.FromDate, parameters.ToDate, parameters.TopCustomersCount);
                    report.UniqueCustomers = customerAnalytics.UniqueCustomers;
                    report.NewCustomers = customerAnalytics.NewCustomers;
                    report.ReturningCustomers = customerAnalytics.ReturningCustomers;
                    report.TopCustomers = customerAnalytics.TopCustomers;
                }

                if (parameters.IncludeDiscounts)
                {
                    report.DiscountUsageData = await GetDiscountUsageAnalyticsAsync(parameters.FromDate, parameters.ToDate);
                }

                if (parameters.IncludeRefunds)
                {
                    report.ReturnReasonData = await GetReturnAnalyticsAsync(parameters.FromDate, parameters.ToDate);
                    report.ReturnRate = CalculateReturnRate(report.TotalTransactions, report.TotalRefundTransactions);
                }

                _logger.LogInformation("Sales report generated successfully with {TransactionCount} transactions", report.TotalTransactions);
                return report;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating sales report");
                throw;
            }
        }

        public async Task<DailySales> GenerateDailySalesSummaryAsync(DateTime date, List<int>? locationIds = null)
        {
            try
            {
                var startDate = date.Date;
                var endDate = startDate.AddDays(1).AddTicks(-1);

                var transactions = await GetTransactionsForPeriodAsync(startDate, endDate, locationIds);

                var dailySales = new DailySales
                {
                    Date = date,
                    DayOfWeek = date.DayOfWeek.ToString(),
                    Sales = transactions.Where(t => t.Type == TransactionType.Sale).Sum(t => t.Total),
                    TransactionCount = transactions.Count(t => t.Type == TransactionType.Sale),
                    ItemsSold = transactions.Where(t => t.Type == TransactionType.Sale).SelectMany(t => t.Items).Sum(i => i.Quantity)
                };

                dailySales.AverageTransactionValue = dailySales.TransactionCount > 0 ? dailySales.Sales / dailySales.TransactionCount : 0;

                return dailySales;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating daily sales summary for {Date}", date);
                throw;
            }
        }

        public async Task<SalesReport> GenerateSalesReportByDateRangeAsync(DateTime fromDate, DateTime toDate, SalesReportGrouping grouping = SalesReportGrouping.Daily)
        {
            var parameters = new SalesReportParameters
            {
                FromDate = fromDate,
                ToDate = toDate,
                Grouping = grouping,
                IncludeCashierPerformance = true,
                IncludeCustomerAnalytics = true,
                IncludeDiscounts = true,
                IncludeRefunds = true
            };

            return await GenerateSalesReportAsync(parameters);
        }

        public async Task<List<TopSellingItem>> GetTopSellingItemsAsync(DateTime fromDate, DateTime toDate, int count = 10, string? categoryFilter = null)
        {
            try
            {
                var query = _context.TransactionItems
                    .Include(ti => ti.Transaction)
                    .Include(ti => ti.Item)
                    .ThenInclude(i => i.Category)
                    .Where(ti => ti.Transaction.TransactionDate >= fromDate && 
                                ti.Transaction.TransactionDate <= toDate &&
                                ti.Transaction.Type == TransactionType.Sale);

                if (!string.IsNullOrEmpty(categoryFilter))
                {
                    query = query.Where(ti => ti.Item.Category.Name == categoryFilter);
                }

                var topItems = await query
                    .GroupBy(ti => new { ti.ItemId, ti.Item.Name, ti.Item.ItemCode, ti.Item.Category.Name })
                    .Select(g => new TopSellingItem
                    {
                        ItemId = g.Key.ItemId,
                        ItemName = g.Key.Name,
                        ItemCode = g.Key.ItemCode,
                        Category = g.Key.Name,
                        QuantitySold = g.Sum(ti => ti.Quantity),
                        TotalRevenue = g.Sum(ti => ti.Quantity * ti.Price),
                        TransactionCount = g.Count(),
                        AveragePrice = g.Average(ti => ti.Price)
                    })
                    .OrderByDescending(item => item.QuantitySold)
                    .Take(count)
                    .ToListAsync();

                return topItems;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting top selling items");
                throw;
            }
        }

        public async Task<List<CashierPerformance>> GetCashierPerformanceAsync(DateTime fromDate, DateTime toDate, List<int>? cashierIds = null)
        {
            try
            {
                var query = _context.Transactions
                    .Include(t => t.User)
                    .Include(t => t.Items)
                    .Where(t => t.TransactionDate >= fromDate && t.TransactionDate <= toDate);

                if (cashierIds != null && cashierIds.Any())
                {
                    query = query.Where(t => cashierIds.Contains(t.UserId));
                }

                var cashierData = await query
                    .GroupBy(t => new { t.UserId, t.User.Username })
                    .Select(g => new CashierPerformance
                    {
                        CashierId = g.Key.UserId,
                        CashierName = g.Key.Username,
                        TotalSales = g.Where(t => t.Type == TransactionType.Sale).Sum(t => t.Total),
                        TransactionCount = g.Count(t => t.Type == TransactionType.Sale),
                        ItemsSold = g.Where(t => t.Type == TransactionType.Sale).SelectMany(t => t.Items).Sum(i => i.Quantity),
                        TotalDiscountsGiven = g.Where(t => t.Type == TransactionType.Sale).Sum(t => t.DiscountAmount),
                        RefundsProcessed = g.Count(t => t.Type == TransactionType.Return),
                        RefundAmount = g.Where(t => t.Type == TransactionType.Return).Sum(t => t.Total)
                    })
                    .ToListAsync();

                // Calculate derived metrics
                foreach (var cashier in cashierData)
                {
                    cashier.AverageTransactionValue = cashier.TransactionCount > 0 ? cashier.TotalSales / cashier.TransactionCount : 0;
                    // Note: TotalWorkTime would need to be calculated from shift/time tracking data
                    cashier.TotalWorkTime = TimeSpan.FromHours(8); // Placeholder
                    cashier.SalesPerHour = (decimal)(cashier.TotalSales / cashier.TotalWorkTime.TotalHours);
                    cashier.TransactionsPerHour = (decimal)(cashier.TransactionCount / cashier.TotalWorkTime.TotalHours);
                }

                return cashierData.OrderByDescending(c => c.TotalSales).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting cashier performance");
                throw;
            }
        }

        public async Task<List<CategorySales>> GetSalesByCategoryAsync(DateTime fromDate, DateTime toDate)
        {
            try
            {
                var categorySales = await _context.TransactionItems
                    .Include(ti => ti.Transaction)
                    .Include(ti => ti.Item)
                    .ThenInclude(i => i.Category)
                    .Where(ti => ti.Transaction.TransactionDate >= fromDate && 
                                ti.Transaction.TransactionDate <= toDate &&
                                ti.Transaction.Type == TransactionType.Sale)
                    .GroupBy(ti => ti.Item.Category.Name)
                    .Select(g => new CategorySales
                    {
                        CategoryName = g.Key,
                        TotalSales = g.Sum(ti => ti.Quantity * ti.Price),
                        ItemsSold = g.Sum(ti => ti.Quantity),
                        TransactionCount = g.Select(ti => ti.TransactionId).Distinct().Count(),
                        AverageItemPrice = g.Average(ti => ti.Price)
                    })
                    .ToListAsync();

                // Calculate percentages
                var totalSales = categorySales.Sum(cs => cs.TotalSales);
                foreach (var category in categorySales)
                {
                    category.PercentageOfTotalSales = totalSales > 0 ? (category.TotalSales / totalSales) * 100 : 0;
                }

                return categorySales.OrderByDescending(cs => cs.TotalSales).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting sales by category");
                throw;
            }
        }

        public async Task<List<HourlySales>> GetHourlySalesBreakdownAsync(DateTime date, List<int>? locationIds = null)
        {
            try
            {
                var startDate = date.Date;
                var endDate = startDate.AddDays(1).AddTicks(-1);

                var query = _context.Transactions
                    .Where(t => t.TransactionDate >= startDate &&
                               t.TransactionDate <= endDate &&
                               t.Type == TransactionType.Sale);

                if (locationIds != null && locationIds.Any())
                {
                    query = query.Where(t => locationIds.Contains(t.LocationId));
                }

                var hourlySales = await query
                    .GroupBy(t => t.TransactionDate.Hour)
                    .Select(g => new HourlySales
                    {
                        Hour = g.Key,
                        TimeRange = $"{g.Key:D2}:00 - {(g.Key + 1):D2}:00",
                        Sales = g.Sum(t => t.Total),
                        TransactionCount = g.Count(),
                        AverageTransactionValue = g.Average(t => t.Total)
                    })
                    .OrderBy(hs => hs.Hour)
                    .ToListAsync();

                return hourlySales;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting hourly sales breakdown for {Date}", date);
                throw;
            }
        }

        public async Task<List<DailySales>> GetSalesTrendsAsync(DateTime fromDate, DateTime toDate, SalesReportGrouping grouping)
        {
            try
            {
                var query = _context.Transactions
                    .Where(t => t.TransactionDate >= fromDate &&
                               t.TransactionDate <= toDate &&
                               t.Type == TransactionType.Sale);

                List<DailySales> salesTrends;

                switch (grouping)
                {
                    case SalesReportGrouping.Daily:
                        salesTrends = await query
                            .GroupBy(t => t.TransactionDate.Date)
                            .Select(g => new DailySales
                            {
                                Date = g.Key,
                                DayOfWeek = g.Key.DayOfWeek.ToString(),
                                Sales = g.Sum(t => t.Total),
                                TransactionCount = g.Count(),
                                ItemsSold = g.SelectMany(t => t.Items).Sum(i => i.Quantity),
                                AverageTransactionValue = g.Average(t => t.Total)
                            })
                            .OrderBy(ds => ds.Date)
                            .ToListAsync();
                        break;

                    case SalesReportGrouping.Weekly:
                        salesTrends = await query
                            .GroupBy(t => new { Year = t.TransactionDate.Year, Week = GetWeekOfYear(t.TransactionDate) })
                            .Select(g => new DailySales
                            {
                                Date = GetFirstDayOfWeek(g.Key.Year, g.Key.Week),
                                DayOfWeek = "Week",
                                Sales = g.Sum(t => t.Total),
                                TransactionCount = g.Count(),
                                ItemsSold = g.SelectMany(t => t.Items).Sum(i => i.Quantity),
                                AverageTransactionValue = g.Average(t => t.Total)
                            })
                            .OrderBy(ds => ds.Date)
                            .ToListAsync();
                        break;

                    case SalesReportGrouping.Monthly:
                        salesTrends = await query
                            .GroupBy(t => new { t.TransactionDate.Year, t.TransactionDate.Month })
                            .Select(g => new DailySales
                            {
                                Date = new DateTime(g.Key.Year, g.Key.Month, 1),
                                DayOfWeek = "Month",
                                Sales = g.Sum(t => t.Total),
                                TransactionCount = g.Count(),
                                ItemsSold = g.SelectMany(t => t.Items).Sum(i => i.Quantity),
                                AverageTransactionValue = g.Average(t => t.Total)
                            })
                            .OrderBy(ds => ds.Date)
                            .ToListAsync();
                        break;

                    default:
                        salesTrends = new List<DailySales>();
                        break;
                }

                return salesTrends;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting sales trends");
                throw;
            }
        }

        public async Task<List<DiscountUsage>> GetDiscountUsageAnalyticsAsync(DateTime fromDate, DateTime toDate)
        {
            try
            {
                var discountUsage = await _context.DiscountApplications
                    .Include(da => da.Discount)
                    .Include(da => da.Transaction)
                    .Where(da => da.AppliedDate >= fromDate && da.AppliedDate <= toDate)
                    .GroupBy(da => new { da.DiscountId, da.Discount.DiscountCode, da.Discount.Name })
                    .Select(g => new DiscountUsage
                    {
                        DiscountId = g.Key.DiscountId,
                        DiscountCode = g.Key.DiscountCode,
                        DiscountName = g.Key.Name,
                        UsageCount = g.Count(),
                        TotalDiscountAmount = g.Sum(da => da.DiscountAmount),
                        AverageDiscountAmount = g.Average(da => da.DiscountAmount),
                        UniqueCustomers = g.Where(da => da.Transaction.CustomerId.HasValue)
                                          .Select(da => da.Transaction.CustomerId.Value)
                                          .Distinct()
                                          .Count()
                    })
                    .OrderByDescending(du => du.TotalDiscountAmount)
                    .ToListAsync();

                // Calculate percentages
                var totalTransactions = await _context.Transactions
                    .CountAsync(t => t.TransactionDate >= fromDate &&
                                    t.TransactionDate <= toDate &&
                                    t.Type == TransactionType.Sale);

                foreach (var discount in discountUsage)
                {
                    discount.PercentageOfTransactions = totalTransactions > 0 ?
                        (decimal)discount.UsageCount / totalTransactions * 100 : 0;
                }

                return discountUsage;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting discount usage analytics");
                throw;
            }
        }

        public async Task<List<ReturnReason>> GetReturnAnalyticsAsync(DateTime fromDate, DateTime toDate)
        {
            try
            {
                var returnReasons = await _context.ReturnTransactions
                    .Where(rt => rt.ReturnDate >= fromDate && rt.ReturnDate <= toDate)
                    .GroupBy(rt => rt.ReturnReason)
                    .Select(g => new ReturnReason
                    {
                        Reason = g.Key,
                        Count = g.Count(),
                        TotalAmount = g.Sum(rt => rt.RefundAmount)
                    })
                    .OrderByDescending(rr => rr.Count)
                    .ToListAsync();

                // Calculate percentages
                var totalReturns = returnReasons.Sum(rr => rr.Count);
                foreach (var reason in returnReasons)
                {
                    reason.PercentageOfReturns = totalReturns > 0 ? (decimal)reason.Count / totalReturns * 100 : 0;
                }

                return returnReasons;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting return analytics");
                throw;
            }
        }

        public async Task<(int UniqueCustomers, int NewCustomers, int ReturningCustomers, List<TopCustomer> TopCustomers)> GetCustomerAnalyticsAsync(DateTime fromDate, DateTime toDate, int count = 10)
        {
            try
            {
                var transactions = await _context.Transactions
                    .Include(t => t.Customer)
                    .Where(t => t.TransactionDate >= fromDate &&
                               t.TransactionDate <= toDate &&
                               t.Type == TransactionType.Sale &&
                               t.CustomerId.HasValue)
                    .ToListAsync();

                var uniqueCustomers = transactions.Select(t => t.CustomerId.Value).Distinct().Count();

                // Get new customers (first transaction in this period)
                var newCustomers = 0;
                var returningCustomers = 0;

                var customerIds = transactions.Select(t => t.CustomerId.Value).Distinct();
                foreach (var customerId in customerIds)
                {
                    var firstTransaction = await _context.Transactions
                        .Where(t => t.CustomerId == customerId)
                        .OrderBy(t => t.TransactionDate)
                        .FirstOrDefaultAsync();

                    if (firstTransaction != null && firstTransaction.TransactionDate >= fromDate)
                    {
                        newCustomers++;
                    }
                    else
                    {
                        returningCustomers++;
                    }
                }

                // Get top customers
                var topCustomers = transactions
                    .GroupBy(t => new { t.CustomerId, t.Customer.Name, t.Customer.CustomerNumber, t.Customer.CustomerType })
                    .Select(g => new TopCustomer
                    {
                        CustomerId = g.Key.CustomerId.Value,
                        CustomerName = g.Key.Name,
                        CustomerNumber = g.Key.CustomerNumber,
                        CustomerType = g.Key.CustomerType,
                        TotalPurchases = g.Sum(t => t.Total),
                        TransactionCount = g.Count(),
                        AverageTransactionValue = g.Average(t => t.Total),
                        LastPurchaseDate = g.Max(t => t.TransactionDate)
                    })
                    .OrderByDescending(tc => tc.TotalPurchases)
                    .Take(count)
                    .ToList();

                return (uniqueCustomers, newCustomers, returningCustomers, topCustomers);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting customer analytics");
                throw;
            }
        }
