using InventoryManagement.DataAccess;
using InventoryManagement.Models;
using InventoryManagement.Models.Reports;
using InventoryManagement.Services.Export;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace InventoryManagement.Services.Reports
{
    /// <summary>
    /// Service for generating sales reports and analytics
    /// </summary>
    public class SalesReportService : ISalesReportService
    {
        private readonly ApplicationDbContext _context;
        private readonly ILogger<SalesReportService> _logger;
        private readonly IExportService _exportService;

        public SalesReportService(
            ApplicationDbContext context,
            ILogger<SalesReportService> logger,
            IExportService exportService)
        {
            _context = context;
            _logger = logger;
            _exportService = exportService;
        }

        public async Task<SalesReport> GenerateSalesReportAsync(SalesReportParameters parameters)
        {
            try
            {
                _logger.LogInformation("Generating sales report for period {FromDate} to {ToDate}", parameters.FromDate, parameters.ToDate);

                var report = new SalesReport
                {
                    FromDate = parameters.FromDate,
                    ToDate = parameters.ToDate,
                    ReportPeriod = $"{parameters.FromDate:yyyy-MM-dd} to {parameters.ToDate:yyyy-MM-dd}",
                    GeneratedBy = "System" // This should come from current user context
                };

                // Get base transaction data
                var transactions = await GetTransactionsForPeriodAsync(parameters.FromDate, parameters.ToDate, parameters.LocationIds, parameters.CashierIds);

                // Calculate summary metrics
                await CalculateSummaryMetricsAsync(report, transactions);

                // Generate detailed analytics
                if (parameters.IncludeHourlyBreakdown)
                {
                    report.HourlySalesData = await GetHourlySalesBreakdownAsync(parameters.FromDate, parameters.LocationIds);
                }

                report.DailySalesData = await GetSalesTrendsAsync(parameters.FromDate, parameters.ToDate, parameters.Grouping);
                report.TopSellingItems = await GetTopSellingItemsAsync(parameters.FromDate, parameters.ToDate, parameters.TopItemsCount);
                report.CategorySalesData = await GetSalesByCategoryAsync(parameters.FromDate, parameters.ToDate);

                if (parameters.IncludeCashierPerformance)
                {
                    report.CashierPerformanceData = await GetCashierPerformanceAsync(parameters.FromDate, parameters.ToDate, parameters.CashierIds);
                }

                if (parameters.IncludeCustomerAnalytics)
                {
                    var customerAnalytics = await GetCustomerAnalyticsAsync(parameters.FromDate, parameters.ToDate, parameters.TopCustomersCount);
                    report.UniqueCustomers = customerAnalytics.UniqueCustomers;
                    report.NewCustomers = customerAnalytics.NewCustomers;
                    report.ReturningCustomers = customerAnalytics.ReturningCustomers;
                    report.TopCustomers = customerAnalytics.TopCustomers;
                }

                if (parameters.IncludeDiscounts)
                {
                    report.DiscountUsageData = await GetDiscountUsageAnalyticsAsync(parameters.FromDate, parameters.ToDate);
                }

                if (parameters.IncludeRefunds)
                {
                    report.ReturnReasonData = await GetReturnAnalyticsAsync(parameters.FromDate, parameters.ToDate);
                    report.ReturnRate = CalculateReturnRate(report.TotalTransactions, report.TotalRefundTransactions);
                }

                _logger.LogInformation("Sales report generated successfully with {TransactionCount} transactions", report.TotalTransactions);
                return report;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating sales report");
                throw;
            }
        }

        public async Task<DailySales> GenerateDailySalesSummaryAsync(DateTime date, List<int>? locationIds = null)
        {
            try
            {
                var startDate = date.Date;
                var endDate = startDate.AddDays(1).AddTicks(-1);

                var transactions = await GetTransactionsForPeriodAsync(startDate, endDate, locationIds);

                var dailySales = new DailySales
                {
                    Date = date,
                    DayOfWeek = date.DayOfWeek.ToString(),
                    Sales = transactions.Where(t => t.Type == TransactionType.Sale).Sum(t => t.Total),
                    TransactionCount = transactions.Count(t => t.Type == TransactionType.Sale),
                    ItemsSold = transactions.Where(t => t.Type == TransactionType.Sale).SelectMany(t => t.Items).Sum(i => i.Quantity)
                };

                dailySales.AverageTransactionValue = dailySales.TransactionCount > 0 ? dailySales.Sales / dailySales.TransactionCount : 0;

                return dailySales;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating daily sales summary for {Date}", date);
                throw;
            }
        }

        public async Task<SalesReport> GenerateSalesReportByDateRangeAsync(DateTime fromDate, DateTime toDate, SalesReportGrouping grouping = SalesReportGrouping.Daily)
        {
            var parameters = new SalesReportParameters
            {
                FromDate = fromDate,
                ToDate = toDate,
                Grouping = grouping,
                IncludeCashierPerformance = true,
                IncludeCustomerAnalytics = true,
                IncludeDiscounts = true,
                IncludeRefunds = true
            };

            return await GenerateSalesReportAsync(parameters);
        }

        public async Task<List<TopSellingItem>> GetTopSellingItemsAsync(DateTime fromDate, DateTime toDate, int count = 10, string? categoryFilter = null)
        {
            try
            {
                var query = _context.TransactionItems
                    .Include(ti => ti.Transaction)
                    .Include(ti => ti.Item)
                    .ThenInclude(i => i.Category)
                    .Where(ti => ti.Transaction.TransactionDate >= fromDate && 
                                ti.Transaction.TransactionDate <= toDate &&
                                ti.Transaction.Type == TransactionType.Sale);

                if (!string.IsNullOrEmpty(categoryFilter))
                {
                    query = query.Where(ti => ti.Item.Category.Name == categoryFilter);
                }

                var topItems = await query
                    .GroupBy(ti => new { ti.ItemId, ti.Item.Name, ti.Item.ItemCode, ti.Item.Category.Name })
                    .Select(g => new TopSellingItem
                    {
                        ItemId = g.Key.ItemId,
                        ItemName = g.Key.Name,
                        ItemCode = g.Key.ItemCode,
                        Category = g.Key.Name,
                        QuantitySold = g.Sum(ti => ti.Quantity),
                        TotalRevenue = g.Sum(ti => ti.Quantity * ti.Price),
                        TransactionCount = g.Count(),
                        AveragePrice = g.Average(ti => ti.Price)
                    })
                    .OrderByDescending(item => item.QuantitySold)
                    .Take(count)
                    .ToListAsync();

                return topItems;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting top selling items");
                throw;
            }
        }

        public async Task<List<CashierPerformance>> GetCashierPerformanceAsync(DateTime fromDate, DateTime toDate, List<int>? cashierIds = null)
        {
            try
            {
                var query = _context.Transactions
                    .Include(t => t.User)
                    .Include(t => t.Items)
                    .Where(t => t.TransactionDate >= fromDate && t.TransactionDate <= toDate);

                if (cashierIds != null && cashierIds.Any())
                {
                    query = query.Where(t => cashierIds.Contains(t.UserId));
                }

                var cashierData = await query
                    .GroupBy(t => new { t.UserId, t.User.Username })
                    .Select(g => new CashierPerformance
                    {
                        CashierId = g.Key.UserId,
                        CashierName = g.Key.Username,
                        TotalSales = g.Where(t => t.Type == TransactionType.Sale).Sum(t => t.Total),
                        TransactionCount = g.Count(t => t.Type == TransactionType.Sale),
                        ItemsSold = g.Where(t => t.Type == TransactionType.Sale).SelectMany(t => t.Items).Sum(i => i.Quantity),
                        TotalDiscountsGiven = g.Where(t => t.Type == TransactionType.Sale).Sum(t => t.DiscountAmount),
                        RefundsProcessed = g.Count(t => t.Type == TransactionType.Return),
                        RefundAmount = g.Where(t => t.Type == TransactionType.Return).Sum(t => t.Total)
                    })
                    .ToListAsync();

                // Calculate derived metrics
                foreach (var cashier in cashierData)
                {
                    cashier.AverageTransactionValue = cashier.TransactionCount > 0 ? cashier.TotalSales / cashier.TransactionCount : 0;
                    // Note: TotalWorkTime would need to be calculated from shift/time tracking data
                    cashier.TotalWorkTime = TimeSpan.FromHours(8); // Placeholder
                    cashier.SalesPerHour = (decimal)(cashier.TotalSales / cashier.TotalWorkTime.TotalHours);
                    cashier.TransactionsPerHour = (decimal)(cashier.TransactionCount / cashier.TotalWorkTime.TotalHours);
                }

                return cashierData.OrderByDescending(c => c.TotalSales).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting cashier performance");
                throw;
            }
        }

        public async Task<List<CategorySales>> GetSalesByCategoryAsync(DateTime fromDate, DateTime toDate)
        {
            try
            {
                var categorySales = await _context.TransactionItems
                    .Include(ti => ti.Transaction)
                    .Include(ti => ti.Item)
                    .ThenInclude(i => i.Category)
                    .Where(ti => ti.Transaction.TransactionDate >= fromDate && 
                                ti.Transaction.TransactionDate <= toDate &&
                                ti.Transaction.Type == TransactionType.Sale)
                    .GroupBy(ti => ti.Item.Category.Name)
                    .Select(g => new CategorySales
                    {
                        CategoryName = g.Key,
                        TotalSales = g.Sum(ti => ti.Quantity * ti.Price),
                        ItemsSold = g.Sum(ti => ti.Quantity),
                        TransactionCount = g.Select(ti => ti.TransactionId).Distinct().Count(),
                        AverageItemPrice = g.Average(ti => ti.Price)
                    })
                    .ToListAsync();

                // Calculate percentages
                var totalSales = categorySales.Sum(cs => cs.TotalSales);
                foreach (var category in categorySales)
                {
                    category.PercentageOfTotalSales = totalSales > 0 ? (category.TotalSales / totalSales) * 100 : 0;
                }

                return categorySales.OrderByDescending(cs => cs.TotalSales).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting sales by category");
                throw;
            }
        }

        public async Task<List<HourlySales>> GetHourlySalesBreakdownAsync(DateTime date, List<int>? locationIds = null)
        {
            try
            {
                var startDate = date.Date;
                var endDate = startDate.AddDays(1).AddTicks(-1);

                var query = _context.Transactions
                    .Where(t => t.TransactionDate >= startDate &&
                               t.TransactionDate <= endDate &&
                               t.Type == TransactionType.Sale);

                if (locationIds != null && locationIds.Any())
                {
                    query = query.Where(t => locationIds.Contains(t.LocationId));
                }

                var hourlySales = await query
                    .GroupBy(t => t.TransactionDate.Hour)
                    .Select(g => new HourlySales
                    {
                        Hour = g.Key,
                        TimeRange = $"{g.Key:D2}:00 - {(g.Key + 1):D2}:00",
                        Sales = g.Sum(t => t.Total),
                        TransactionCount = g.Count(),
                        AverageTransactionValue = g.Average(t => t.Total)
                    })
                    .OrderBy(hs => hs.Hour)
                    .ToListAsync();

                return hourlySales;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting hourly sales breakdown for {Date}", date);
                throw;
            }
        }

        public async Task<List<DailySales>> GetSalesTrendsAsync(DateTime fromDate, DateTime toDate, SalesReportGrouping grouping)
        {
            try
            {
                var query = _context.Transactions
                    .Where(t => t.TransactionDate >= fromDate &&
                               t.TransactionDate <= toDate &&
                               t.Type == TransactionType.Sale);

                var salesTrends = await query
                    .GroupBy(t => t.TransactionDate.Date)
                    .Select(g => new DailySales
                    {
                        Date = g.Key,
                        DayOfWeek = g.Key.DayOfWeek.ToString(),
                        Sales = g.Sum(t => t.Total),
                        TransactionCount = g.Count(),
                        ItemsSold = g.SelectMany(t => t.Items).Sum(i => i.Quantity),
                        AverageTransactionValue = g.Average(t => t.Total)
                    })
                    .OrderBy(ds => ds.Date)
                    .ToListAsync();

                return salesTrends;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting sales trends");
                throw;
            }
        }

        public async Task<List<DiscountUsage>> GetDiscountUsageAnalyticsAsync(DateTime fromDate, DateTime toDate)
        {
            try
            {
                var discountUsage = await _context.DiscountApplications
                    .Include(da => da.Discount)
                    .Include(da => da.Transaction)
                    .Where(da => da.AppliedDate >= fromDate && da.AppliedDate <= toDate)
                    .GroupBy(da => new { da.DiscountId, da.Discount.DiscountCode, da.Discount.Name })
                    .Select(g => new DiscountUsage
                    {
                        DiscountId = g.Key.DiscountId,
                        DiscountCode = g.Key.DiscountCode,
                        DiscountName = g.Key.Name,
                        UsageCount = g.Count(),
                        TotalDiscountAmount = g.Sum(da => da.DiscountAmount),
                        AverageDiscountAmount = g.Average(da => da.DiscountAmount),
                        UniqueCustomers = g.Where(da => da.Transaction.CustomerId.HasValue)
                                          .Select(da => da.Transaction.CustomerId.Value)
                                          .Distinct()
                                          .Count()
                    })
                    .OrderByDescending(du => du.TotalDiscountAmount)
                    .ToListAsync();

                return discountUsage;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting discount usage analytics");
                throw;
            }
        }

        public async Task<List<ReturnReason>> GetReturnAnalyticsAsync(DateTime fromDate, DateTime toDate)
        {
            try
            {
                var returnReasons = await _context.ReturnTransactions
                    .Where(rt => rt.ReturnDate >= fromDate && rt.ReturnDate <= toDate)
                    .GroupBy(rt => rt.ReturnReason)
                    .Select(g => new ReturnReason
                    {
                        Reason = g.Key,
                        Count = g.Count(),
                        TotalAmount = g.Sum(rt => rt.RefundAmount)
                    })
                    .OrderByDescending(rr => rr.Count)
                    .ToListAsync();

                var totalReturns = returnReasons.Sum(rr => rr.Count);
                foreach (var reason in returnReasons)
                {
                    reason.PercentageOfReturns = totalReturns > 0 ? (decimal)reason.Count / totalReturns * 100 : 0;
                }

                return returnReasons;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting return analytics");
                throw;
            }
        }

        public async Task<(int UniqueCustomers, int NewCustomers, int ReturningCustomers, List<TopCustomer> TopCustomers)> GetCustomerAnalyticsAsync(DateTime fromDate, DateTime toDate, int count = 10)
        {
            try
            {
                var transactions = await _context.Transactions
                    .Include(t => t.Customer)
                    .Where(t => t.TransactionDate >= fromDate &&
                               t.TransactionDate <= toDate &&
                               t.Type == TransactionType.Sale &&
                               t.CustomerId.HasValue)
                    .ToListAsync();

                var uniqueCustomers = transactions.Select(t => t.CustomerId.Value).Distinct().Count();
                var newCustomers = 0;
                var returningCustomers = uniqueCustomers; // Simplified for now

                var topCustomers = transactions
                    .GroupBy(t => new { t.CustomerId, t.Customer.Name, t.Customer.CustomerNumber, t.Customer.CustomerType })
                    .Select(g => new TopCustomer
                    {
                        CustomerId = g.Key.CustomerId.Value,
                        CustomerName = g.Key.Name,
                        CustomerNumber = g.Key.CustomerNumber,
                        CustomerType = g.Key.CustomerType,
                        TotalPurchases = g.Sum(t => t.Total),
                        TransactionCount = g.Count(),
                        AverageTransactionValue = g.Average(t => t.Total),
                        LastPurchaseDate = g.Max(t => t.TransactionDate)
                    })
                    .OrderByDescending(tc => tc.TotalPurchases)
                    .Take(count)
                    .ToList();

                return (uniqueCustomers, newCustomers, returningCustomers, topCustomers);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting customer analytics");
                throw;
            }
        }

        public async Task<byte[]> ExportSalesReportAsync(SalesReport report, SalesReportFormat format)
        {
            try
            {
                return format switch
                {
                    SalesReportFormat.PDF => await _exportService.ExportToPdfAsync(report),
                    SalesReportFormat.Excel => await _exportService.ExportToExcelAsync(report),
                    SalesReportFormat.CSV => await _exportService.ExportToCsvAsync(report),
                    SalesReportFormat.JSON => await _exportService.ExportToJsonAsync(report),
                    _ => throw new ArgumentException($"Unsupported export format: {format}")
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error exporting sales report to {Format}", format);
                throw;
            }
        }

        public async Task<SalesSummaryDashboard> GetSalesSummaryForDashboardAsync(DateTime date)
        {
            try
            {
                var today = await GenerateDailySalesSummaryAsync(date);
                var yesterday = await GenerateDailySalesSummaryAsync(date.AddDays(-1));
                var topItems = await GetTopSellingItemsAsync(date, date, 5);
                var hourlyBreakdown = await GetHourlySalesBreakdownAsync(date);

                return new SalesSummaryDashboard
                {
                    Date = date,
                    TodaysSales = today.Sales,
                    YesterdaysSales = yesterday.Sales,
                    SalesGrowth = yesterday.Sales > 0 ? ((today.Sales - yesterday.Sales) / yesterday.Sales) * 100 : 0,
                    TodaysTransactions = today.TransactionCount,
                    YesterdaysTransactions = yesterday.TransactionCount,
                    AverageTransactionValue = today.AverageTransactionValue,
                    TopItems = topItems,
                    HourlyBreakdown = hourlyBreakdown
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting sales summary for dashboard");
                throw;
            }
        }

        public async Task<SalesComparison> CompareSalesPerformanceAsync(DateTime currentFromDate, DateTime currentToDate, DateTime previousFromDate, DateTime previousToDate)
        {
            try
            {
                var currentReport = await GenerateSalesReportByDateRangeAsync(currentFromDate, currentToDate);
                var previousReport = await GenerateSalesReportByDateRangeAsync(previousFromDate, previousToDate);

                return new SalesComparison
                {
                    CurrentPeriod = $"{currentFromDate:yyyy-MM-dd} to {currentToDate:yyyy-MM-dd}",
                    PreviousPeriod = $"{previousFromDate:yyyy-MM-dd} to {previousToDate:yyyy-MM-dd}",
                    CurrentSales = currentReport.NetSales,
                    PreviousSales = previousReport.NetSales,
                    SalesChange = currentReport.NetSales - previousReport.NetSales,
                    SalesChangePercentage = previousReport.NetSales > 0 ? ((currentReport.NetSales - previousReport.NetSales) / previousReport.NetSales) * 100 : 0,
                    CurrentTransactions = currentReport.TotalTransactions,
                    PreviousTransactions = previousReport.TotalTransactions,
                    TransactionChange = currentReport.TotalTransactions - previousReport.TotalTransactions,
                    TransactionChangePercentage = previousReport.TotalTransactions > 0 ? ((decimal)(currentReport.TotalTransactions - previousReport.TotalTransactions) / previousReport.TotalTransactions) * 100 : 0,
                    CurrentAverageTransaction = currentReport.AverageTransactionValue,
                    PreviousAverageTransaction = previousReport.AverageTransactionValue,
                    AverageTransactionChange = currentReport.AverageTransactionValue - previousReport.AverageTransactionValue,
                    AverageTransactionChangePercentage = previousReport.AverageTransactionValue > 0 ? ((currentReport.AverageTransactionValue - previousReport.AverageTransactionValue) / previousReport.AverageTransactionValue) * 100 : 0
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error comparing sales performance");
                throw;
            }
        }

        public async Task<List<SalesForecast>> GetSalesForecastAsync(int forecastDays = 7, int historicalDays = 30)
        {
            try
            {
                // Simple moving average forecast - in production you'd use more sophisticated algorithms
                var endDate = DateTime.Now.Date;
                var startDate = endDate.AddDays(-historicalDays);

                var historicalSales = await GetSalesTrendsAsync(startDate, endDate, SalesReportGrouping.Daily);
                var averageDailySales = historicalSales.Average(s => s.Sales);

                var forecasts = new List<SalesForecast>();
                for (int i = 1; i <= forecastDays; i++)
                {
                    var forecastDate = endDate.AddDays(i);
                    forecasts.Add(new SalesForecast
                    {
                        Date = forecastDate,
                        ForecastedSales = averageDailySales,
                        ConfidenceLevel = 70, // Simplified confidence level
                        LowerBound = averageDailySales * 0.8m,
                        UpperBound = averageDailySales * 1.2m,
                        ForecastMethod = "Moving Average",
                        Factors = new List<string> { "Historical average", "Day of week pattern" }
                    });
                }

                return forecasts;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating sales forecast");
                throw;
            }
        }

        // Helper methods
        private async Task<List<Transaction>> GetTransactionsForPeriodAsync(DateTime fromDate, DateTime toDate, List<int>? locationIds = null, List<int>? cashierIds = null)
        {
            var query = _context.Transactions
                .Include(t => t.Items)
                .ThenInclude(ti => ti.Item)
                .Include(t => t.Customer)
                .Include(t => t.User)
                .Where(t => t.TransactionDate >= fromDate && t.TransactionDate <= toDate);

            if (locationIds != null && locationIds.Any())
            {
                query = query.Where(t => locationIds.Contains(t.LocationId));
            }

            if (cashierIds != null && cashierIds.Any())
            {
                query = query.Where(t => cashierIds.Contains(t.UserId));
            }

            return await query.ToListAsync();
        }

        private async Task CalculateSummaryMetricsAsync(SalesReport report, List<Transaction> transactions)
        {
            var salesTransactions = transactions.Where(t => t.Type == TransactionType.Sale).ToList();
            var refundTransactions = transactions.Where(t => t.Type == TransactionType.Return).ToList();

            report.TotalSales = salesTransactions.Sum(t => t.Total);
            report.TotalRefunds = refundTransactions.Sum(t => t.Total);
            report.NetSales = report.TotalSales - report.TotalRefunds;
            report.TotalTransactions = salesTransactions.Count;
            report.TotalRefundTransactions = refundTransactions.Count;
            report.AverageTransactionValue = report.TotalTransactions > 0 ? report.TotalSales / report.TotalTransactions : 0;
            report.TotalItemsSold = salesTransactions.SelectMany(t => t.Items).Sum(i => i.Quantity);
            report.TotalDiscountsGiven = salesTransactions.Sum(t => t.DiscountAmount);

            // Payment method breakdown
            report.CashSales = salesTransactions.Where(t => t.PaymentMethod == "Cash").Sum(t => t.Total);
            report.CreditSales = salesTransactions.Where(t => t.PaymentMethod == "Credit").Sum(t => t.Total);
            report.OtherPaymentSales = salesTransactions.Where(t => t.PaymentMethod != "Cash" && t.PaymentMethod != "Credit").Sum(t => t.Total);
        }

        private decimal CalculateReturnRate(int totalTransactions, int totalRefundTransactions)
        {
            return totalTransactions > 0 ? (decimal)totalRefundTransactions / totalTransactions * 100 : 0;
        }
    }
}
