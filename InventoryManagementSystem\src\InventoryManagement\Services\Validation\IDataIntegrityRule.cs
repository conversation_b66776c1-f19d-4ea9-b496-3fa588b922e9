using System.Collections.Generic;
using System.Threading.Tasks;

namespace InventoryManagement.Services.Validation
{
    /// <summary>
    /// Interface for data integrity validation rules
    /// </summary>
    public interface IDataIntegrityRule
    {
        /// <summary>
        /// Name of the rule
        /// </summary>
        string Name { get; }
        
        /// <summary>
        /// Description of what the rule validates
        /// </summary>
        string Description { get; }
        
        /// <summary>
        /// Type of entity this rule validates
        /// </summary>
        string EntityType { get; }
        
        /// <summary>
        /// Validates data integrity according to the rule's logic
        /// </summary>
        /// <returns>List of integrity issues found</returns>
        Task<List<DataIntegrityIssue>> ValidateAsync();
    }
}
