using InventoryManagement.DataAccess;
using InventoryManagement.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace InventoryManagement.Services
{
    /// <summary>
    /// Service for processing returns and refunds
    /// </summary>
    public class ReturnService : IReturnService
    {
        private readonly ApplicationDbContext _context;
        private readonly ILogger<ReturnService> _logger;
        private readonly IAuditService _auditService;
        private readonly IInventoryService _inventoryService;
        private readonly IFinancialService _financialService;
        private readonly IReceiptService _receiptService;

        public ReturnService(
            ApplicationDbContext context,
            ILogger<ReturnService> logger,
            IAuditService auditService,
            IInventoryService inventoryService,
            IFinancialService financialService,
            IReceiptService receiptService)
        {
            _context = context;
            _logger = logger;
            _auditService = auditService;
            _inventoryService = inventoryService;
            _financialService = financialService;
            _receiptService = receiptService;
        }

        public async Task<ReturnProcessingResult> ProcessReturnAsync(ReturnRequest returnRequest, int userId)
        {
            var result = new ReturnProcessingResult();

            try
            {
                _logger.LogInformation("Processing return for transaction {TransactionId}", returnRequest.OriginalTransactionId);

                // Validate the return request
                var validationResult = await ValidateReturnAsync(returnRequest.OriginalTransactionId, returnRequest.Items);
                if (!validationResult.IsValid)
                {
                    result.IsSuccessful = false;
                    result.Message = "Return validation failed";
                    result.Errors = validationResult.Errors;
                    result.Warnings = validationResult.Warnings;
                    return result;
                }

                // Get original transaction
                var originalTransaction = await _context.Transactions
                    .Include(t => t.Items)
                    .ThenInclude(ti => ti.Item)
                    .FirstOrDefaultAsync(t => t.Id == returnRequest.OriginalTransactionId);

                if (originalTransaction == null)
                {
                    result.IsSuccessful = false;
                    result.Message = "Original transaction not found";
                    return result;
                }

                // Calculate refund amount
                var refundAmount = await CalculateRefundAmountAsync(returnRequest.Items, originalTransaction);

                // Check if manager approval is required
                bool requiresApproval = DetermineIfApprovalRequired(refundAmount, returnRequest);

                using var transaction = await _context.Database.BeginTransactionAsync();

                try
                {
                    // Create return transaction
                    var returnTransaction = new ReturnTransaction
                    {
                        ReturnNumber = await GenerateReturnNumberAsync(),
                        OriginalTransactionId = returnRequest.OriginalTransactionId,
                        CustomerId = returnRequest.CustomerId,
                        CashierId = userId,
                        ReturnDate = DateTime.Now,
                        ReturnReason = returnRequest.ReturnReason,
                        Notes = returnRequest.Notes,
                        RefundAmount = refundAmount,
                        RefundMethod = returnRequest.RefundMethod,
                        Status = requiresApproval ? ReturnStatus.Pending : ReturnStatus.Approved,
                        RequiredApproval = requiresApproval,
                        IsPartialReturn = IsPartialReturn(returnRequest.Items, originalTransaction)
                    };

                    _context.ReturnTransactions.Add(returnTransaction);
                    await _context.SaveChangesAsync();

                    // Create return items
                    foreach (var itemRequest in returnRequest.Items)
                    {
                        var returnItem = new ReturnItem
                        {
                            ReturnTransactionId = returnTransaction.Id,
                            ItemId = itemRequest.ItemId,
                            Quantity = itemRequest.Quantity,
                            OriginalPrice = itemRequest.OriginalPrice,
                            RefundPricePerUnit = itemRequest.RefundPricePerUnit,
                            RefundAmount = itemRequest.Quantity * itemRequest.RefundPricePerUnit,
                            ItemCondition = itemRequest.ItemCondition,
                            CanRestock = itemRequest.CanRestock,
                            ItemReturnReason = itemRequest.ItemReturnReason,
                            RestockLocationId = itemRequest.RestockLocationId
                        };

                        _context.ReturnItems.Add(returnItem);
                    }

                    await _context.SaveChangesAsync();

                    // If no approval required, complete the return immediately
                    if (!requiresApproval)
                    {
                        var completionResult = await CompleteReturnInternalAsync(returnTransaction.Id, userId);
                        if (!completionResult.IsSuccessful)
                        {
                            await transaction.RollbackAsync();
                            result.IsSuccessful = false;
                            result.Message = "Failed to complete return: " + completionResult.Message;
                            result.Errors = completionResult.Errors;
                            return result;
                        }
                    }

                    await transaction.CommitAsync();

                    // Log the return
                    await _auditService.LogEventAsync(
                        "Returns",
                        $"Created return {returnTransaction.ReturnNumber} for transaction {originalTransaction.Id}",
                        userId);

                    result.IsSuccessful = true;
                    result.Message = requiresApproval ? "Return created and pending approval" : "Return processed successfully";
                    result.ReturnTransaction = returnTransaction;
                    result.ReturnNumber = returnTransaction.ReturnNumber;
                    result.RefundAmount = refundAmount;
                    result.RequiresApproval = requiresApproval;
                    result.Warnings = validationResult.Warnings;

                    _logger.LogInformation("Return {ReturnNumber} processed successfully", returnTransaction.ReturnNumber);
                }
                catch (Exception ex)
                {
                    await transaction.RollbackAsync();
                    throw;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing return for transaction {TransactionId}", returnRequest.OriginalTransactionId);
                result.IsSuccessful = false;
                result.Message = "An error occurred while processing the return";
                result.Errors.Add(ex.Message);
            }

            return result;
        }

        public async Task<ReturnTransaction?> GetReturnByIdAsync(int returnId)
        {
            try
            {
                return await _context.ReturnTransactions
                    .Include(r => r.OriginalTransaction)
                    .Include(r => r.Customer)
                    .Include(r => r.Cashier)
                    .Include(r => r.ApprovedByUser)
                    .Include(r => r.ReturnItems)
                    .ThenInclude(ri => ri.Item)
                    .FirstOrDefaultAsync(r => r.Id == returnId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting return by ID {ReturnId}", returnId);
                throw;
            }
        }

        public async Task<ReturnTransaction?> GetReturnByNumberAsync(string returnNumber)
        {
            try
            {
                return await _context.ReturnTransactions
                    .Include(r => r.OriginalTransaction)
                    .Include(r => r.Customer)
                    .Include(r => r.Cashier)
                    .Include(r => r.ApprovedByUser)
                    .Include(r => r.ReturnItems)
                    .ThenInclude(ri => ri.Item)
                    .FirstOrDefaultAsync(r => r.ReturnNumber == returnNumber);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting return by number {ReturnNumber}", returnNumber);
                throw;
            }
        }

        public async Task<List<ReturnTransaction>> GetReturnsByTransactionAsync(int originalTransactionId)
        {
            try
            {
                return await _context.ReturnTransactions
                    .Include(r => r.Customer)
                    .Include(r => r.Cashier)
                    .Include(r => r.ReturnItems)
                    .Where(r => r.OriginalTransactionId == originalTransactionId)
                    .OrderByDescending(r => r.ReturnDate)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting returns for transaction {TransactionId}", originalTransactionId);
                throw;
            }
        }

        public async Task<List<ReturnTransaction>> GetReturnsByDateRangeAsync(DateTime fromDate, DateTime toDate, ReturnStatus? status = null)
        {
            try
            {
                var query = _context.ReturnTransactions
                    .Include(r => r.OriginalTransaction)
                    .Include(r => r.Customer)
                    .Include(r => r.Cashier)
                    .Where(r => r.ReturnDate >= fromDate && r.ReturnDate <= toDate);

                if (status.HasValue)
                {
                    query = query.Where(r => r.Status == status.Value);
                }

                return await query
                    .OrderByDescending(r => r.ReturnDate)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting returns by date range");
                throw;
            }
        }

        public async Task<List<ReturnTransaction>> GetReturnsRequiringApprovalAsync()
        {
            try
            {
                return await _context.ReturnTransactions
                    .Include(r => r.OriginalTransaction)
                    .Include(r => r.Customer)
                    .Include(r => r.Cashier)
                    .Include(r => r.ReturnItems)
                    .ThenInclude(ri => ri.Item)
                    .Where(r => r.Status == ReturnStatus.Pending && r.RequiredApproval)
                    .OrderBy(r => r.ReturnDate)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting returns requiring approval");
                throw;
            }
        }

        public async Task<bool> ApproveReturnAsync(int returnId, int approverId, string notes = "")
        {
            try
            {
                var returnTransaction = await _context.ReturnTransactions.FindAsync(returnId);
                if (returnTransaction == null)
                {
                    _logger.LogWarning("Return transaction {ReturnId} not found for approval", returnId);
                    return false;
                }

                if (returnTransaction.Status != ReturnStatus.Pending)
                {
                    _logger.LogWarning("Return transaction {ReturnId} is not in pending status", returnId);
                    return false;
                }

                returnTransaction.Status = ReturnStatus.Approved;
                returnTransaction.ApprovedByUserId = approverId;
                returnTransaction.ApprovalDate = DateTime.Now;
                if (!string.IsNullOrEmpty(notes))
                {
                    returnTransaction.Notes += $"\nApproval Notes: {notes}";
                }

                await _context.SaveChangesAsync();

                // Log the approval
                await _auditService.LogEventAsync(
                    "Returns",
                    $"Approved return {returnTransaction.ReturnNumber}",
                    approverId);

                _logger.LogInformation("Return {ReturnNumber} approved by user {ApproverId}", returnTransaction.ReturnNumber, approverId);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error approving return {ReturnId}", returnId);
                throw;
            }
        }

        public async Task<bool> RejectReturnAsync(int returnId, int rejectedById, string reason)
        {
            try
            {
                var returnTransaction = await _context.ReturnTransactions.FindAsync(returnId);
                if (returnTransaction == null)
                {
                    _logger.LogWarning("Return transaction {ReturnId} not found for rejection", returnId);
                    return false;
                }

                if (returnTransaction.Status != ReturnStatus.Pending)
                {
                    _logger.LogWarning("Return transaction {ReturnId} is not in pending status", returnId);
                    return false;
                }

                returnTransaction.Status = ReturnStatus.Rejected;
                returnTransaction.ApprovedByUserId = rejectedById;
                returnTransaction.ApprovalDate = DateTime.Now;
                returnTransaction.Notes += $"\nRejection Reason: {reason}";

                await _context.SaveChangesAsync();

                // Log the rejection
                await _auditService.LogEventAsync(
                    "Returns",
                    $"Rejected return {returnTransaction.ReturnNumber}: {reason}",
                    rejectedById);

                _logger.LogInformation("Return {ReturnNumber} rejected by user {RejectedById}", returnTransaction.ReturnNumber, rejectedById);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error rejecting return {ReturnId}", returnId);
                throw;
            }
        }

        public async Task<ReturnCompletionResult> CompleteReturnAsync(int returnId, int userId)
        {
            try
            {
                return await CompleteReturnInternalAsync(returnId, userId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error completing return {ReturnId}", returnId);
                return new ReturnCompletionResult
                {
                    IsSuccessful = false,
                    Message = "An error occurred while completing the return",
                    Errors = new List<string> { ex.Message }
                };
            }
        }

        private async Task<ReturnCompletionResult> CompleteReturnInternalAsync(int returnId, int userId)
        {
            var result = new ReturnCompletionResult();

            var returnTransaction = await _context.ReturnTransactions
                .Include(r => r.ReturnItems)
                .ThenInclude(ri => ri.Item)
                .Include(r => r.OriginalTransaction)
                .FirstOrDefaultAsync(r => r.Id == returnId);

            if (returnTransaction == null)
            {
                result.IsSuccessful = false;
                result.Message = "Return transaction not found";
                return result;
            }

            if (returnTransaction.Status != ReturnStatus.Approved)
            {
                result.IsSuccessful = false;
                result.Message = "Return must be approved before completion";
                return result;
            }

            using var transaction = await _context.Database.BeginTransactionAsync();

            try
            {
                // Process refund
                var refundResult = await ProcessRefundAsync(returnTransaction);
                if (!refundResult.IsSuccessful)
                {
                    result.IsSuccessful = false;
                    result.Message = "Failed to process refund: " + refundResult.Message;
                    result.Errors = refundResult.Errors;
                    return result;
                }

                // Restock items if applicable
                bool itemsRestocked = await RestockReturnedItemsInternalAsync(returnTransaction, userId);

                // Update return status
                returnTransaction.Status = ReturnStatus.Completed;
                returnTransaction.RefundReceiptNumber = refundResult.ReceiptNumber;
                returnTransaction.ItemsRestocked = itemsRestocked;
                if (itemsRestocked)
                {
                    returnTransaction.RestockDate = DateTime.Now;
                    returnTransaction.RestockedByUserId = userId;
                }

                await _context.SaveChangesAsync();
                await transaction.CommitAsync();

                // Log completion
                await _auditService.LogEventAsync(
                    "Returns",
                    $"Completed return {returnTransaction.ReturnNumber} - Refund: {returnTransaction.RefundAmount:C}",
                    userId);

                result.IsSuccessful = true;
                result.Message = "Return completed successfully";
                result.RefundAmount = returnTransaction.RefundAmount;
                result.RefundMethod = returnTransaction.RefundMethod;
                result.RefundReceiptNumber = returnTransaction.RefundReceiptNumber;
                result.ItemsRestocked = itemsRestocked;

                _logger.LogInformation("Return {ReturnNumber} completed successfully", returnTransaction.ReturnNumber);
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                result.IsSuccessful = false;
                result.Message = "An error occurred while completing the return";
                result.Errors.Add(ex.Message);
                _logger.LogError(ex, "Error completing return {ReturnId}", returnId);
            }

            return result;
        }

        public async Task<bool> RestockReturnedItemsAsync(int returnId, int userId)
        {
            try
            {
                var returnTransaction = await _context.ReturnTransactions
                    .Include(r => r.ReturnItems)
                    .ThenInclude(ri => ri.Item)
                    .FirstOrDefaultAsync(r => r.Id == returnId);

                if (returnTransaction == null)
                {
                    _logger.LogWarning("Return transaction {ReturnId} not found for restocking", returnId);
                    return false;
                }

                return await RestockReturnedItemsInternalAsync(returnTransaction, userId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error restocking returned items for return {ReturnId}", returnId);
                throw;
            }
        }

        private async Task<bool> RestockReturnedItemsInternalAsync(ReturnTransaction returnTransaction, int userId)
        {
            try
            {
                bool anyItemsRestocked = false;

                foreach (var returnItem in returnTransaction.ReturnItems.Where(ri => ri.CanRestock))
                {
                    // Determine restock location
                    int locationId = returnItem.RestockLocationId ?? 1; // Default to main location

                    // Add inventory back
                    var inventoryResult = await _inventoryService.AdjustInventoryAsync(
                        returnItem.ItemId,
                        locationId,
                        returnItem.Quantity,
                        $"Restocked from return {returnTransaction.ReturnNumber}",
                        userId);

                    if (inventoryResult)
                    {
                        anyItemsRestocked = true;
                        _logger.LogInformation("Restocked {Quantity} units of item {ItemId} from return {ReturnNumber}",
                            returnItem.Quantity, returnItem.ItemId, returnTransaction.ReturnNumber);
                    }
                    else
                    {
                        _logger.LogWarning("Failed to restock item {ItemId} from return {ReturnNumber}",
                            returnItem.ItemId, returnTransaction.ReturnNumber);
                    }
                }

                if (anyItemsRestocked)
                {
                    await _auditService.LogEventAsync(
                        "Inventory",
                        $"Restocked items from return {returnTransaction.ReturnNumber}",
                        userId);
                }

                return anyItemsRestocked;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error restocking items for return {ReturnNumber}", returnTransaction.ReturnNumber);
                return false;
            }
        }

        public async Task<ReturnValidationResult> ValidateReturnAsync(int transactionId, List<ReturnItemRequest> returnItems)
        {
            var result = new ReturnValidationResult();

            try
            {
                // Get original transaction
                var originalTransaction = await _context.Transactions
                    .Include(t => t.Items)
                    .ThenInclude(ti => ti.Item)
                    .FirstOrDefaultAsync(t => t.Id == transactionId);

                if (originalTransaction == null)
                {
                    result.IsValid = false;
                    result.Errors.Add("Original transaction not found");
                    return result;
                }

                // Check return period
                result.IsWithinReturnPeriod = IsWithinReturnPeriod(originalTransaction.TransactionDate);
                if (!result.IsWithinReturnPeriod)
                {
                    result.Warnings.Add("Return is outside the standard return period");
                    result.RequiresManagerApproval = true;
                }

                // Validate return items
                foreach (var returnItem in returnItems)
                {
                    var originalItem = originalTransaction.Items.FirstOrDefault(ti => ti.ItemId == returnItem.ItemId);
                    if (originalItem == null)
                    {
                        result.Errors.Add($"Item {returnItem.ItemId} was not in the original transaction");
                        continue;
                    }

                    // Check quantity
                    var alreadyReturned = await GetAlreadyReturnedQuantityAsync(transactionId, returnItem.ItemId);
                    var availableToReturn = originalItem.Quantity - alreadyReturned;

                    if (returnItem.Quantity > availableToReturn)
                    {
                        result.Errors.Add($"Cannot return {returnItem.Quantity} units of item {returnItem.ItemId}. Only {availableToReturn} units available for return");
                    }

                    // Check refund amount
                    if (returnItem.RefundPricePerUnit > originalItem.Price)
                    {
                        result.Warnings.Add($"Refund price for item {returnItem.ItemId} exceeds original price");
                        result.RequiresManagerApproval = true;
                    }
                }

                // Calculate total refund amount
                result.MaxRefundAmount = await CalculateRefundAmountAsync(returnItems, originalTransaction);

                // Check if large refund requires approval
                if (result.MaxRefundAmount > 100) // Configurable threshold
                {
                    result.RequiresManagerApproval = true;
                    result.Warnings.Add("Large refund amount requires manager approval");
                }

                result.IsValid = !result.Errors.Any();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating return for transaction {TransactionId}", transactionId);
                result.IsValid = false;
                result.Errors.Add("An error occurred during validation");
            }

            return result;
        }

        public async Task<decimal> CalculateRefundAmountAsync(List<ReturnItemRequest> returnItems, Transaction originalTransaction)
        {
            try
            {
                decimal totalRefund = 0;

                foreach (var returnItem in returnItems)
                {
                    totalRefund += returnItem.Quantity * returnItem.RefundPricePerUnit;
                }

                return Math.Round(totalRefund, 2);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error calculating refund amount");
                throw;
            }
        }

        public async Task<ReturnStatistics> GetReturnStatisticsAsync(DateTime fromDate, DateTime toDate)
        {
            try
            {
                var returns = await _context.ReturnTransactions
                    .Include(r => r.ReturnItems)
                    .Where(r => r.ReturnDate >= fromDate && r.ReturnDate <= toDate)
                    .ToListAsync();

                var stats = new ReturnStatistics
                {
                    TotalReturns = returns.Count,
                    TotalRefundAmount = returns.Sum(r => r.RefundAmount),
                    PendingReturns = returns.Count(r => r.Status == ReturnStatus.Pending),
                    ApprovedReturns = returns.Count(r => r.Status == ReturnStatus.Approved),
                    RejectedReturns = returns.Count(r => r.Status == ReturnStatus.Rejected),
                    CompletedReturns = returns.Count(r => r.Status == ReturnStatus.Completed),
                    AverageRefundAmount = returns.Count > 0 ? returns.Average(r => r.RefundAmount) : 0
                };

                // Group by return reasons
                stats.ReturnReasons = returns
                    .GroupBy(r => r.ReturnReason)
                    .ToDictionary(g => g.Key, g => g.Count());

                // Group by refund methods
                stats.RefundMethods = returns
                    .Where(r => r.Status == ReturnStatus.Completed)
                    .GroupBy(r => r.RefundMethod)
                    .ToDictionary(g => g.Key, g => g.Sum(r => r.RefundAmount));

                return stats;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting return statistics");
                throw;
            }
        }

        public bool IsWithinReturnPeriod(DateTime transactionDate, int allowedDays = 30)
        {
            return (DateTime.Now - transactionDate).TotalDays <= allowedDays;
        }

        public async Task<string> GenerateReturnReceiptAsync(int returnId)
        {
            try
            {
                var returnTransaction = await GetReturnByIdAsync(returnId);
                if (returnTransaction == null)
                {
                    throw new ArgumentException($"Return transaction {returnId} not found");
                }

                // Use the existing receipt service to generate return receipt
                return await _receiptService.GenerateReturnReceiptAsync(returnTransaction);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating return receipt for return {ReturnId}", returnId);
                throw;
            }
        }

        // Helper methods
        private async Task<string> GenerateReturnNumberAsync()
        {
            try
            {
                var lastReturn = await _context.ReturnTransactions
                    .Where(r => r.ReturnNumber.StartsWith("RET"))
                    .OrderByDescending(r => r.Id)
                    .FirstOrDefaultAsync();

                int nextNumber = 1;
                if (lastReturn != null && !string.IsNullOrEmpty(lastReturn.ReturnNumber))
                {
                    var numberPart = lastReturn.ReturnNumber.Substring(3);
                    if (int.TryParse(numberPart, out int lastNumber))
                    {
                        nextNumber = lastNumber + 1;
                    }
                }

                return $"RET{nextNumber:D6}"; // RET000001, RET000002, etc.
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating return number");
                return $"RET{DateTime.Now.Ticks % 1000000:D6}";
            }
        }

        private bool DetermineIfApprovalRequired(decimal refundAmount, ReturnRequest returnRequest)
        {
            // Configurable business rules for when approval is required
            if (refundAmount > 100) return true; // Large amounts
            if (returnRequest.RefundMethod == RefundMethods.Cash && refundAmount > 50) return true; // Cash refunds
            if (returnRequest.ReturnReason == ReturnReasons.CustomerChanged) return true; // Customer changed mind

            return false;
        }

        private bool IsPartialReturn(List<ReturnItemRequest> returnItems, Transaction originalTransaction)
        {
            // Check if all items from original transaction are being returned
            foreach (var originalItem in originalTransaction.Items)
            {
                var returnItem = returnItems.FirstOrDefault(ri => ri.ItemId == originalItem.ItemId);
                if (returnItem == null || returnItem.Quantity < originalItem.Quantity)
                {
                    return true; // Partial return
                }
            }

            return returnItems.Count < originalTransaction.Items.Count;
        }

        private async Task<int> GetAlreadyReturnedQuantityAsync(int transactionId, int itemId)
        {
            try
            {
                return await _context.ReturnItems
                    .Where(ri => ri.ReturnTransaction.OriginalTransactionId == transactionId &&
                                ri.ItemId == itemId &&
                                ri.ReturnTransaction.Status != ReturnStatus.Rejected &&
                                ri.ReturnTransaction.Status != ReturnStatus.Cancelled)
                    .SumAsync(ri => ri.Quantity);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting already returned quantity");
                return 0;
            }
        }

        private async Task<RefundProcessingResult> ProcessRefundAsync(ReturnTransaction returnTransaction)
        {
            var result = new RefundProcessingResult();

            try
            {
                // Generate receipt number
                result.ReceiptNumber = $"REF{DateTime.Now:yyyyMMddHHmmss}";

                // Process the refund based on method
                switch (returnTransaction.RefundMethod)
                {
                    case RefundMethods.Cash:
                        // Record cash refund
                        await _financialService.RecordCashRefundAsync(
                            returnTransaction.RefundAmount,
                            returnTransaction.CashierId,
                            $"Cash refund for return {returnTransaction.ReturnNumber}");
                        break;

                    case RefundMethods.Credit:
                        // Process credit refund (would integrate with payment processor)
                        // For now, just log it
                        await _auditService.LogEventAsync(
                            "Refunds",
                            $"Credit refund processed: {returnTransaction.RefundAmount:C} for return {returnTransaction.ReturnNumber}",
                            returnTransaction.CashierId);
                        break;

                    case RefundMethods.StoreCredit:
                        // Issue store credit
                        returnTransaction.StoreCreditAmount = returnTransaction.RefundAmount;
                        returnTransaction.StoreCreditVoucherNumber = $"SC{DateTime.Now:yyyyMMddHHmmss}";
                        break;
                }

                result.IsSuccessful = true;
                result.Message = "Refund processed successfully";
            }
            catch (Exception ex)
            {
                result.IsSuccessful = false;
                result.Message = "Failed to process refund";
                result.Errors.Add(ex.Message);
                _logger.LogError(ex, "Error processing refund for return {ReturnNumber}", returnTransaction.ReturnNumber);
            }

            return result;
        }
    }

    /// <summary>
    /// Refund processing result
    /// </summary>
    public class RefundProcessingResult
    {
        public bool IsSuccessful { get; set; }
        public string Message { get; set; } = string.Empty;
        public string ReceiptNumber { get; set; } = string.Empty;
        public List<string> Errors { get; set; } = new List<string>();
    }
}
