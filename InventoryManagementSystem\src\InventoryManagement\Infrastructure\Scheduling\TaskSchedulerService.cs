using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Hosting;

namespace InventoryManagement.Infrastructure.Scheduling
{
    /// <summary>
    /// Service for managing and executing scheduled tasks
    /// </summary>
    public class TaskSchedulerService : BackgroundService, ITaskSchedulerService
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly ILogger<TaskSchedulerService> _logger;
        private readonly Dictionary<Guid, ScheduledTask> _tasks;
        private readonly Dictionary<Guid, CancellationTokenSource> _runningTasks;
        private readonly string _tasksFilePath;
        private readonly Timer _checkTimer;
        
        public TaskSchedulerService(
            IServiceProvider serviceProvider,
            ILogger<TaskSchedulerService> logger)
        {
            _serviceProvider = serviceProvider ?? throw new ArgumentNullException(nameof(serviceProvider));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _tasks = new Dictionary<Guid, ScheduledTask>();
            _runningTasks = new Dictionary<Guid, CancellationTokenSource>();
            _tasksFilePath = Path.Combine(AppContext.BaseDirectory, "App_Data", "ScheduledTasks.json");
            
            LoadTasks();
            
            // Initialize timer to check for tasks to run every minute
            _checkTimer = new Timer(
                CheckTasksToExecute, 
                null,
                TimeSpan.FromMinutes(1), 
                TimeSpan.FromMinutes(1));
        }
        
        /// <summary>
        /// Gets all scheduled tasks
        /// </summary>
        public IReadOnlyList<ScheduledTask> GetAllTasks()
        {
            return _tasks.Values.ToList();
        }
        
        /// <summary>
        /// Gets a specific task by ID
        /// </summary>
        public ScheduledTask GetTaskById(Guid taskId)
        {
            return _tasks.TryGetValue(taskId, out var task) ? task : null;
        }
        
        /// <summary>
        /// Adds or updates a scheduled task
        /// </summary>
        public async Task<ScheduledTask> SaveTaskAsync(ScheduledTask task)
        {
            if (task == null)
            {
                throw new ArgumentNullException(nameof(task));
            }
            
            // Ensure it has a valid ID
            if (task.Id == Guid.Empty)
            {
                task.Id = Guid.NewGuid();
            }
            
            // Calculate next run time
            if (task.IsEnabled)
            {
                try
                {
                    task.NextRunTime = task.Schedule.CalculateNextRunTime(task.LastRunTime);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error calculating next run time for task: {TaskName}", task.Name);
                    task.IsEnabled = false;
                }
            }
            
            // Add or update
            _tasks[task.Id] = task;
            
            await SaveTasksAsync();
            _logger.LogInformation("Saved scheduled task: {TaskName}", task.Name);
            
            return task;
        }
        
        /// <summary>
        /// Deletes a scheduled task
        /// </summary>
        public async Task<bool> DeleteTaskAsync(Guid taskId)
        {
            var removed = _tasks.Remove(taskId);
            
            if (removed)
            {
                // Cancel if running
                if (_runningTasks.TryGetValue(taskId, out var cts))
                {
                    cts.Cancel();
                    _runningTasks.Remove(taskId);
                }
                
                await SaveTasksAsync();
                _logger.LogInformation("Deleted scheduled task: {TaskId}", taskId);
            }
            
            return removed;
        }
        
        /// <summary>
        /// Enables or disables a scheduled task
        /// </summary>
        public async Task<bool> SetTaskEnabledStateAsync(Guid taskId, bool enabled)
        {
            if (!_tasks.TryGetValue(taskId, out var task))
            {
                return false;
            }
            
            task.IsEnabled = enabled;
            
            // Recalculate next run time if enabling
            if (enabled)
            {
                try
                {
                    task.NextRunTime = task.Schedule.CalculateNextRunTime(task.LastRunTime);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error calculating next run time for task: {TaskName}", task.Name);
                    task.IsEnabled = false;
                }
            }
            else
            {
                // Cancel if running
                if (_runningTasks.TryGetValue(taskId, out var cts))
                {
                    cts.Cancel();
                    _runningTasks.Remove(taskId);
                }
            }
            
            await SaveTasksAsync();
            
            _logger.LogInformation("Set task {TaskName} enabled state to: {Enabled}", 
                task.Name, 
                enabled);
                
            return true;
        }
        
        /// <summary>
        /// Executes a task immediately, regardless of schedule
        /// </summary>
        public async Task<bool> ExecuteTaskNowAsync(Guid taskId)
        {
            if (!_tasks.TryGetValue(taskId, out var task))
            {
                return false;
            }
            
            _logger.LogInformation("Executing task immediately: {TaskName}", task.Name);
            
            try
            {
                await ExecuteTaskAsync(task);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error executing task immediately: {TaskName}", task.Name);
                return false;
            }
        }
        
        /// <summary>
        /// Loads tasks from storage
        /// </summary>
        private void LoadTasks()
        {
            try
            {
                if (File.Exists(_tasksFilePath))
                {
                    var json = File.ReadAllText(_tasksFilePath);
                    var loadedTasks = JsonSerializer.Deserialize<List<ScheduledTask>>(json);
                    
                    if (loadedTasks != null)
                    {
                        _tasks.Clear();
                        foreach (var task in loadedTasks)
                        {
                            _tasks[task.Id] = task;
                        }
                        
                        _logger.LogInformation("Loaded {Count} scheduled tasks", _tasks.Count);
                    }
                }
                else
                {
                    _tasks.Clear();
                    _logger.LogInformation("No scheduled tasks file found, starting with empty task list");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading scheduled tasks");
                _tasks.Clear();
            }
        }
        
        /// <summary>
        /// Saves tasks to storage
        /// </summary>
        private async Task SaveTasksAsync()
        {
            try
            {
                Directory.CreateDirectory(Path.GetDirectoryName(_tasksFilePath));
                var json = JsonSerializer.Serialize(_tasks.Values.ToList(), new JsonSerializerOptions
                {
                    WriteIndented = true
                });
                
                await File.WriteAllTextAsync(_tasksFilePath, json);
                _logger.LogInformation("Saved {Count} scheduled tasks", _tasks.Count);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error saving scheduled tasks");
            }
        }
        
        /// <summary>
        /// Checks for tasks that need to be executed
        /// </summary>
        private void CheckTasksToExecute(object state)
        {
            var now = DateTime.Now;
            
            foreach (var task in _tasks.Values)
            {
                // Skip if disabled, already running, or not yet due
                if (!task.IsEnabled || 
                    _runningTasks.ContainsKey(task.Id) || 
                    !task.NextRunTime.HasValue || 
                    task.NextRunTime.Value > now)
                {
                    continue;
                }
                
                _logger.LogInformation("Scheduled task due for execution: {TaskName}", task.Name);
                
                // Execute the task
                _ = Task.Run(async () =>
                {
                    await ExecuteTaskAsync(task);
                    
                    // Calculate next run time
                    try
                    {
                        task.NextRunTime = task.Schedule.CalculateNextRunTime(DateTime.Now);
                        await SaveTasksAsync();
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Error calculating next run time for task: {TaskName}", task.Name);
                    }
                });
            }
        }
        
        /// <summary>
        /// Executes a scheduled task
        /// </summary>
        private async Task ExecuteTaskAsync(ScheduledTask task)
        {
            // Skip if already running
            if (_runningTasks.ContainsKey(task.Id))
            {
                _logger.LogWarning("Task is already running: {TaskName}", task.Name);
                return;
            }
            
            // Create cancellation token for this execution
            var cts = new CancellationTokenSource(TimeSpan.FromSeconds(task.TimeoutSeconds));
            _runningTasks[task.Id] = cts;
            
            try
            {
                task.LastRunTime = DateTime.Now;
                task.LastRunStatus = TaskExecutionStatus.Running;
                task.LastErrorMessage = null;
                
                // Save state
                await SaveTasksAsync();
                
                _logger.LogInformation("Starting execution of task: {TaskName}", task.Name);
                
                // Create handler
                var handlerType = Type.GetType(task.TaskHandlerTypeName);
                if (handlerType == null)
                {
                    throw new InvalidOperationException($"Task handler type not found: {task.TaskHandlerTypeName}");
                }
                
                using var scope = _serviceProvider.CreateScope();
                var handler = scope.ServiceProvider.GetService(handlerType) as ITaskHandler;
                
                if (handler == null)
                {
                    throw new InvalidOperationException(
                        $"Failed to create task handler of type: {task.TaskHandlerTypeName}");
                }
                
                // Execute task
                await handler.ExecuteAsync(task, cts.Token);
                
                // Update status on successful completion
                task.LastRunStatus = TaskExecutionStatus.Success;
                task.CurrentRetryCount = 0;
            }
            catch (OperationCanceledException)
            {
                task.LastRunStatus = TaskExecutionStatus.TimedOut;
                task.LastErrorMessage = "Task execution timed out";
                _logger.LogWarning("Task execution timed out: {TaskName}", task.Name);
            }
            catch (Exception ex)
            {
                task.LastRunStatus = TaskExecutionStatus.Failed;
                task.LastErrorMessage = ex.Message;
                _logger.LogError(ex, "Error executing task: {TaskName}", task.Name);
                
                // Handle retries
                if (task.CurrentRetryCount < task.MaxRetries)
                {
                    task.CurrentRetryCount++;
                    
                    // Schedule a retry in 5 minutes
                    task.NextRunTime = DateTime.Now.AddMinutes(5);
                    _logger.LogInformation("Scheduling retry {Count}/{Max} for task: {TaskName}", 
                        task.CurrentRetryCount, 
                        task.MaxRetries,
                        task.Name);
                }
                else
                {
                    // Reset retry count and calculate next regular run
                    task.CurrentRetryCount = 0;
                }
            }
            finally
            {
                _runningTasks.Remove(task.Id);
                cts.Dispose();
                
                // Save final status
                await SaveTasksAsync();
            }
        }
        
        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            _logger.LogInformation("Task scheduler service starting");
            
            // Run startup tasks
            foreach (var task in _tasks.Values.Where(t => t.IsEnabled && t.RunAtStartup))
            {
                _logger.LogInformation("Executing startup task: {TaskName}", task.Name);
                await ExecuteTaskAsync(task);
            }
            
            // Wait for cancellation
            while (!stoppingToken.IsCancellationRequested)
            {
                await Task.Delay(TimeSpan.FromSeconds(30), stoppingToken);
            }
            
            _logger.LogInformation("Task scheduler service stopping");
        }
    }
}
