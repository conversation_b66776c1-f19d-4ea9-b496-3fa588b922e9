using InventoryManagement.DataAccess;
using InventoryManagement.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace InventoryManagement.Services
{
    /// <summary>
    /// Service for discount management and calculation
    /// </summary>
    public class DiscountService : IDiscountService
    {
        private readonly ApplicationDbContext _context;
        private readonly ILogger<DiscountService> _logger;
        private readonly IAuditService _auditService;

        public DiscountService(
            ApplicationDbContext context,
            ILogger<DiscountService> logger,
            IAuditService auditService)
        {
            _context = context;
            _logger = logger;
            _auditService = auditService;
        }

        public async Task<List<Discount>> GetActiveDiscountsAsync()
        {
            try
            {
                return await _context.Discounts
                    .Where(d => d.IsActive && d.IsCurrentlyValid())
                    .OrderBy(d => d.Priority)
                    .ThenBy(d => d.Name)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting active discounts");
                throw;
            }
        }

        public async Task<Discount?> GetDiscountByIdAsync(int discountId)
        {
            try
            {
                return await _context.Discounts
                    .Include(d => d.CreatedByUser)
                    .Include(d => d.LastModifiedByUser)
                    .FirstOrDefaultAsync(d => d.Id == discountId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting discount by ID {DiscountId}", discountId);
                throw;
            }
        }

        public async Task<Discount?> GetDiscountByCodeAsync(string discountCode)
        {
            try
            {
                return await _context.Discounts
                    .FirstOrDefaultAsync(d => d.DiscountCode == discountCode && d.IsActive);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting discount by code {DiscountCode}", discountCode);
                throw;
            }
        }

        public async Task<List<Discount>> GetApplicableDiscountsAsync(List<TransactionItem> transactionItems, string customerType, decimal totalAmount)
        {
            try
            {
                var allActiveDiscounts = await GetActiveDiscountsAsync();
                var applicableDiscounts = new List<Discount>();

                foreach (var discount in allActiveDiscounts)
                {
                    // Check minimum purchase amount
                    if (totalAmount < discount.MinimumPurchaseAmount)
                        continue;

                    // Check customer type
                    if (!discount.IsApplicableToCustomerType(customerType))
                        continue;

                    // Check if applicable to any items in the transaction
                    bool applicableToItems = false;
                    foreach (var item in transactionItems)
                    {
                        // You would need to get the item's category here
                        // For now, assume all items are applicable
                        applicableToItems = true;
                        break;
                    }

                    if (applicableToItems)
                    {
                        applicableDiscounts.Add(discount);
                    }
                }

                return applicableDiscounts.OrderByDescending(d => d.Priority).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting applicable discounts");
                throw;
            }
        }

        public async Task<DiscountCalculationResult> CalculateDiscountAsync(Discount discount, List<TransactionItem> transactionItems, decimal totalAmount)
        {
            var result = new DiscountCalculationResult();

            try
            {
                if (!discount.IsCurrentlyValid())
                {
                    result.IsValid = false;
                    result.Errors.Add("Discount is not currently valid");
                    return result;
                }

                if (totalAmount < discount.MinimumPurchaseAmount)
                {
                    result.IsValid = false;
                    result.Errors.Add($"Minimum purchase amount of {discount.MinimumPurchaseAmount:C} not met");
                    return result;
                }

                decimal discountAmount = 0;

                switch (discount.Type)
                {
                    case DiscountType.Percentage:
                        discountAmount = totalAmount * (discount.Value / 100);
                        if (discount.MaximumDiscountAmount > 0 && discountAmount > discount.MaximumDiscountAmount)
                        {
                            discountAmount = discount.MaximumDiscountAmount;
                            result.Warnings.Add($"Discount capped at maximum amount of {discount.MaximumDiscountAmount:C}");
                        }
                        break;

                    case DiscountType.FixedAmount:
                        discountAmount = Math.Min(discount.Value, totalAmount);
                        break;

                    case DiscountType.BuyXGetYFree:
                        // Complex logic for buy X get Y free - simplified for now
                        discountAmount = CalculateBuyXGetYDiscount(discount, transactionItems);
                        break;

                    default:
                        result.IsValid = false;
                        result.Errors.Add("Unsupported discount type");
                        return result;
                }

                result.IsValid = true;
                result.DiscountAmount = Math.Round(discountAmount, 2);
                result.FinalAmount = totalAmount - result.DiscountAmount;
                result.Message = $"Discount of {result.DiscountAmount:C} applied";

                _logger.LogInformation("Calculated discount {DiscountCode}: {DiscountAmount:C} on {TotalAmount:C}",
                    discount.DiscountCode, result.DiscountAmount, totalAmount);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error calculating discount {DiscountCode}", discount.DiscountCode);
                result.IsValid = false;
                result.Errors.Add("An error occurred while calculating the discount");
            }

            return result;
        }

        public async Task<DiscountApplicationResult> ApplyDiscountAsync(int transactionId, int discountId, int appliedByUserId, string reason = "", int? itemId = null)
        {
            var result = new DiscountApplicationResult();

            try
            {
                var discount = await GetDiscountByIdAsync(discountId);
                if (discount == null)
                {
                    result.IsSuccessful = false;
                    result.Message = "Discount not found";
                    return result;
                }

                var transaction = await _context.Transactions
                    .Include(t => t.Items)
                    .ThenInclude(ti => ti.Item)
                    .FirstOrDefaultAsync(t => t.Id == transactionId);

                if (transaction == null)
                {
                    result.IsSuccessful = false;
                    result.Message = "Transaction not found";
                    return result;
                }

                // Validate discount application
                var validationResult = await ValidateDiscountApplicationAsync(discount, transaction.Items, "Regular", appliedByUserId);
                if (!validationResult.IsValid)
                {
                    result.IsSuccessful = false;
                    result.Message = "Discount validation failed";
                    result.Errors = validationResult.Errors;
                    return result;
                }

                // Calculate discount amount
                var calculationResult = await CalculateDiscountAsync(discount, transaction.Items, transaction.Total);
                if (!calculationResult.IsValid)
                {
                    result.IsSuccessful = false;
                    result.Message = "Discount calculation failed";
                    result.Errors = calculationResult.Errors;
                    return result;
                }

                using var dbTransaction = await _context.Database.BeginTransactionAsync();

                try
                {
                    // Create discount application record
                    var discountApplication = new DiscountApplication
                    {
                        DiscountId = discountId,
                        TransactionId = transactionId,
                        ItemId = itemId,
                        DiscountAmount = calculationResult.DiscountAmount,
                        AppliedByUserId = appliedByUserId,
                        AppliedDate = DateTime.Now,
                        Reason = reason
                    };

                    // Check if approval is required
                    if (validationResult.RequiresApproval)
                    {
                        result.RequiredApproval = true;
                        result.Message = "Discount applied but requires manager approval";
                        // In a real system, you might set a pending status
                    }

                    _context.DiscountApplications.Add(discountApplication);

                    // Update discount usage count
                    discount.CurrentUsageCount++;
                    _context.Discounts.Update(discount);

                    // Update transaction total
                    transaction.DiscountAmount += calculationResult.DiscountAmount;
                    transaction.Total = transaction.SubTotal - transaction.DiscountAmount;
                    _context.Transactions.Update(transaction);

                    await _context.SaveChangesAsync();
                    await dbTransaction.CommitAsync();

                    // Log the discount application
                    await _auditService.LogEventAsync(
                        "Discounts",
                        $"Applied discount {discount.DiscountCode} ({calculationResult.DiscountAmount:C}) to transaction {transactionId}",
                        appliedByUserId);

                    result.IsSuccessful = true;
                    result.Message = validationResult.RequiresApproval ? "Discount applied pending approval" : "Discount applied successfully";
                    result.DiscountApplication = discountApplication;
                    result.DiscountAmount = calculationResult.DiscountAmount;

                    _logger.LogInformation("Applied discount {DiscountCode} to transaction {TransactionId}: {DiscountAmount:C}",
                        discount.DiscountCode, transactionId, calculationResult.DiscountAmount);
                }
                catch (Exception ex)
                {
                    await dbTransaction.RollbackAsync();
                    throw;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error applying discount {DiscountId} to transaction {TransactionId}", discountId, transactionId);
                result.IsSuccessful = false;
                result.Message = "An error occurred while applying the discount";
                result.Errors.Add(ex.Message);
            }

            return result;
        }

        public async Task<bool> RemoveDiscountAsync(int discountApplicationId, int userId)
        {
            try
            {
                var discountApplication = await _context.DiscountApplications
                    .Include(da => da.Discount)
                    .Include(da => da.Transaction)
                    .FirstOrDefaultAsync(da => da.Id == discountApplicationId);

                if (discountApplication == null)
                {
                    _logger.LogWarning("Discount application {DiscountApplicationId} not found", discountApplicationId);
                    return false;
                }

                using var transaction = await _context.Database.BeginTransactionAsync();

                try
                {
                    // Update transaction total
                    discountApplication.Transaction.DiscountAmount -= discountApplication.DiscountAmount;
                    discountApplication.Transaction.Total = discountApplication.Transaction.SubTotal - discountApplication.Transaction.DiscountAmount;

                    // Update discount usage count
                    discountApplication.Discount.CurrentUsageCount--;

                    // Remove discount application
                    _context.DiscountApplications.Remove(discountApplication);

                    await _context.SaveChangesAsync();
                    await transaction.CommitAsync();

                    // Log the removal
                    await _auditService.LogEventAsync(
                        "Discounts",
                        $"Removed discount {discountApplication.Discount.DiscountCode} from transaction {discountApplication.TransactionId}",
                        userId);

                    _logger.LogInformation("Removed discount application {DiscountApplicationId}", discountApplicationId);
                    return true;
                }
                catch (Exception ex)
                {
                    await transaction.RollbackAsync();
                    throw;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error removing discount application {DiscountApplicationId}", discountApplicationId);
                throw;
            }
        }

        public async Task<Discount> CreateDiscountAsync(Discount discount, int userId)
        {
            try
            {
                // Validate discount
                ValidateDiscount(discount);

                // Check for duplicate discount code
                var existingDiscount = await GetDiscountByCodeAsync(discount.DiscountCode);
                if (existingDiscount != null)
                {
                    throw new InvalidOperationException($"Discount code '{discount.DiscountCode}' already exists");
                }

                discount.CreatedByUserId = userId;
                discount.CreatedDate = DateTime.Now;

                _context.Discounts.Add(discount);
                await _context.SaveChangesAsync();

                // Log the creation
                await _auditService.LogEventAsync(
                    "Discounts",
                    $"Created discount {discount.DiscountCode} - {discount.Name}",
                    userId);

                _logger.LogInformation("Created discount {DiscountCode} with ID {DiscountId}", discount.DiscountCode, discount.Id);
                return discount;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating discount {DiscountCode}", discount.DiscountCode);
                throw;
            }
        }

        public async Task<Discount> UpdateDiscountAsync(Discount discount, int userId)
        {
            try
            {
                // Validate discount
                ValidateDiscount(discount);

                var existingDiscount = await GetDiscountByIdAsync(discount.Id);
                if (existingDiscount == null)
                {
                    throw new InvalidOperationException($"Discount with ID {discount.Id} not found");
                }

                // Check for duplicate discount code (excluding current discount)
                var duplicateDiscount = await _context.Discounts
                    .FirstOrDefaultAsync(d => d.DiscountCode == discount.DiscountCode && d.Id != discount.Id);
                if (duplicateDiscount != null)
                {
                    throw new InvalidOperationException($"Discount code '{discount.DiscountCode}' already exists");
                }

                discount.LastModifiedByUserId = userId;
                discount.LastModifiedDate = DateTime.Now;

                _context.Discounts.Update(discount);
                await _context.SaveChangesAsync();

                // Log the update
                await _auditService.LogEventAsync(
                    "Discounts",
                    $"Updated discount {discount.DiscountCode} - {discount.Name}",
                    userId);

                _logger.LogInformation("Updated discount {DiscountCode} with ID {DiscountId}", discount.DiscountCode, discount.Id);
                return discount;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating discount {DiscountId}", discount.Id);
                throw;
            }
        }

        public async Task<bool> DeleteDiscountAsync(int discountId, int userId)
        {
            try
            {
                var discount = await GetDiscountByIdAsync(discountId);
                if (discount == null)
                {
                    _logger.LogWarning("Discount {DiscountId} not found for deletion", discountId);
                    return false;
                }

                // Check if discount has been used
                var hasApplications = await _context.DiscountApplications
                    .AnyAsync(da => da.DiscountId == discountId);

                if (hasApplications)
                {
                    // Soft delete - just deactivate
                    discount.IsActive = false;
                    discount.LastModifiedByUserId = userId;
                    discount.LastModifiedDate = DateTime.Now;
                    _context.Discounts.Update(discount);
                }
                else
                {
                    // Hard delete if never used
                    _context.Discounts.Remove(discount);
                }

                await _context.SaveChangesAsync();

                // Log the deletion
                await _auditService.LogEventAsync(
                    "Discounts",
                    $"Deleted discount {discount.DiscountCode} - {discount.Name}",
                    userId);

                _logger.LogInformation("Deleted discount {DiscountCode} with ID {DiscountId}", discount.DiscountCode, discountId);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting discount {DiscountId}", discountId);
                throw;
            }
        }

        public async Task<DiscountValidationResult> ValidateDiscountApplicationAsync(Discount discount, List<TransactionItem> transactionItems, string customerType, int userId)
        {
            var result = new DiscountValidationResult();

            try
            {
                // Check if discount is currently valid
                if (!discount.IsCurrentlyValid())
                {
                    result.IsValid = false;
                    result.Errors.Add("Discount is not currently valid");
                    return result;
                }

                // Check customer type
                if (!discount.IsApplicableToCustomerType(customerType))
                {
                    result.IsValid = false;
                    result.Errors.Add($"Discount not applicable to customer type: {customerType}");
                    return result;
                }

                // Check usage limits
                if (discount.MaxUsageCount.HasValue && discount.CurrentUsageCount >= discount.MaxUsageCount.Value)
                {
                    result.IsValid = false;
                    result.Errors.Add("Discount usage limit exceeded");
                    return result;
                }

                // Check if approval is required
                if (discount.RequiresApproval)
                {
                    result.RequiresApproval = true;
                    result.Warnings.Add("This discount requires manager approval");
                }

                // Check user role permissions
                // You would get the user's role here - simplified for now
                string userRole = "Cashier"; // This should come from user service
                if (!CanUserApplyDiscount(discount, userRole))
                {
                    result.RequiresApproval = true;
                    result.Warnings.Add("User role requires manager approval for this discount");
                }

                result.IsValid = true;
                result.MaxDiscountAmount = discount.MaximumDiscountAmount;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating discount application");
                result.IsValid = false;
                result.Errors.Add("An error occurred during validation");
            }

            return result;
        }

        public async Task<DiscountUsageStatistics> GetDiscountUsageStatisticsAsync(int discountId, DateTime fromDate, DateTime toDate)
        {
            try
            {
                var applications = await _context.DiscountApplications
                    .Include(da => da.Transaction)
                    .Include(da => da.Item)
                    .Where(da => da.DiscountId == discountId &&
                                da.AppliedDate >= fromDate &&
                                da.AppliedDate <= toDate)
                    .ToListAsync();

                var stats = new DiscountUsageStatistics
                {
                    TotalUsages = applications.Count,
                    TotalDiscountAmount = applications.Sum(da => da.DiscountAmount),
                    AverageDiscountAmount = applications.Count > 0 ? applications.Average(da => da.DiscountAmount) : 0,
                    UniqueCustomers = applications.Where(da => da.Transaction.CustomerId.HasValue)
                                                .Select(da => da.Transaction.CustomerId.Value)
                                                .Distinct()
                                                .Count()
                };

                // Usage by day
                stats.UsageByDay = applications
                    .GroupBy(da => da.AppliedDate.Date.ToString("yyyy-MM-dd"))
                    .ToDictionary(g => g.Key, g => g.Count());

                return stats;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting discount usage statistics for discount {DiscountId}", discountId);
                throw;
            }
        }

        public async Task<List<DiscountApplication>> GetTransactionDiscountsAsync(int transactionId)
        {
            try
            {
                return await _context.DiscountApplications
                    .Include(da => da.Discount)
                    .Include(da => da.Item)
                    .Include(da => da.AppliedByUser)
                    .Where(da => da.TransactionId == transactionId)
                    .OrderBy(da => da.AppliedDate)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting transaction discounts for transaction {TransactionId}", transactionId);
                throw;
            }
        }

        public bool CanUserApplyDiscount(Discount discount, string userRole)
        {
            // Define role hierarchy
            var roleHierarchy = new Dictionary<string, int>
            {
                { "Cashier", 1 },
                { "Manager", 2 },
                { "Admin", 3 }
            };

            var requiredLevel = roleHierarchy.GetValueOrDefault(discount.MinimumUserRole, 1);
            var userLevel = roleHierarchy.GetValueOrDefault(userRole, 1);

            return userLevel >= requiredLevel;
        }

        public async Task<List<DiscountRecommendation>> GetDiscountRecommendationsAsync(List<TransactionItem> transactionItems, string customerType, decimal totalAmount)
        {
            try
            {
                var applicableDiscounts = await GetApplicableDiscountsAsync(transactionItems, customerType, totalAmount);
                var recommendations = new List<DiscountRecommendation>();

                foreach (var discount in applicableDiscounts)
                {
                    var calculationResult = await CalculateDiscountAsync(discount, transactionItems, totalAmount);
                    if (calculationResult.IsValid && calculationResult.DiscountAmount > 0)
                    {
                        recommendations.Add(new DiscountRecommendation
                        {
                            Discount = discount,
                            PotentialSavings = calculationResult.DiscountAmount,
                            RecommendationReason = GetRecommendationReason(discount, totalAmount),
                            Priority = discount.Priority,
                            RequiresApproval = discount.RequiresApproval
                        });
                    }
                }

                return recommendations.OrderByDescending(r => r.PotentialSavings).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting discount recommendations");
                throw;
            }
        }

        // Helper methods
        private void ValidateDiscount(Discount discount)
        {
            var errors = new List<string>();

            if (string.IsNullOrWhiteSpace(discount.DiscountCode))
                errors.Add("Discount code is required");

            if (string.IsNullOrWhiteSpace(discount.Name))
                errors.Add("Discount name is required");

            if (discount.Value <= 0)
                errors.Add("Discount value must be greater than zero");

            if (discount.Type == DiscountType.Percentage && discount.Value > 100)
                errors.Add("Percentage discount cannot exceed 100%");

            if (discount.StartDate.HasValue && discount.EndDate.HasValue && discount.StartDate > discount.EndDate)
                errors.Add("Start date cannot be after end date");

            if (errors.Any())
                throw new ArgumentException($"Discount validation failed: {string.Join(", ", errors)}");
        }

        private decimal CalculateBuyXGetYDiscount(Discount discount, List<TransactionItem> transactionItems)
        {
            // Simplified implementation for buy X get Y free
            // In a real implementation, you would parse the discount rules
            // For now, return 0
            return 0;
        }

        private string GetRecommendationReason(Discount discount, decimal totalAmount)
        {
            if (discount.Type == DiscountType.Percentage)
                return $"Save {discount.Value}% on your purchase";
            else if (discount.Type == DiscountType.FixedAmount)
                return $"Save {discount.Value:C} on your purchase";
            else
                return "Special discount available";
        }
    }
}
