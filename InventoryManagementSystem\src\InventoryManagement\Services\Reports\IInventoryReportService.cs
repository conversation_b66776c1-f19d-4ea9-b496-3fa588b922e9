using InventoryManagement.Models.Reports;

namespace InventoryManagement.Services.Reports
{
    /// <summary>
    /// Interface for inventory reporting services
    /// </summary>
    public interface IInventoryReportService
    {
        /// <summary>
        /// Generate comprehensive inventory report
        /// </summary>
        /// <param name="parameters">Report parameters</param>
        /// <returns>Inventory report</returns>
        Task<InventoryReport> GenerateInventoryReportAsync(InventoryReportParameters parameters);

        /// <summary>
        /// Get current inventory snapshot
        /// </summary>
        /// <param name="locationIds">Location IDs to include</param>
        /// <param name="categoryFilter">Category filter (optional)</param>
        /// <returns>Current inventory snapshot</returns>
        Task<InventorySnapshot> GetCurrentInventorySnapshotAsync(List<int>? locationIds = null, string? categoryFilter = null);

        /// <summary>
        /// Get low stock items alert
        /// </summary>
        /// <param name="locationIds">Location IDs to check</param>
        /// <returns>List of low stock items</returns>
        Task<List<LowStockItem>> GetLowStockItemsAsync(List<int>? locationIds = null);

        /// <summary>
        /// Get out of stock items alert
        /// </summary>
        /// <param name="locationIds">Location IDs to check</param>
        /// <returns>List of out of stock items</returns>
        Task<List<OutOfStockItem>> GetOutOfStockItemsAsync(List<int>? locationIds = null);

        /// <summary>
        /// Get overstocked items
        /// </summary>
        /// <param name="locationIds">Location IDs to check</param>
        /// <returns>List of overstocked items</returns>
        Task<List<OverstockedItem>> GetOverstockedItemsAsync(List<int>? locationIds = null);

        /// <summary>
        /// Get inventory by category
        /// </summary>
        /// <param name="asOfDate">Date for inventory snapshot</param>
        /// <param name="locationIds">Location IDs to include</param>
        /// <returns>Category inventory breakdown</returns>
        Task<List<CategoryInventory>> GetInventoryByCategoryAsync(DateTime? asOfDate = null, List<int>? locationIds = null);

        /// <summary>
        /// Get inventory by location
        /// </summary>
        /// <param name="asOfDate">Date for inventory snapshot</param>
        /// <returns>Location inventory breakdown</returns>
        Task<List<LocationInventory>> GetInventoryByLocationAsync(DateTime? asOfDate = null);

        /// <summary>
        /// Get inventory movements for a period
        /// </summary>
        /// <param name="fromDate">Start date</param>
        /// <param name="toDate">End date</param>
        /// <param name="locationIds">Location IDs to include</param>
        /// <param name="movementTypes">Movement types to include</param>
        /// <returns>List of inventory movements</returns>
        Task<List<InventoryMovementSummary>> GetInventoryMovementsAsync(DateTime fromDate, DateTime toDate, List<int>? locationIds = null, List<string>? movementTypes = null);

        /// <summary>
        /// Get item turnover analysis
        /// </summary>
        /// <param name="fromDate">Start date for analysis</param>
        /// <param name="toDate">End date for analysis</param>
        /// <param name="categoryFilter">Category filter (optional)</param>
        /// <returns>Item turnover data</returns>
        Task<List<ItemTurnover>> GetItemTurnoverAnalysisAsync(DateTime fromDate, DateTime toDate, string? categoryFilter = null);

        /// <summary>
        /// Get slow moving items
        /// </summary>
        /// <param name="daysThreshold">Days threshold for slow moving</param>
        /// <param name="locationIds">Location IDs to check</param>
        /// <returns>List of slow moving items</returns>
        Task<List<SlowMovingItem>> GetSlowMovingItemsAsync(int daysThreshold = 90, List<int>? locationIds = null);

        /// <summary>
        /// Get fast moving items
        /// </summary>
        /// <param name="daysThreshold">Days threshold for analysis</param>
        /// <param name="locationIds">Location IDs to check</param>
        /// <returns>List of fast moving items</returns>
        Task<List<FastMovingItem>> GetFastMovingItemsAsync(int daysThreshold = 30, List<int>? locationIds = null);

        /// <summary>
        /// Get inventory valuation
        /// </summary>
        /// <param name="asOfDate">Date for valuation</param>
        /// <param name="locationIds">Location IDs to include</param>
        /// <param name="categoryFilter">Category filter (optional)</param>
        /// <returns>Inventory valuation data</returns>
        Task<List<ItemValuation>> GetInventoryValuationAsync(DateTime? asOfDate = null, List<int>? locationIds = null, string? categoryFilter = null);

        /// <summary>
        /// Get inventory by supplier
        /// </summary>
        /// <param name="asOfDate">Date for inventory snapshot</param>
        /// <param name="supplierIds">Supplier IDs to include</param>
        /// <returns>Supplier inventory breakdown</returns>
        Task<List<SupplierInventory>> GetInventoryBySupplierAsync(DateTime? asOfDate = null, List<int>? supplierIds = null);

        /// <summary>
        /// Get inventory aging analysis
        /// </summary>
        /// <param name="asOfDate">Date for aging analysis</param>
        /// <param name="locationIds">Location IDs to include</param>
        /// <returns>Inventory aging data</returns>
        Task<List<InventoryAging>> GetInventoryAgingAnalysisAsync(DateTime? asOfDate = null, List<int>? locationIds = null);

        /// <summary>
        /// Export inventory report to specified format
        /// </summary>
        /// <param name="report">Inventory report to export</param>
        /// <param name="format">Export format</param>
        /// <returns>Exported file content as byte array</returns>
        Task<byte[]> ExportInventoryReportAsync(InventoryReport report, InventoryReportFormat format);

        /// <summary>
        /// Get inventory summary for dashboard
        /// </summary>
        /// <returns>Inventory summary data</returns>
        Task<InventorySummaryDashboard> GetInventorySummaryForDashboardAsync();

        /// <summary>
        /// Get inventory alerts summary
        /// </summary>
        /// <param name="locationIds">Location IDs to check</param>
        /// <returns>Inventory alerts summary</returns>
        Task<InventoryAlertsSummary> GetInventoryAlertsSummaryAsync(List<int>? locationIds = null);

        /// <summary>
        /// Get inventory forecast based on historical data
        /// </summary>
        /// <param name="itemId">Item ID to forecast</param>
        /// <param name="forecastDays">Number of days to forecast</param>
        /// <param name="historicalDays">Number of historical days to analyze</param>
        /// <returns>Inventory forecast data</returns>
        Task<List<InventoryForecast>> GetInventoryForecastAsync(int itemId, int forecastDays = 30, int historicalDays = 90);

        /// <summary>
        /// Get reorder recommendations
        /// </summary>
        /// <param name="locationIds">Location IDs to check</param>
        /// <returns>List of reorder recommendations</returns>
        Task<List<ReorderRecommendation>> GetReorderRecommendationsAsync(List<int>? locationIds = null);
    }

    /// <summary>
    /// Current inventory snapshot
    /// </summary>
    public class InventorySnapshot
    {
        public DateTime SnapshotDate { get; set; } = DateTime.Now;
        public int TotalItems { get; set; }
        public int TotalQuantity { get; set; }
        public decimal TotalValue { get; set; }
        public int LowStockItems { get; set; }
        public int OutOfStockItems { get; set; }
        public int OverstockedItems { get; set; }
        public List<CategoryInventory> CategoryBreakdown { get; set; } = new List<CategoryInventory>();
        public List<LocationInventory> LocationBreakdown { get; set; } = new List<LocationInventory>();
    }

    /// <summary>
    /// Inventory summary for dashboard display
    /// </summary>
    public class InventorySummaryDashboard
    {
        public DateTime Date { get; set; } = DateTime.Now;
        public decimal TotalInventoryValue { get; set; }
        public int TotalItems { get; set; }
        public int LowStockAlerts { get; set; }
        public int OutOfStockAlerts { get; set; }
        public int OverstockAlerts { get; set; }
        public int RecentMovements { get; set; }
        public List<TopValueItem> TopValueItems { get; set; } = new List<TopValueItem>();
        public List<CategoryInventory> TopCategories { get; set; } = new List<CategoryInventory>();
        public decimal InventoryTurnoverRate { get; set; }
        public int DaysOfInventory { get; set; }
    }

    /// <summary>
    /// Top value item for dashboard
    /// </summary>
    public class TopValueItem
    {
        public int ItemId { get; set; }
        public string ItemName { get; set; } = string.Empty;
        public string ItemCode { get; set; } = string.Empty;
        public int Quantity { get; set; }
        public decimal UnitValue { get; set; }
        public decimal TotalValue { get; set; }
        public string LocationName { get; set; } = string.Empty;
    }

    /// <summary>
    /// Inventory alerts summary
    /// </summary>
    public class InventoryAlertsSummary
    {
        public int LowStockCount { get; set; }
        public int OutOfStockCount { get; set; }
        public int OverstockCount { get; set; }
        public int CriticalAlerts { get; set; }
        public decimal TotalValueAtRisk { get; set; }
        public List<string> TopAlertCategories { get; set; } = new List<string>();
        public List<string> TopAlertLocations { get; set; } = new List<string>();
    }

    /// <summary>
    /// Inventory forecast data
    /// </summary>
    public class InventoryForecast
    {
        public DateTime Date { get; set; }
        public int ForecastedQuantity { get; set; }
        public int ForecastedDemand { get; set; }
        public int RecommendedReorderQuantity { get; set; }
        public DateTime RecommendedReorderDate { get; set; }
        public decimal ConfidenceLevel { get; set; }
        public string ForecastMethod { get; set; } = string.Empty;
        public List<string> Factors { get; set; } = new List<string>();
    }

    /// <summary>
    /// Reorder recommendation
    /// </summary>
    public class ReorderRecommendation
    {
        public int ItemId { get; set; }
        public string ItemName { get; set; } = string.Empty;
        public string ItemCode { get; set; } = string.Empty;
        public int CurrentStock { get; set; }
        public int ReorderLevel { get; set; }
        public int RecommendedOrderQuantity { get; set; }
        public DateTime RecommendedOrderDate { get; set; }
        public string SupplierName { get; set; } = string.Empty;
        public decimal EstimatedCost { get; set; }
        public int LeadTimeDays { get; set; }
        public string Priority { get; set; } = string.Empty; // High, Medium, Low
        public string Reason { get; set; } = string.Empty;
    }
}
