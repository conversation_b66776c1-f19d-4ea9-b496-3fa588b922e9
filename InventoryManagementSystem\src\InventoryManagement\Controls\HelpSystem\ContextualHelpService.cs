using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using Microsoft.Extensions.Logging;

namespace InventoryManagement.Controls.HelpSystem
{
    /// <summary>
    /// Service for providing context-sensitive help within the application
    /// </summary>
    public class ContextualHelpService : IContextualHelpService
    {
        private readonly ILogger<ContextualHelpService> _logger;
        private readonly Dictionary<string, string> _helpTopics;
        private readonly string _helpContentBasePath;
        
        public ContextualHelpService(ILogger<ContextualHelpService> logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _helpTopics = new Dictionary<string, string>();
            _helpContentBasePath = Path.Combine(AppContext.BaseDirectory, "Resources", "Help");
            
            LoadHelpTopics();
        }
        
        /// <summary>
        /// Loads help topics from embedded resources
        /// </summary>
        private void LoadHelpTopics()
        {
            try
            {
                if (Directory.Exists(_helpContentBasePath))
                {
                    foreach (var file in Directory.GetFiles(_helpContentBasePath, "*.md"))
                    {
                        var topicKey = Path.GetFileNameWithoutExtension(file);
                        _helpTopics[topicKey] = file;
                    }
                }
                
                _logger.LogInformation("Loaded {Count} help topics", _helpTopics.Count);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading help topics");
            }
        }
        
        /// <summary>
        /// Shows help content for the specified control context
        /// </summary>
        /// <param name="controlName">Name of the control needing help</param>
        /// <param name="contextKey">Optional additional context</param>
        public async Task ShowHelpAsync(string controlName, string contextKey = null)
        {
            try
            {
                string topicKey = string.IsNullOrEmpty(contextKey) 
                    ? controlName 
                    : $"{controlName}.{contextKey}";
                
                if (_helpTopics.TryGetValue(topicKey, out string helpFilePath))
                {
                    var content = await File.ReadAllTextAsync(helpFilePath);
                    ShowHelpDialog(topicKey, content);
                }
                else if (_helpTopics.TryGetValue(controlName, out helpFilePath))
                {
                    // Fall back to general control help if specific context not found
                    var content = await File.ReadAllTextAsync(helpFilePath);
                    ShowHelpDialog(controlName, content);
                }
                else
                {
                    _logger.LogWarning("No help content found for {ControlName}", controlName);
                    ShowHelpDialog("Help Not Found", 
                        $"Sorry, no help content is available for '{controlName}'.");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error showing help for {ControlName}", controlName);
            }
        }
        
        /// <summary>
        /// Shows context-sensitive help for the element under the cursor
        /// </summary>
        /// <param name="element">UI element requesting help</param>
        public async Task ShowHelpForElementAsync(FrameworkElement element)
        {
            if (element == null)
            {
                return;
            }
            
            try
            {
                // Try to find a suitable name to use as help key
                string helpKey = null;
                
                if (!string.IsNullOrEmpty(element.Name))
                {
                    helpKey = element.Name;
                }
                else if (element.Tag is string tagValue)
                {
                    helpKey = tagValue;
                }
                else if (element.DataContext != null)
                {
                    // Try to infer from data context type name
                    var typeName = element.DataContext.GetType().Name;
                    if (typeName.EndsWith("ViewModel"))
                    {
                        helpKey = typeName.Replace("ViewModel", "");
                    }
                    else
                    {
                        helpKey = typeName;
                    }
                }
                else
                {
                    // Last resort: use control type
                    helpKey = element.GetType().Name;
                }
                
                await ShowHelpAsync(helpKey);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error showing help for element");
            }
        }
        
        /// <summary>
        /// Displays the help dialog with the given content
        /// </summary>
        private void ShowHelpDialog(string title, string content)
        {
            // In a real implementation, this would create and show a WPF dialog
            // For this example, we're just logging the action
            _logger.LogInformation("Showing help dialog: {Title}", title);
        }
    }
}
