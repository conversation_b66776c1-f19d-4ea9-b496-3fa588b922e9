using System;
using System.Data;
using System.Threading.Tasks;
using InventoryManagement.Models.Reports;

namespace InventoryManagement.Services.Export
{
    /// <summary>
    /// Interface for services that export data to various file formats
    /// </summary>
    public interface IDataExportService
    {
        /// <summary>
        /// Exports data to a file in the specified format
        /// </summary>
        /// <param name="data">The data to export</param>
        /// <param name="format">The format to export to</param>
        /// <param name="filePath">Optional file path, will be generated if not provided</param>
        /// <param name="options">Optional export options</param>
        /// <returns>The path to the exported file</returns>
        Task<string> ExportDataAsync(
            DataTable data, 
            ExportFormat format, 
            string filePath = null, 
            ExportOptions options = null);
        
        /// <summary>
        /// Exports a JSON serializable object to a JSON file
        /// </summary>
        /// <param name="data">The data object to serialize and export</param>
        /// <param name="filePath">Optional file path, will be generated if not provided</param>
        /// <param name="options">Optional export options</param>
        /// <returns>The path to the exported file</returns>
        Task<string> ExportToJsonAsync<T>(
            T data, 
            string filePath = null, 
            ExportOptions options = null);
    }
}
