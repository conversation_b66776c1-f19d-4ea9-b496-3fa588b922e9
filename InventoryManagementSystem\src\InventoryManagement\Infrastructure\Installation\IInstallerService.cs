using System;
using System.Threading.Tasks;

namespace InventoryManagement.Infrastructure.Installation
{
    /// <summary>
    /// Interface for installer service operations
    /// </summary>
    public interface IInstallerService
    {
        /// <summary>
        /// Checks if the application needs to be updated
        /// </summary>
        Task<bool> CheckForUpdatesAsync();
        
        /// <summary>
        /// Creates a backup before updating
        /// </summary>
        Task<string> CreateUpdateBackupAsync();
        
        /// <summary>
        /// Creates shortcuts for the application
        /// </summary>
        void CreateShortcuts();
        
        /// <summary>
        /// Initializes automatic startup if configured
        /// </summary>
        void SetupAutoStart();
    }
}
