using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace InventoryManagement.Models
{
    /// <summary>
    /// Represents a return/refund transaction
    /// </summary>
    public class ReturnTransaction
    {
        [Key]
        public int Id { get; set; }

        /// <summary>
        /// Unique return number for tracking
        /// </summary>
        [Required]
        [MaxLength(50)]
        public string ReturnNumber { get; set; } = string.Empty;

        /// <summary>
        /// Original transaction being returned
        /// </summary>
        [Required]
        public int OriginalTransactionId { get; set; }

        /// <summary>
        /// Customer making the return
        /// </summary>
        public int? CustomerId { get; set; }

        /// <summary>
        /// Cashier processing the return
        /// </summary>
        [Required]
        public int CashierId { get; set; }

        /// <summary>
        /// Manager who approved the return (if required)
        /// </summary>
        public int? ApprovedByUserId { get; set; }

        /// <summary>
        /// Date and time of the return
        /// </summary>
        [Required]
        public DateTime ReturnDate { get; set; } = DateTime.Now;

        /// <summary>
        /// Reason for the return
        /// </summary>
        [Required]
        [MaxLength(100)]
        public string ReturnReason { get; set; } = string.Empty;

        /// <summary>
        /// Additional notes about the return
        /// </summary>
        [MaxLength(500)]
        public string Notes { get; set; } = string.Empty;

        /// <summary>
        /// Total amount being refunded
        /// </summary>
        [Required]
        [Column(TypeName = "decimal(18,2)")]
        public decimal RefundAmount { get; set; }

        /// <summary>
        /// Method of refund (Cash, Credit, Store Credit, etc.)
        /// </summary>
        [Required]
        [MaxLength(50)]
        public string RefundMethod { get; set; } = "Cash";

        /// <summary>
        /// Status of the return
        /// </summary>
        [Required]
        public ReturnStatus Status { get; set; } = ReturnStatus.Pending;

        /// <summary>
        /// Whether manager approval was required
        /// </summary>
        public bool RequiredApproval { get; set; } = false;

        /// <summary>
        /// Date when the return was approved
        /// </summary>
        public DateTime? ApprovalDate { get; set; }

        /// <summary>
        /// Whether the items were restocked
        /// </summary>
        public bool ItemsRestocked { get; set; } = false;

        /// <summary>
        /// Date when items were restocked
        /// </summary>
        public DateTime? RestockDate { get; set; }

        /// <summary>
        /// User who restocked the items
        /// </summary>
        public int? RestockedByUserId { get; set; }

        /// <summary>
        /// Receipt number for the refund
        /// </summary>
        [MaxLength(50)]
        public string RefundReceiptNumber { get; set; } = string.Empty;

        /// <summary>
        /// Whether this is a partial return
        /// </summary>
        public bool IsPartialReturn { get; set; } = false;

        /// <summary>
        /// Store credit amount issued (if applicable)
        /// </summary>
        [Column(TypeName = "decimal(18,2)")]
        public decimal StoreCreditAmount { get; set; } = 0;

        /// <summary>
        /// Store credit voucher number (if applicable)
        /// </summary>
        [MaxLength(50)]
        public string StoreCreditVoucherNumber { get; set; } = string.Empty;

        // Navigation properties
        [ForeignKey("OriginalTransactionId")]
        public virtual Transaction OriginalTransaction { get; set; } = new Transaction();

        [ForeignKey("CustomerId")]
        public virtual Customer? Customer { get; set; }

        [ForeignKey("CashierId")]
        public virtual User Cashier { get; set; } = new User();

        [ForeignKey("ApprovedByUserId")]
        public virtual User? ApprovedByUser { get; set; }

        [ForeignKey("RestockedByUserId")]
        public virtual User? RestockedByUser { get; set; }

        /// <summary>
        /// Items being returned
        /// </summary>
        public virtual List<ReturnItem> ReturnItems { get; set; } = new List<ReturnItem>();

        /// <summary>
        /// Check if return is within allowed time period
        /// </summary>
        /// <param name="allowedDays">Number of days allowed for returns</param>
        /// <returns>True if within allowed period</returns>
        public bool IsWithinReturnPeriod(int allowedDays = 30)
        {
            if (OriginalTransaction == null) return false;
            return (DateTime.Now - OriginalTransaction.TransactionDate).TotalDays <= allowedDays;
        }

        /// <summary>
        /// Calculate total refund amount based on return items
        /// </summary>
        /// <returns>Total refund amount</returns>
        public decimal CalculateTotalRefund()
        {
            return ReturnItems?.Sum(ri => ri.RefundAmount) ?? 0;
        }
    }

    /// <summary>
    /// Represents an individual item being returned
    /// </summary>
    public class ReturnItem
    {
        [Key]
        public int Id { get; set; }

        /// <summary>
        /// Return transaction this item belongs to
        /// </summary>
        [Required]
        public int ReturnTransactionId { get; set; }

        /// <summary>
        /// Item being returned
        /// </summary>
        [Required]
        public int ItemId { get; set; }

        /// <summary>
        /// Quantity being returned
        /// </summary>
        [Required]
        public int Quantity { get; set; }

        /// <summary>
        /// Original price per unit
        /// </summary>
        [Required]
        [Column(TypeName = "decimal(18,2)")]
        public decimal OriginalPrice { get; set; }

        /// <summary>
        /// Refund amount per unit (may be different from original price)
        /// </summary>
        [Required]
        [Column(TypeName = "decimal(18,2)")]
        public decimal RefundPricePerUnit { get; set; }

        /// <summary>
        /// Total refund amount for this item
        /// </summary>
        [Required]
        [Column(TypeName = "decimal(18,2)")]
        public decimal RefundAmount { get; set; }

        /// <summary>
        /// Condition of the returned item
        /// </summary>
        [Required]
        [MaxLength(50)]
        public string ItemCondition { get; set; } = "Good";

        /// <summary>
        /// Whether this item can be restocked
        /// </summary>
        public bool CanRestock { get; set; } = true;

        /// <summary>
        /// Reason for this specific item return
        /// </summary>
        [MaxLength(200)]
        public string ItemReturnReason { get; set; } = string.Empty;

        /// <summary>
        /// Location where item should be restocked
        /// </summary>
        public int? RestockLocationId { get; set; }

        // Navigation properties
        [ForeignKey("ReturnTransactionId")]
        public virtual ReturnTransaction ReturnTransaction { get; set; } = new ReturnTransaction();

        [ForeignKey("ItemId")]
        public virtual Item Item { get; set; } = new Item();

        [ForeignKey("RestockLocationId")]
        public virtual Location? RestockLocation { get; set; }
    }

    /// <summary>
    /// Return transaction status
    /// </summary>
    public enum ReturnStatus
    {
        Pending = 0,
        Approved = 1,
        Completed = 2,
        Rejected = 3,
        Cancelled = 4,
        PartiallyCompleted = 5
    }

    /// <summary>
    /// Return reasons
    /// </summary>
    public static class ReturnReasons
    {
        public const string Defective = "Defective";
        public const string WrongItem = "Wrong Item";
        public const string CustomerChanged = "Customer Changed Mind";
        public const string Damaged = "Damaged";
        public const string NotAsDescribed = "Not As Described";
        public const string Duplicate = "Duplicate Purchase";
        public const string Other = "Other";
    }

    /// <summary>
    /// Item conditions for returns
    /// </summary>
    public static class ItemConditions
    {
        public const string New = "New";
        public const string Good = "Good";
        public const string Fair = "Fair";
        public const string Poor = "Poor";
        public const string Damaged = "Damaged";
        public const string Defective = "Defective";
    }

    /// <summary>
    /// Refund methods
    /// </summary>
    public static class RefundMethods
    {
        public const string Cash = "Cash";
        public const string Credit = "Credit";
        public const string StoreCredit = "Store Credit";
        public const string OriginalPayment = "Original Payment Method";
        public const string BankTransfer = "Bank Transfer";
    }
}
