using System;
using System.Collections.Generic;
using System.Data;
using System.Threading.Tasks;
using InventoryManagement.Models.Database;
using Microsoft.Extensions.Logging;
using Npgsql;

namespace InventoryManagement.Services.Database.Monitoring
{
    /// <summary>
    /// Service for monitoring PostgreSQL database health and performance
    /// </summary>
    public class DatabaseHealthMonitor : IDatabaseHealthMonitor
    {
        private readonly string _connectionString;
        private readonly ILogger<DatabaseHealthMonitor> _logger;
        
        public DatabaseHealthMonitor(
            string connectionString,
            ILogger<DatabaseHealthMonitor> logger)
        {
            _connectionString = connectionString ?? throw new ArgumentNullException(nameof(connectionString));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }
        
        /// <summary>
        /// Gets overall database health statistics
        /// </summary>
        public async Task<DatabaseHealthReport> GetDatabaseHealthReportAsync()
        {
            _logger.LogInformation("Generating database health report");
            var report = new DatabaseHealthReport
            {
                TimeStamp = DateTime.Now,
                DatabaseSize = await GetDatabaseSizeAsync(),
                TableSizes = await GetTableSizesAsync(),
                ConnectionStats = await GetConnectionStatsAsync(),
                CacheHitRatio = await GetCacheHitRatioAsync(),
                IndexUsageStats = await GetIndexUsageStatsAsync(),
                DeadTuples = await GetDeadTuplesAsync(),
                SlowQueries = await GetSlowQueriesAsync(),
                VacuumStats = await GetVacuumStatsAsync()
            };
            
            // Calculate overall health score based on various metrics
            report.CalculateHealthScore();
            
            return report;
        }
        
        /// <summary>
        /// Gets the current size of the database
        /// </summary>
        private async Task<DatabaseSizeInfo> GetDatabaseSizeAsync()
        {
            var sizeInfo = new DatabaseSizeInfo();
            
            try
            {
                using var connection = new NpgsqlConnection(_connectionString);
                await connection.OpenAsync();
                
                // Get database name from connection
                string databaseName = connection.Database;
                
                // Get database size
                using var cmd = new NpgsqlCommand(
                    "SELECT pg_size_pretty(pg_database_size(current_database())) as size, " +
                    "pg_database_size(current_database()) as size_bytes", 
                    connection);
                
                using var reader = await cmd.ExecuteReaderAsync();
                if (await reader.ReadAsync())
                {
                    sizeInfo.DatabaseName = databaseName;
                    sizeInfo.Size = reader["size"].ToString();
                    sizeInfo.SizeInBytes = Convert.ToInt64(reader["size_bytes"]);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting database size");
            }
            
            return sizeInfo;
        }
        
        /// <summary>
        /// Gets the sizes of all tables in the database
        /// </summary>
        private async Task<List<TableSizeInfo>> GetTableSizesAsync()
        {
            var tableSizes = new List<TableSizeInfo>();
            
            try
            {
                using var connection = new NpgsqlConnection(_connectionString);
                await connection.OpenAsync();
                
                // Get table sizes
                using var cmd = new NpgsqlCommand(
                    "SELECT tablename, " +
                    "pg_size_pretty(pg_total_relation_size(schemaname || '.' || tablename)) as size, " +
                    "pg_total_relation_size(schemaname || '.' || tablename) as size_bytes, " +
                    "pg_size_pretty(pg_relation_size(schemaname || '.' || tablename)) as table_size, " +
                    "pg_size_pretty(pg_total_relation_size(schemaname || '.' || tablename) - pg_relation_size(schemaname || '.' || tablename)) as index_size, " +
                    "n_live_tup as row_count " +
                    "FROM pg_catalog.pg_tables t " +
                    "JOIN pg_stat_user_tables st ON t.tablename = st.relname " +
                    "WHERE schemaname = 'public' " +
                    "ORDER BY pg_total_relation_size(schemaname || '.' || tablename) DESC", 
                    connection);
                
                using var reader = await cmd.ExecuteReaderAsync();
                while (await reader.ReadAsync())
                {
                    tableSizes.Add(new TableSizeInfo
                    {
                        TableName = reader["tablename"].ToString(),
                        TotalSize = reader["size"].ToString(),
                        SizeInBytes = Convert.ToInt64(reader["size_bytes"]),
                        TableSize = reader["table_size"].ToString(),
                        IndexSize = reader["index_size"].ToString(),
                        RowCount = reader["row_count"] != DBNull.Value ? Convert.ToInt64(reader["row_count"]) : 0
                    });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting table sizes");
            }
            
            return tableSizes;
        }
        
        /// <summary>
        /// Gets statistics about database connections
        /// </summary>
        private async Task<ConnectionStats> GetConnectionStatsAsync()
        {
            var stats = new ConnectionStats();
            
            try
            {
                using var connection = new NpgsqlConnection(_connectionString);
                await connection.OpenAsync();
                
                // Get connection stats
                using var cmd = new NpgsqlCommand(
                    "SELECT count(*) as active_connections, " +
                    "(SELECT setting::int FROM pg_settings WHERE name = 'max_connections') as max_connections " +
                    "FROM pg_stat_activity", 
                    connection);
                
                using var reader = await cmd.ExecuteReaderAsync();
                if (await reader.ReadAsync())
                {
                    stats.ActiveConnections = Convert.ToInt32(reader["active_connections"]);
                    stats.MaxConnections = Convert.ToInt32(reader["max_connections"]);
                    stats.ConnectionUtilization = (double)stats.ActiveConnections / stats.MaxConnections * 100;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting connection stats");
            }
            
            return stats;
        }
        
        /// <summary>
        /// Gets database cache hit ratio statistics
        /// </summary>
        private async Task<CacheHitRatio> GetCacheHitRatioAsync()
        {
            var cacheHitRatio = new CacheHitRatio();
            
            try
            {
                using var connection = new NpgsqlConnection(_connectionString);
                await connection.OpenAsync();
                
                // Get cache hit ratio
                using var cmd = new NpgsqlCommand(
                    "SELECT sum(heap_blks_read) as heap_read, " +
                    "sum(heap_blks_hit) as heap_hit, " +
                    "sum(heap_blks_hit) / (sum(heap_blks_hit) + sum(heap_blks_read)) as ratio " +
                    "FROM pg_statio_user_tables", 
                    connection);
                
                using var reader = await cmd.ExecuteReaderAsync();
                if (await reader.ReadAsync())
                {
                    cacheHitRatio.HeapRead = Convert.ToInt64(reader["heap_read"]);
                    cacheHitRatio.HeapHit = Convert.ToInt64(reader["heap_hit"]);
                    cacheHitRatio.Ratio = reader["ratio"] != DBNull.Value ? 
                        Convert.ToDouble(reader["ratio"]) * 100 : 0;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting cache hit ratio");
            }
            
            return cacheHitRatio;
        }
        
        /// <summary>
        /// Gets statistics about index usage
        /// </summary>
        private async Task<List<IndexUsageInfo>> GetIndexUsageStatsAsync()
        {
            var indexStats = new List<IndexUsageInfo>();
            
            try
            {
                using var connection = new NpgsqlConnection(_connectionString);
                await connection.OpenAsync();
                
                // Get index usage stats
                using var cmd = new NpgsqlCommand(
                    "SELECT " +
                    "t.tablename as table_name, " +
                    "i.indexrelname as index_name, " +
                    "i.idx_scan as scans, " +
                    "pg_size_pretty(pg_relation_size(i.indexrelid::regclass)) as index_size, " +
                    "pg_relation_size(i.indexrelid::regclass) as index_size_bytes " +
                    "FROM pg_stat_user_indexes i " +
                    "JOIN pg_tables t ON i.schemaname = t.schemaname AND i.relname = t.tablename " +
                    "WHERE t.schemaname = 'public' " +
                    "ORDER BY i.idx_scan DESC, pg_relation_size(i.indexrelid::regclass) DESC", 
                    connection);
                
                using var reader = await cmd.ExecuteReaderAsync();
                while (await reader.ReadAsync())
                {
                    indexStats.Add(new IndexUsageInfo
                    {
                        TableName = reader["table_name"].ToString(),
                        IndexName = reader["index_name"].ToString(),
                        Scans = Convert.ToInt64(reader["scans"]),
                        Size = reader["index_size"].ToString(),
                        SizeInBytes = Convert.ToInt64(reader["index_size_bytes"])
                    });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting index usage stats");
            }
            
            return indexStats;
        }
        
        /// <summary>
        /// Gets statistics about dead tuples (rows) in the database
        /// </summary>
        private async Task<List<DeadTupleInfo>> GetDeadTuplesAsync()
        {
            var deadTuples = new List<DeadTupleInfo>();
            
            try
            {
                using var connection = new NpgsqlConnection(_connectionString);
                await connection.OpenAsync();
                
                // Get dead tuple stats
                using var cmd = new NpgsqlCommand(
                    "SELECT " +
                    "relname as table_name, " +
                    "n_dead_tup as dead_tuples, " +
                    "n_live_tup as live_tuples, " +
                    "CASE WHEN n_live_tup > 0 THEN round(n_dead_tup::numeric / n_live_tup::numeric * 100, 2) ELSE 0 END as dead_tuple_percentage " +
                    "FROM pg_stat_user_tables " +
                    "WHERE n_dead_tup > 0 " +
                    "ORDER BY n_dead_tup DESC", 
                    connection);
                
                using var reader = await cmd.ExecuteReaderAsync();
                while (await reader.ReadAsync())
                {
                    deadTuples.Add(new DeadTupleInfo
                    {
                        TableName = reader["table_name"].ToString(),
                        DeadTuples = Convert.ToInt64(reader["dead_tuples"]),
                        LiveTuples = Convert.ToInt64(reader["live_tuples"]),
                        DeadTuplePercentage = Convert.ToDouble(reader["dead_tuple_percentage"])
                    });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting dead tuple stats");
            }
            
            return deadTuples;
        }
        
        /// <summary>
        /// Gets statistics about slow queries
        /// </summary>
        private async Task<List<SlowQueryInfo>> GetSlowQueriesAsync()
        {
            var slowQueries = new List<SlowQueryInfo>();
            
            try
            {
                using var connection = new NpgsqlConnection(_connectionString);
                await connection.OpenAsync();
                
                // We need pg_stat_statements extension for this
                // Check if it's available
                using var checkCmd = new NpgsqlCommand(
                    "SELECT count(*) FROM pg_extension WHERE extname = 'pg_stat_statements'", 
                    connection);
                
                int extensionCount = Convert.ToInt32(await checkCmd.ExecuteScalarAsync());
                
                if (extensionCount > 0)
                {
                    // Get slow query stats
                    using var cmd = new NpgsqlCommand(
                        "SELECT " +
                        "substring(query, 1, 100) as query_text, " +
                        "round(total_exec_time::numeric, 2) as total_time_ms, " +
                        "calls, " +
                        "round(mean_exec_time::numeric, 2) as mean_time_ms, " +
                        "round((100 * total_exec_time / sum(total_exec_time) OVER())::numeric, 2) as percentage " +
                        "FROM pg_stat_statements " +
                        "ORDER BY total_exec_time DESC " +
                        "LIMIT 10", 
                        connection);
                    
                    using var reader = await cmd.ExecuteReaderAsync();
                    while (await reader.ReadAsync())
                    {
                        slowQueries.Add(new SlowQueryInfo
                        {
                            QueryText = reader["query_text"].ToString(),
                            TotalTimeMs = Convert.ToDouble(reader["total_time_ms"]),
                            Calls = Convert.ToInt64(reader["calls"]),
                            MeanTimeMs = Convert.ToDouble(reader["mean_time_ms"]),
                            Percentage = Convert.ToDouble(reader["percentage"])
                        });
                    }
                }
                else
                {
                    _logger.LogWarning("pg_stat_statements extension is not installed, cannot collect slow query statistics");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting slow query stats");
            }
            
            return slowQueries;
        }
        
        /// <summary>
        /// Gets statistics about vacuum operations
        /// </summary>
        private async Task<List<VacuumStats>> GetVacuumStatsAsync()
        {
            var vacuumStats = new List<VacuumStats>();
            
            try
            {
                using var connection = new NpgsqlConnection(_connectionString);
                await connection.OpenAsync();
                
                // Get vacuum stats
                using var cmd = new NpgsqlCommand(
                    "SELECT " +
                    "relname as table_name, " +
                    "last_vacuum, " +
                    "last_autovacuum, " +
                    "last_analyze, " +
                    "last_autoanalyze " +
                    "FROM pg_stat_user_tables " +
                    "ORDER BY coalesce(last_vacuum, last_autovacuum) NULLS FIRST", 
                    connection);
                
                using var reader = await cmd.ExecuteReaderAsync();
                while (await reader.ReadAsync())
                {
                    vacuumStats.Add(new VacuumStats
                    {
                        TableName = reader["table_name"].ToString(),
                        LastVacuum = reader["last_vacuum"] != DBNull.Value ? 
                            Convert.ToDateTime(reader["last_vacuum"]) : (DateTime?)null,
                        LastAutoVacuum = reader["last_autovacuum"] != DBNull.Value ? 
                            Convert.ToDateTime(reader["last_autovacuum"]) : (DateTime?)null,
                        LastAnalyze = reader["last_analyze"] != DBNull.Value ? 
                            Convert.ToDateTime(reader["last_analyze"]) : (DateTime?)null,
                        LastAutoAnalyze = reader["last_autoanalyze"] != DBNull.Value ? 
                            Convert.ToDateTime(reader["last_autoanalyze"]) : (DateTime?)null
                    });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting vacuum stats");
            }
            
            return vacuumStats;
        }
        
        /// <summary>
        /// Performs maintenance operations on the database
        /// </summary>
        public async Task<MaintenanceResult> PerformMaintenanceAsync()
        {
            _logger.LogInformation("Performing database maintenance");
            var result = new MaintenanceResult
            {
                StartTime = DateTime.Now,
                Operations = new List<string>()
            };
            
            try
            {
                using var connection = new NpgsqlConnection(_connectionString);
                await connection.OpenAsync();
                
                // Analyze all tables
                using var analyzeCmd = new NpgsqlCommand("ANALYZE", connection);
                await analyzeCmd.ExecuteNonQueryAsync();
                result.Operations.Add("Analyzed all tables");
                
                // Vacuum analyze tables with high dead tuple percentages
                var tablesToVacuum = await GetTablesNeedingVacuumAsync(connection);
                foreach (var table in tablesToVacuum)
                {
                    using var vacuumCmd = new NpgsqlCommand($"VACUUM ANALYZE {table}", connection);
                    await vacuumCmd.ExecuteNonQueryAsync();
                    result.Operations.Add($"Vacuumed table: {table}");
                }
                
                // Reindex any invalid indexes
                var indexesToReindex = await GetInvalidIndexesAsync(connection);
                foreach (var index in indexesToReindex)
                {
                    using var reindexCmd = new NpgsqlCommand($"REINDEX INDEX {index}", connection);
                    await reindexCmd.ExecuteNonQueryAsync();
                    result.Operations.Add($"Reindexed index: {index}");
                }
                
                result.Success = true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error performing database maintenance");
                result.Success = false;
                result.ErrorMessage = ex.Message;
            }
            
            result.EndTime = DateTime.Now;
            return result;
        }
        
        /// <summary>
        /// Gets tables that need vacuum based on dead tuple percentage
        /// </summary>
        private async Task<List<string>> GetTablesNeedingVacuumAsync(NpgsqlConnection connection)
        {
            var tables = new List<string>();
            
            using var cmd = new NpgsqlCommand(
                "SELECT " +
                "relname as table_name " +
                "FROM pg_stat_user_tables " +
                "WHERE n_live_tup > 0 " +
                "AND n_dead_tup > 0 " +
                "AND (n_dead_tup::float / n_live_tup::float) > 0.2 " + // 20% threshold
                "ORDER BY n_dead_tup DESC", 
                connection);
            
            using var reader = await cmd.ExecuteReaderAsync();
            while (await reader.ReadAsync())
            {
                tables.Add(reader["table_name"].ToString());
            }
            
            return tables;
        }
        
        /// <summary>
        /// Gets invalid indexes that need to be reindexed
        /// </summary>
        private async Task<List<string>> GetInvalidIndexesAsync(NpgsqlConnection connection)
        {
            var indexes = new List<string>();
            
            using var cmd = new NpgsqlCommand(
                "SELECT " +
                "t.relname as tablename, " +
                "i.indexrelname as indexname " +
                "FROM pg_class t, pg_class i, pg_index idx " +
                "WHERE t.oid = idx.indrelid " +
                "AND i.oid = idx.indexrelid " +
                "AND t.relkind = 'r' " +
                "AND idx.indisvalid = false", 
                connection);
            
            using var reader = await cmd.ExecuteReaderAsync();
            while (await reader.ReadAsync())
            {
                indexes.Add(reader["indexname"].ToString());
            }
            
            return indexes;
        }
    }
}
