using System;

namespace InventoryManagement.Models.Reports
{
    /// <summary>
    /// Defines a parameter for a custom report
    /// </summary>
    public class ReportParameter
    {
        /// <summary>
        /// Name of the parameter (used in SQL as @ParameterName)
        /// </summary>
        public string Name { get; set; }
        
        /// <summary>
        /// Display name shown in the UI
        /// </summary>
        public string DisplayName { get; set; }
        
        /// <summary>
        /// Description of the parameter
        /// </summary>
        public string Description { get; set; }
        
        /// <summary>
        /// Data type of the parameter
        /// </summary>
        public ParameterDataType DataType { get; set; }
        
        /// <summary>
        /// Whether this parameter is required
        /// </summary>
        public bool IsRequired { get; set; }
        
        /// <summary>
        /// Default value for the parameter
        /// </summary>
        public string DefaultValue { get; set; }
        
        /// <summary>
        /// For enumerated types, possible values (comma-separated)
        /// </summary>
        public string PossibleValues { get; set; }
        
        /// <summary>
        /// Validation expression
        /// </summary>
        public string ValidationExpression { get; set; }
        
        /// <summary>
        /// Error message when validation fails
        /// </summary>
        public string ValidationErrorMessage { get; set; }
        
        /// <summary>
        /// SQL query to dynamically load possible values
        /// </summary>
        public string ValuesQuery { get; set; }
    }
    
    /// <summary>
    /// Data types for report parameters
    /// </summary>
    public enum ParameterDataType
    {
        String,
        Integer,
        Decimal,
        Boolean,
        Date,
        DateTime,
        Enum
    }
}
