using System.Threading.Tasks;

namespace InventoryManagement.Services.Validation
{
    /// <summary>
    /// Interface for services verifying data integrity across the inventory system
    /// </summary>
    public interface IDataIntegrityService
    {
        /// <summary>
        /// Registers an integrity rule to be checked during validation
        /// </summary>
        void RegisterRule(IDataIntegrityRule rule);
        
        /// <summary>
        /// Performs a comprehensive data integrity check across the system
        /// </summary>
        Task<DataIntegrityReport> VerifyDataIntegrityAsync();
    }
}
