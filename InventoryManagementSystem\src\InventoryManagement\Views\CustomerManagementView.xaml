<UserControl x:Class="InventoryManagement.Views.CustomerManagementView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             mc:Ignorable="d" 
             d:DesignHeight="800" d:DesignWidth="1400">
    
    <UserControl.Resources>
        <Style x:Key="HeaderStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="18"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Foreground" Value="#2C3E50"/>
            <Setter Property="Margin" Value="0,10,0,5"/>
        </Style>
        
        <Style x:Key="ActionButtonStyle" TargetType="Button">
            <Setter Property="Padding" Value="15,8"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="MinWidth" Value="100"/>
        </Style>
        
        <Style x:Key="DataGridStyle" TargetType="DataGrid">
            <Setter Property="AutoGenerateColumns" Value="False"/>
            <Setter Property="CanUserAddRows" Value="False"/>
            <Setter Property="CanUserDeleteRows" Value="False"/>
            <Setter Property="IsReadOnly" Value="True"/>
            <Setter Property="SelectionMode" Value="Single"/>
            <Setter Property="GridLinesVisibility" Value="Horizontal"/>
            <Setter Property="HeadersVisibility" Value="Column"/>
            <Setter Property="AlternatingRowBackground" Value="#F8F9FA"/>
        </Style>
    </UserControl.Resources>

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,20">
            <TextBlock Text="Customer Management" FontSize="24" FontWeight="Bold" Foreground="#2C3E50"/>
            <Button Content="Refresh" Command="{Binding RefreshCommand}" 
                    Style="{StaticResource ActionButtonStyle}" Margin="20,0,0,0"/>
            <Button Content="Export" Command="{Binding ExportCustomersCommand}" 
                    Style="{StaticResource ActionButtonStyle}"/>
        </StackPanel>

        <!-- Main Content -->
        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="2*"/>
                <ColumnDefinition Width="10"/>
                <ColumnDefinition Width="3*"/>
            </Grid.ColumnDefinitions>

            <!-- Customer List Panel -->
            <GroupBox Grid.Column="0" Header="Customers" Padding="15">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <!-- Search and Filter -->
                    <StackPanel Grid.Row="0" Margin="0,0,0,15">
                        <TextBlock Text="Search & Filter" Style="{StaticResource HeaderStyle}"/>
                        
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="10"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                            <TextBox Grid.Row="0" Grid.Column="0" Text="{Binding SearchText, UpdateSourceTrigger=PropertyChanged}" 
                                     Margin="0,5"/>
                            <Button Grid.Row="0" Grid.Column="2" Content="Search" Command="{Binding SearchCustomersCommand}" 
                                    Style="{StaticResource ActionButtonStyle}" Margin="0,5"/>

                            <StackPanel Grid.Row="1" Grid.Column="0" Grid.ColumnSpan="3" Orientation="Horizontal" Margin="0,10">
                                <TextBlock Text="Type:" VerticalAlignment="Center" Margin="0,0,10,0"/>
                                <ComboBox ItemsSource="{Binding CustomerTypes}" SelectedItem="{Binding SelectedCustomerType}" 
                                          Width="120" Margin="0,0,20,0"/>
                                <CheckBox Content="Active Only" IsChecked="{Binding ShowActiveOnly}"/>
                            </StackPanel>
                        </Grid>
                    </StackPanel>

                    <!-- Customer Count -->
                    <TextBlock Grid.Row="1" Text="{Binding CustomersView.Count, StringFormat='Total: {0} customers'}" 
                               Foreground="Gray" Margin="0,0,0,10"/>

                    <!-- Customer List -->
                    <DataGrid Grid.Row="2" ItemsSource="{Binding CustomersView}" 
                              SelectedItem="{Binding SelectedCustomer}"
                              Style="{StaticResource DataGridStyle}">
                        <DataGrid.Columns>
                            <DataGridTextColumn Header="Customer #" Binding="{Binding CustomerNumber}" Width="100"/>
                            <DataGridTextColumn Header="Name" Binding="{Binding Name}" Width="*"/>
                            <DataGridTextColumn Header="Type" Binding="{Binding CustomerType}" Width="80"/>
                            <DataGridTextColumn Header="Phone" Binding="{Binding PhoneNumber}" Width="120"/>
                            <DataGridCheckBoxColumn Header="Active" Binding="{Binding IsActive}" Width="60"/>
                        </DataGrid.Columns>
                    </DataGrid>

                    <!-- Action Buttons -->
                    <StackPanel Grid.Row="3" Orientation="Horizontal" Margin="0,15,0,0">
                        <Button Content="Add Customer" Command="{Binding AddCustomerCommand}" 
                                Style="{StaticResource ActionButtonStyle}"/>
                        <Button Content="Edit" Command="{Binding EditCustomerCommand}" 
                                Style="{StaticResource ActionButtonStyle}"/>
                        <Button Content="Delete" Command="{Binding DeleteCustomerCommand}" 
                                Style="{StaticResource ActionButtonStyle}"/>
                    </StackPanel>
                </Grid>
            </GroupBox>

            <!-- Customer Details Panel -->
            <TabControl Grid.Column="2">
                
                <!-- Customer Details Tab -->
                <TabItem Header="Customer Details">
                    <ScrollViewer VerticalScrollBarVisibility="Auto" Padding="15">
                        <StackPanel>
                            <!-- Customer Information -->
                            <TextBlock Text="Customer Information" Style="{StaticResource HeaderStyle}"/>
                            
                            <Grid IsEnabled="{Binding CanEditCustomerDetails}">
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>

                                <TextBlock Grid.Row="0" Grid.Column="0" Text="Customer Number:" Margin="0,5" VerticalAlignment="Center"/>
                                <TextBox Grid.Row="0" Grid.Column="1" Text="{Binding CustomerDetails.CustomerNumber}" 
                                         Margin="10,5"/>

                                <TextBlock Grid.Row="1" Grid.Column="0" Text="Name:" Margin="0,5" VerticalAlignment="Center"/>
                                <TextBox Grid.Row="1" Grid.Column="1" Text="{Binding CustomerDetails.Name}" Margin="10,5"/>

                                <TextBlock Grid.Row="2" Grid.Column="0" Text="Customer Type:" Margin="0,5" VerticalAlignment="Center"/>
                                <ComboBox Grid.Row="2" Grid.Column="1" ItemsSource="{Binding CustomerTypes}" 
                                          SelectedItem="{Binding CustomerDetails.CustomerType}" Margin="10,5"/>

                                <TextBlock Grid.Row="3" Grid.Column="0" Text="Phone Number:" Margin="0,5" VerticalAlignment="Center"/>
                                <TextBox Grid.Row="3" Grid.Column="1" Text="{Binding CustomerDetails.PhoneNumber}" Margin="10,5"/>

                                <TextBlock Grid.Row="4" Grid.Column="0" Text="Email:" Margin="0,5" VerticalAlignment="Center"/>
                                <TextBox Grid.Row="4" Grid.Column="1" Text="{Binding CustomerDetails.Email}" Margin="10,5"/>

                                <TextBlock Grid.Row="5" Grid.Column="0" Text="Address:" Margin="0,5" VerticalAlignment="Top"/>
                                <TextBox Grid.Row="5" Grid.Column="1" Text="{Binding CustomerDetails.Address}" 
                                         Height="60" TextWrapping="Wrap" AcceptsReturn="True" Margin="10,5"/>

                                <TextBlock Grid.Row="6" Grid.Column="0" Text="Credit Limit:" Margin="0,5" VerticalAlignment="Center"/>
                                <TextBox Grid.Row="6" Grid.Column="1" Text="{Binding CustomerDetails.CreditLimit, StringFormat=C}" Margin="10,5"/>

                                <TextBlock Grid.Row="7" Grid.Column="0" Text="Discount Percentage:" Margin="0,5" VerticalAlignment="Center"/>
                                <TextBox Grid.Row="7" Grid.Column="1" Text="{Binding CustomerDetails.DiscountPercentage, StringFormat=P2}" Margin="10,5"/>

                                <CheckBox Grid.Row="8" Grid.Column="1" Content="Active" IsChecked="{Binding CustomerDetails.IsActive}" Margin="10,5"/>

                                <TextBlock Grid.Row="9" Grid.Column="0" Text="Notes:" Margin="0,5" VerticalAlignment="Top"/>
                                <TextBox Grid.Row="9" Grid.Column="1" Text="{Binding CustomerDetails.Notes}" 
                                         Height="80" TextWrapping="Wrap" AcceptsReturn="True" Margin="10,5"/>
                            </Grid>

                            <!-- Action Buttons -->
                            <StackPanel Orientation="Horizontal" Margin="0,20">
                                <Button Content="Save" Command="{Binding SaveCustomerCommand}" 
                                        Style="{StaticResource ActionButtonStyle}"/>
                                <Button Content="Cancel" Command="{Binding CancelEditCommand}" 
                                        Style="{StaticResource ActionButtonStyle}"/>
                            </StackPanel>

                            <!-- Customer Summary -->
                            <Border BorderBrush="#E0E0E0" BorderThickness="1" Padding="15" Margin="0,20,0,0">
                                <StackPanel>
                                    <TextBlock Text="Customer Summary" Style="{StaticResource HeaderStyle}"/>
                                    
                                    <Grid>
                                        <Grid.RowDefinitions>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="Auto"/>
                                        </Grid.RowDefinitions>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>

                                        <TextBlock Grid.Row="0" Grid.Column="0" Text="{Binding CustomerSummary.TotalPurchases, StringFormat='Total Purchases: {0:C}'}" Margin="0,5"/>
                                        <TextBlock Grid.Row="0" Grid.Column="1" Text="{Binding CustomerSummary.TotalTransactions, StringFormat='Total Transactions: {0}'}" Margin="0,5"/>
                                        
                                        <TextBlock Grid.Row="1" Grid.Column="0" Text="{Binding CustomerSummary.AverageTransactionValue, StringFormat='Average Transaction: {0:C}'}" Margin="0,5"/>
                                        <TextBlock Grid.Row="1" Grid.Column="1" Text="{Binding CustomerSummary.LastPurchaseDate, StringFormat='Last Purchase: {0:d}'}" Margin="0,5"/>
                                        
                                        <TextBlock Grid.Row="2" Grid.Column="0" Text="{Binding CustomerSummary.CurrentCreditBalance, StringFormat='Credit Balance: {0:C}'}" Margin="0,5"/>
                                        <TextBlock Grid.Row="2" Grid.Column="1" Text="{Binding CustomerSummary.TotalDiscountsReceived, StringFormat='Total Discounts: {0:C}'}" Margin="0,5"/>
                                        
                                        <TextBlock Grid.Row="3" Grid.Column="0" Text="{Binding CustomerSummary.TotalReturns, StringFormat='Total Returns: {0:C}'}" Margin="0,5"/>
                                        <TextBlock Grid.Row="3" Grid.Column="1" Text="{Binding CustomerSummary.LoyaltyPoints, StringFormat='Loyalty Points: {0}'}" Margin="0,5"/>
                                    </Grid>
                                </StackPanel>
                            </Border>
                        </StackPanel>
                    </ScrollViewer>
                </TabItem>

                <!-- Transaction History Tab -->
                <TabItem Header="Transaction History">
                    <Grid Padding="15">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>

                        <TextBlock Grid.Row="0" Text="Recent Transactions" Style="{StaticResource HeaderStyle}"/>
                        
                        <DataGrid Grid.Row="1" ItemsSource="{Binding CustomerTransactions}" 
                                  SelectedItem="{Binding SelectedTransaction}"
                                  Style="{StaticResource DataGridStyle}">
                            <DataGrid.Columns>
                                <DataGridTextColumn Header="Transaction #" Binding="{Binding Id}" Width="100"/>
                                <DataGridTextColumn Header="Date" Binding="{Binding TransactionDate, StringFormat=d}" Width="100"/>
                                <DataGridTextColumn Header="Type" Binding="{Binding Type}" Width="80"/>
                                <DataGridTextColumn Header="Total" Binding="{Binding Total, StringFormat=C}" Width="100"/>
                                <DataGridTextColumn Header="Payment Method" Binding="{Binding PaymentMethod}" Width="120"/>
                                <DataGridTextColumn Header="Status" Binding="{Binding Status}" Width="80"/>
                            </DataGrid.Columns>
                        </DataGrid>
                    </Grid>
                </TabItem>
            </TabControl>
        </Grid>

        <!-- Loading Overlay -->
        <Grid Grid.Row="0" Grid.RowSpan="2" Background="#80000000">
            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center" Background="White" Padding="30">
                <ProgressBar IsIndeterminate="True" Width="200" Height="4" Margin="0,0,0,10"/>
                <TextBlock Text="Loading customers..." HorizontalAlignment="Center"/>
            </StackPanel>
        </Grid>
    </Grid>
</UserControl>
