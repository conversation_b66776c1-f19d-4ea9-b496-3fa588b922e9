using Microsoft.Extensions.DependencyInjection;
using System;

namespace InventoryManagement.Infrastructure
{
    /// <summary>
    /// Service locator implementation for WPF application to resolve dependencies
    /// </summary>
    public class ServiceLocator
    {
        private static ServiceLocator _current;
        private IServiceProvider _serviceProvider;

        /// <summary>
        /// Gets the current service locator instance
        /// </summary>
        public static ServiceLocator Current => _current ?? (_current = new ServiceLocator());

        /// <summary>
        /// Initializes the service locator with a service provider
        /// </summary>
        /// <param name="serviceProvider">Service provider to use for resolving dependencies</param>
        public void Initialize(IServiceProvider serviceProvider)
        {
            _serviceProvider = serviceProvider ?? throw new ArgumentNullException(nameof(serviceProvider));
        }

        /// <summary>
        /// Gets a service of the specified type
        /// </summary>
        /// <typeparam name="T">Service type to resolve</typeparam>
        /// <returns>Resolved service instance</returns>
        public T GetService<T>() where T : class
        {
            if (_serviceProvider == null)
            {
                throw new InvalidOperationException("Service provider has not been initialized. Call Initialize() first.");
            }

            return _serviceProvider.GetRequiredService<T>();
        }

        /// <summary>
        /// Gets a service of the specified type
        /// </summary>
        /// <param name="serviceType">Service type to resolve</param>
        /// <returns>Resolved service instance</returns>
        public object GetService(Type serviceType)
        {
            if (_serviceProvider == null)
            {
                throw new InvalidOperationException("Service provider has not been initialized. Call Initialize() first.");
            }

            return _serviceProvider.GetRequiredService(serviceType);
        }
    }
}
