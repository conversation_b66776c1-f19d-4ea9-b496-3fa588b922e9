using InventoryManagement.Models.Hardware;
using Microsoft.Extensions.Logging;
using System.Drawing;
using System.Drawing.Printing;
using System.IO.Ports;
using System.Management;
using System.Text;

namespace InventoryManagement.Services.Hardware
{
    /// <summary>
    /// Hardware service for receipt printer integration
    /// Supports thermal printers, dot matrix, and standard Windows printers
    /// </summary>
    public class PrinterHardwareService : IPrinterHardwareService, IDisposable
    {
        private readonly ILogger<PrinterHardwareService> _logger;
        private PrinterConfiguration? _currentConfiguration;
        private SerialPort? _serialPort;
        private PrintDocument? _printDocument;
        private bool _isConnected = false;
        private bool _isPrinting = false;
        private bool _disposed = false;
        private readonly object _lockObject = new object();
        private int _printJobsToday = 0;
        private DateTime _lastPrintTime = DateTime.MinValue;
        private string? _lastError;
        private string? _currentPrintContent;

        public event EventHandler<PrinterStatusChangedEventArgs>? PrinterStatusChanged;
        public event EventHandler<PrintJobCompletedEventArgs>? PrintJobCompleted;

        public bool IsConnected => _isConnected;
        public bool IsPrinting => _isPrinting;
        public PrinterConfiguration? CurrentConfiguration => _currentConfiguration;

        public PrinterHardwareService(ILogger<PrinterHardwareService> logger)
        {
            _logger = logger;
        }

        public async Task<bool> InitializeAsync(PrinterConfiguration configuration)
        {
            try
            {
                _logger.LogInformation("Initializing printer: {PrinterName}", configuration.Name);
                
                if (_isConnected)
                {
                    await DisconnectAsync();
                }

                _currentConfiguration = configuration;

                switch (configuration.ConnectionType.ToUpper())
                {
                    case "SERIAL":
                        return await InitializeSerialPrinterAsync(configuration);
                    case "USB":
                    case "NETWORK":
                        return await InitializeWindowsPrinterAsync(configuration);
                    default:
                        _logger.LogWarning("Unsupported printer connection type: {ConnectionType}", configuration.ConnectionType);
                        return false;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error initializing printer");
                _lastError = ex.Message;
                return false;
            }
        }

        private async Task<bool> InitializeSerialPrinterAsync(PrinterConfiguration config)
        {
            try
            {
                if (string.IsNullOrEmpty(config.ComPort))
                {
                    _logger.LogError("COM port not specified for serial printer");
                    return false;
                }

                _serialPort = new SerialPort(config.ComPort, config.BaudRate, Parity.None, 8, StopBits.One);
                _serialPort.ErrorReceived += SerialPort_ErrorReceived;

                _serialPort.Open();
                _isConnected = true;

                _logger.LogInformation("Serial printer connected on {ComPort} at {BaudRate} baud", config.ComPort, config.BaudRate);
                OnPrinterStatusChanged(true, true, true, "Serial printer connected successfully");

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error initializing serial printer on {ComPort}", config.ComPort);
                _lastError = ex.Message;
                return false;
            }
        }

        private async Task<bool> InitializeWindowsPrinterAsync(PrinterConfiguration config)
        {
            try
            {
                _printDocument = new PrintDocument();
                
                if (!string.IsNullOrEmpty(config.PrinterDriverName))
                {
                    _printDocument.PrinterSettings.PrinterName = config.PrinterDriverName;
                }

                // Verify printer exists and is available
                if (!_printDocument.PrinterSettings.IsValid)
                {
                    _logger.LogError("Printer not found or not available: {PrinterName}", config.PrinterDriverName);
                    return false;
                }

                _printDocument.PrintPage += PrintDocument_PrintPage;
                _printDocument.EndPrint += PrintDocument_EndPrint;

                _isConnected = true;
                _logger.LogInformation("Windows printer initialized: {PrinterName}", config.PrinterDriverName);
                OnPrinterStatusChanged(true, true, true, "Windows printer connected successfully");

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error initializing Windows printer");
                _lastError = ex.Message;
                return false;
            }
        }

        public async Task<PrintResult> PrintReceiptAsync(string receiptContent, int copies = 1)
        {
            var result = new PrintResult
            {
                JobId = Guid.NewGuid().ToString(),
                PrintTime = DateTime.Now
            };

            try
            {
                if (!_isConnected)
                {
                    result.Message = "Printer not connected";
                    return result;
                }

                if (string.IsNullOrEmpty(receiptContent))
                {
                    result.Message = "Receipt content is empty";
                    return result;
                }

                _isPrinting = true;
                _currentPrintContent = receiptContent;

                if (_currentConfiguration?.ConnectionType.ToUpper() == "SERIAL")
                {
                    result = await PrintToSerialPrinterAsync(receiptContent, copies);
                }
                else
                {
                    result = await PrintToWindowsPrinterAsync(receiptContent, copies);
                }

                if (result.IsSuccessful)
                {
                    _printJobsToday++;
                    _lastPrintTime = DateTime.Now;

                    // Open cash drawer if configured
                    if (_currentConfiguration?.EnableCashDrawer == true)
                    {
                        await OpenCashDrawerAsync();
                    }
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error printing receipt");
                result.IsSuccessful = false;
                result.Message = ex.Message;
                result.ErrorCode = "PRINT_ERROR";
                _lastError = ex.Message;
                return result;
            }
            finally
            {
                _isPrinting = false;
                OnPrintJobCompleted(result.JobId, result.IsSuccessful, result.Message);
            }
        }

        private async Task<PrintResult> PrintToSerialPrinterAsync(string content, int copies)
        {
            var result = new PrintResult { IsSuccessful = false };

            try
            {
                if (_serialPort == null || !_serialPort.IsOpen)
                {
                    result.Message = "Serial port not available";
                    return result;
                }

                for (int i = 0; i < copies; i++)
                {
                    // Send content to thermal printer
                    byte[] data = Encoding.UTF8.GetBytes(content);
                    _serialPort.Write(data, 0, data.Length);

                    // Send cut command if auto-cut is enabled
                    if (_currentConfiguration?.AutoCut == true)
                    {
                        await CutPaperAsync();
                    }

                    await Task.Delay(100); // Small delay between copies
                }

                result.IsSuccessful = true;
                result.Message = $"Successfully printed {copies} copy(ies)";
                result.PagesPrinted = copies;

                _logger.LogInformation("Receipt printed successfully to serial printer");
            }
            catch (Exception ex)
            {
                result.Message = ex.Message;
                _logger.LogError(ex, "Error printing to serial printer");
            }

            return result;
        }

        private async Task<PrintResult> PrintToWindowsPrinterAsync(string content, int copies)
        {
            var result = new PrintResult { IsSuccessful = false };

            try
            {
                if (_printDocument == null)
                {
                    result.Message = "Print document not initialized";
                    return result;
                }

                _printDocument.PrinterSettings.Copies = (short)copies;
                _currentPrintContent = content;

                _printDocument.Print();

                result.IsSuccessful = true;
                result.Message = $"Successfully sent {copies} copy(ies) to printer";
                result.PagesPrinted = copies;

                _logger.LogInformation("Receipt sent to Windows printer successfully");
            }
            catch (Exception ex)
            {
                result.Message = ex.Message;
                _logger.LogError(ex, "Error printing to Windows printer");
            }

            return result;
        }

        public async Task<PrintResult> PrintTextAsync(string content, TextFormatting? formatting = null)
        {
            // For now, delegate to PrintReceiptAsync
            // In a full implementation, this would handle specific text formatting
            return await PrintReceiptAsync(content, 1);
        }

        public async Task<bool> OpenCashDrawerAsync()
        {
            try
            {
                if (!_isConnected || _currentConfiguration?.EnableCashDrawer != true)
                {
                    return false;
                }

                if (_serialPort != null && _serialPort.IsOpen)
                {
                    // ESC/POS command to open cash drawer
                    byte[] openDrawerCommand = { 0x1B, 0x70, (byte)_currentConfiguration.CashDrawerPin, 0x19, 0x19 };
                    _serialPort.Write(openDrawerCommand, 0, openDrawerCommand.Length);
                    
                    _logger.LogInformation("Cash drawer opened");
                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error opening cash drawer");
                return false;
            }
        }

        public async Task<bool> CutPaperAsync()
        {
            try
            {
                if (_serialPort != null && _serialPort.IsOpen)
                {
                    // ESC/POS command for paper cut
                    byte[] cutCommand = { 0x1D, 0x56, 0x00 }; // Full cut
                    _serialPort.Write(cutCommand, 0, cutCommand.Length);
                    
                    _logger.LogInformation("Paper cut command sent");
                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error cutting paper");
                return false;
            }
        }

        public async Task<List<PrinterDevice>> DiscoverPrintersAsync()
        {
            var devices = new List<PrinterDevice>();

            try
            {
                // Discover Windows printers
                foreach (string printerName in PrinterSettings.InstalledPrinters)
                {
                    devices.Add(new PrinterDevice
                    {
                        DeviceId = printerName,
                        Name = printerName,
                        ConnectionType = "Windows",
                        IsAvailable = true,
                        IsDefault = printerName == new PrinterSettings().PrinterName
                    });
                }

                // Discover serial ports for thermal printers
                var serialPorts = SerialPort.GetPortNames();
                foreach (var port in serialPorts)
                {
                    devices.Add(new PrinterDevice
                    {
                        DeviceId = port,
                        Name = $"Thermal Printer ({port})",
                        ConnectionType = "Serial",
                        Port = port,
                        IsAvailable = true
                    });
                }

                _logger.LogInformation("Discovered {Count} printer devices", devices.Count);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error discovering printer devices");
            }

            return devices;
        }

        public async Task<PrinterTestResult> TestPrinterAsync()
        {
            var result = new PrinterTestResult();

            try
            {
                if (!_isConnected)
                {
                    result.IsSuccessful = false;
                    result.Message = "Printer not connected";
                    return result;
                }

                // Test basic connectivity
                result.CanPrint = true;
                result.HasPaper = true; // Assume paper is available
                result.CashDrawerConnected = _currentConfiguration?.EnableCashDrawer == true;
                result.IsSuccessful = true;
                result.Message = "Printer test completed successfully";

                _logger.LogInformation("Printer test completed successfully");
            }
            catch (Exception ex)
            {
                result.IsSuccessful = false;
                result.Message = ex.Message;
                _logger.LogError(ex, "Printer test failed");
            }

            return result;
        }

        public async Task<PrintResult> PrintTestPageAsync()
        {
            string testContent = $@"
=== TEST RECEIPT ===
Tom General Trading
Test Print
Date: {DateTime.Now:yyyy-MM-dd HH:mm:ss}
Printer: {_currentConfiguration?.Name}
Connection: {_currentConfiguration?.ConnectionType}
===================
";
            return await PrintReceiptAsync(testContent, 1);
        }

        public async Task DisconnectAsync()
        {
            try
            {
                _isPrinting = false;
                
                if (_serialPort != null)
                {
                    if (_serialPort.IsOpen)
                    {
                        _serialPort.Close();
                    }
                    _serialPort.Dispose();
                    _serialPort = null;
                }

                if (_printDocument != null)
                {
                    _printDocument.Dispose();
                    _printDocument = null;
                }

                _isConnected = false;
                OnPrinterStatusChanged(false, false, false, "Printer disconnected");
                _logger.LogInformation("Printer disconnected");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error disconnecting printer");
            }
        }

        public PrinterStatus GetStatus()
        {
            return new PrinterStatus
            {
                IsConnected = _isConnected,
                IsOnline = _isConnected,
                IsPrinting = _isPrinting,
                HasPaper = true, // Would need hardware feedback for accurate status
                HasError = !string.IsNullOrEmpty(_lastError),
                ConnectionType = _currentConfiguration?.ConnectionType ?? "Unknown",
                DeviceInfo = _currentConfiguration?.Name ?? "Unknown",
                PrintJobsToday = _printJobsToday,
                LastError = _lastError,
                LastPrintTime = _lastPrintTime
            };
        }

        private void SerialPort_ErrorReceived(object sender, SerialErrorReceivedEventArgs e)
        {
            _logger.LogError("Serial printer error: {Error}", e.EventType);
            _lastError = $"Serial printer error: {e.EventType}";
            OnPrinterStatusChanged(false, false, false, $"Serial printer error: {e.EventType}");
        }

        private void PrintDocument_PrintPage(object sender, PrintPageEventArgs e)
        {
            try
            {
                if (string.IsNullOrEmpty(_currentPrintContent) || e.Graphics == null)
                    return;

                // Simple text printing - in production, you'd want more sophisticated formatting
                using (Font font = new Font("Courier New", 8))
                {
                    e.Graphics.DrawString(_currentPrintContent, font, Brushes.Black, e.MarginBounds);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in PrintPage event");
            }
        }

        private void PrintDocument_EndPrint(object sender, PrintEventArgs e)
        {
            _logger.LogInformation("Print job completed");
        }

        private void OnPrinterStatusChanged(bool isConnected, bool isOnline, bool hasPaper, string message)
        {
            PrinterStatusChanged?.Invoke(this, new PrinterStatusChangedEventArgs
            {
                IsConnected = isConnected,
                IsOnline = isOnline,
                HasPaper = hasPaper,
                StatusMessage = message,
                StatusTime = DateTime.Now
            });
        }

        private void OnPrintJobCompleted(string jobId, bool isSuccessful, string message)
        {
            PrintJobCompleted?.Invoke(this, new PrintJobCompletedEventArgs
            {
                JobId = jobId,
                IsSuccessful = isSuccessful,
                Message = message,
                CompletionTime = DateTime.Now
            });
        }

        public void Dispose()
        {
            if (!_disposed)
            {
                DisconnectAsync().Wait();
                _disposed = true;
            }
        }
    }
}
