using System.ComponentModel.DataAnnotations;

namespace InventoryManagement.Models.Hardware
{
    /// <summary>
    /// Configuration settings for receipt printers
    /// </summary>
    public class PrinterConfiguration
    {
        public int Id { get; set; }
        
        [Required]
        [MaxLength(100)]
        public string Name { get; set; } = string.Empty;
        
        [Required]
        [MaxLength(50)]
        public string PrinterType { get; set; } = "Thermal"; // Thermal, Dot Matrix, Inkjet
        
        [Required]
        [MaxLength(50)]
        public string ConnectionType { get; set; } = "USB"; // USB, Serial, Network, Bluetooth
        
        [MaxLength(20)]
        public string ComPort { get; set; } = string.Empty; // For serial connections
        
        public int BaudRate { get; set; } = 9600; // For serial connections
        
        [MaxLength(100)]
        public string DeviceId { get; set; } = string.Empty; // USB device identifier or network IP
        
        [MaxLength(200)]
        public string PrinterDriverName { get; set; } = string.Empty; // Windows printer driver name
        
        public int PaperWidth { get; set; } = 80; // Paper width in mm (58mm, 80mm common for thermal)
        
        public int CharactersPerLine { get; set; } = 48; // Characters per line based on paper width
        
        public bool AutoCut { get; set; } = true; // Automatic paper cutting
        
        public bool EnableCashDrawer { get; set; } = false; // Open cash drawer after printing
        
        public int CashDrawerPin { get; set; } = 0; // Cash drawer pin (0 or 1)
        
        public bool PrintLogo { get; set; } = false; // Print company logo on receipts
        
        [MaxLength(500)]
        public string LogoPath { get; set; } = string.Empty; // Path to logo image file
        
        public int PrintDensity { get; set; } = 8; // Print density (1-15 for thermal printers)
        
        public int PrintSpeed { get; set; } = 4; // Print speed (1-9 for thermal printers)
        
        public bool IsActive { get; set; } = true;
        
        public bool IsDefault { get; set; } = false;
        
        public DateTime CreatedAt { get; set; } = DateTime.Now;
        
        public DateTime? LastModifiedAt { get; set; }
        
        /// <summary>
        /// Number of copies to print by default
        /// </summary>
        public int DefaultCopies { get; set; } = 1;
        
        /// <summary>
        /// Margin settings for receipt formatting
        /// </summary>
        public int TopMargin { get; set; } = 0;
        public int BottomMargin { get; set; } = 3;
        public int LeftMargin { get; set; } = 0;
        public int RightMargin { get; set; } = 0;
        
        /// <summary>
        /// Additional printer-specific settings as JSON
        /// </summary>
        public string? AdditionalSettings { get; set; }
    }
    
    /// <summary>
    /// Printer types
    /// </summary>
    public static class PrinterTypes
    {
        public const string Thermal = "Thermal";
        public const string DotMatrix = "DotMatrix";
        public const string Inkjet = "Inkjet";
        public const string Laser = "Laser";
    }
    
    /// <summary>
    /// Printer connection types
    /// </summary>
    public static class PrinterConnectionTypes
    {
        public const string USB = "USB";
        public const string Serial = "Serial";
        public const string Network = "Network";
        public const string Bluetooth = "Bluetooth";
        public const string Parallel = "Parallel";
    }
    
    /// <summary>
    /// Common paper widths for thermal printers
    /// </summary>
    public static class PaperWidths
    {
        public const int Width58mm = 58;
        public const int Width80mm = 80;
        public const int Width112mm = 112;
    }
}
