using InventoryManagement.Models.Reports;

namespace InventoryManagement.Services.Reports
{
    /// <summary>
    /// Interface for financial reporting services
    /// </summary>
    public interface IFinancialReportService
    {
        /// <summary>
        /// Generate comprehensive financial report
        /// </summary>
        /// <param name="parameters">Report parameters</param>
        /// <returns>Financial report</returns>
        Task<FinancialReport> GenerateFinancialReportAsync(FinancialReportParameters parameters);

        /// <summary>
        /// Generate daily financial summary
        /// </summary>
        /// <param name="date">Date for the report</param>
        /// <param name="locationIds">Location IDs to include</param>
        /// <returns>Daily financial summary</returns>
        Task<DailyFinancialSummary> GenerateDailyFinancialSummaryAsync(DateTime date, List<int>? locationIds = null);

        /// <summary>
        /// Get payment method breakdown
        /// </summary>
        /// <param name="fromDate">Start date</param>
        /// <param name="toDate">End date</param>
        /// <param name="locationIds">Location IDs to include</param>
        /// <returns>Payment method summary</returns>
        Task<List<PaymentMethodSummary>> GetPaymentMethodBreakdownAsync(DateTime fromDate, DateTime toDate, List<int>? locationIds = null);

        /// <summary>
        /// Get category profitability analysis
        /// </summary>
        /// <param name="fromDate">Start date</param>
        /// <param name="toDate">End date</param>
        /// <param name="locationIds">Location IDs to include</param>
        /// <returns>Category profitability data</returns>
        Task<List<CategoryProfitability>> GetCategoryProfitabilityAsync(DateTime fromDate, DateTime toDate, List<int>? locationIds = null);

        /// <summary>
        /// Get location profitability analysis
        /// </summary>
        /// <param name="fromDate">Start date</param>
        /// <param name="toDate">End date</param>
        /// <returns>Location profitability data</returns>
        Task<List<LocationProfitability>> GetLocationProfitabilityAsync(DateTime fromDate, DateTime toDate);

        /// <summary>
        /// Get cashier financial performance
        /// </summary>
        /// <param name="fromDate">Start date</param>
        /// <param name="toDate">End date</param>
        /// <param name="cashierIds">Specific cashier IDs (optional)</param>
        /// <returns>Cashier financial performance data</returns>
        Task<List<CashierFinancialPerformance>> GetCashierFinancialPerformanceAsync(DateTime fromDate, DateTime toDate, List<int>? cashierIds = null);

        /// <summary>
        /// Get cash register reconciliation
        /// </summary>
        /// <param name="date">Date for reconciliation</param>
        /// <param name="cashierId">Cashier ID (optional)</param>
        /// <returns>Cash register reconciliation data</returns>
        Task<List<CashRegisterReconciliation>> GetCashRegisterReconciliationAsync(DateTime date, int? cashierId = null);

        /// <summary>
        /// Get financial trends over time
        /// </summary>
        /// <param name="fromDate">Start date</param>
        /// <param name="toDate">End date</param>
        /// <param name="grouping">Time grouping</param>
        /// <returns>Financial trend data</returns>
        Task<List<DailyFinancialSummary>> GetFinancialTrendsAsync(DateTime fromDate, DateTime toDate, FinancialReportGrouping grouping);

        /// <summary>
        /// Compare financial performance between periods
        /// </summary>
        /// <param name="currentFromDate">Current period start</param>
        /// <param name="currentToDate">Current period end</param>
        /// <param name="previousFromDate">Previous period start</param>
        /// <param name="previousToDate">Previous period end</param>
        /// <returns>Financial comparison data</returns>
        Task<FinancialComparison> CompareFinancialPerformanceAsync(DateTime currentFromDate, DateTime currentToDate, DateTime previousFromDate, DateTime previousToDate);

        /// <summary>
        /// Get profit and loss statement
        /// </summary>
        /// <param name="fromDate">Start date</param>
        /// <param name="toDate">End date</param>
        /// <param name="locationIds">Location IDs to include</param>
        /// <returns>Profit and loss data</returns>
        Task<ProfitAndLossStatement> GetProfitAndLossStatementAsync(DateTime fromDate, DateTime toDate, List<int>? locationIds = null);

        /// <summary>
        /// Get cash flow statement
        /// </summary>
        /// <param name="fromDate">Start date</param>
        /// <param name="toDate">End date</param>
        /// <param name="locationIds">Location IDs to include</param>
        /// <returns>Cash flow data</returns>
        Task<CashFlowStatement> GetCashFlowStatementAsync(DateTime fromDate, DateTime toDate, List<int>? locationIds = null);

        /// <summary>
        /// Get financial KPIs
        /// </summary>
        /// <param name="fromDate">Start date</param>
        /// <param name="toDate">End date</param>
        /// <param name="locationIds">Location IDs to include</param>
        /// <returns>Financial KPI data</returns>
        Task<List<FinancialKPI>> GetFinancialKPIsAsync(DateTime fromDate, DateTime toDate, List<int>? locationIds = null);

        /// <summary>
        /// Export financial report to specified format
        /// </summary>
        /// <param name="report">Financial report to export</param>
        /// <param name="format">Export format</param>
        /// <returns>Exported file content as byte array</returns>
        Task<byte[]> ExportFinancialReportAsync(FinancialReport report, FinancialReportFormat format);

        /// <summary>
        /// Get financial summary for dashboard
        /// </summary>
        /// <param name="date">Date for summary</param>
        /// <returns>Financial summary data</returns>
        Task<FinancialSummaryDashboard> GetFinancialSummaryForDashboardAsync(DateTime date);

        /// <summary>
        /// Get expense analysis
        /// </summary>
        /// <param name="fromDate">Start date</param>
        /// <param name="toDate">End date</param>
        /// <param name="categoryFilter">Expense category filter (optional)</param>
        /// <returns>Expense analysis data</returns>
        Task<List<ExpenseSummary>> GetExpenseAnalysisAsync(DateTime fromDate, DateTime toDate, string? categoryFilter = null);

        /// <summary>
        /// Get credit and debt analysis
        /// </summary>
        /// <param name="asOfDate">Date for analysis</param>
        /// <returns>Credit and debt analysis data</returns>
        Task<CreditDebtAnalysis> GetCreditDebtAnalysisAsync(DateTime? asOfDate = null);

        /// <summary>
        /// Get tax summary (if applicable)
        /// </summary>
        /// <param name="fromDate">Start date</param>
        /// <param name="toDate">End date</param>
        /// <returns>Tax summary data</returns>
        Task<List<TaxSummary>> GetTaxSummaryAsync(DateTime fromDate, DateTime toDate);
    }

    /// <summary>
    /// Profit and Loss Statement
    /// </summary>
    public class ProfitAndLossStatement
    {
        public DateTime FromDate { get; set; }
        public DateTime ToDate { get; set; }
        public string Period { get; set; } = string.Empty;

        // Revenue
        public decimal GrossRevenue { get; set; }
        public decimal Returns { get; set; }
        public decimal Discounts { get; set; }
        public decimal NetRevenue { get; set; }

        // Cost of Goods Sold
        public decimal BeginningInventory { get; set; }
        public decimal Purchases { get; set; }
        public decimal EndingInventory { get; set; }
        public decimal CostOfGoodsSold { get; set; }

        // Gross Profit
        public decimal GrossProfit { get; set; }
        public decimal GrossProfitMargin { get; set; }

        // Operating Expenses
        public List<ExpenseCategory> OperatingExpenses { get; set; } = new List<ExpenseCategory>();
        public decimal TotalOperatingExpenses { get; set; }

        // Net Income
        public decimal OperatingIncome { get; set; }
        public decimal OtherIncome { get; set; }
        public decimal OtherExpenses { get; set; }
        public decimal NetIncome { get; set; }
        public decimal NetProfitMargin { get; set; }
    }

    /// <summary>
    /// Expense category for P&L
    /// </summary>
    public class ExpenseCategory
    {
        public string CategoryName { get; set; } = string.Empty;
        public decimal Amount { get; set; }
        public decimal PercentageOfRevenue { get; set; }
    }

    /// <summary>
    /// Cash Flow Statement
    /// </summary>
    public class CashFlowStatement
    {
        public DateTime FromDate { get; set; }
        public DateTime ToDate { get; set; }
        public string Period { get; set; } = string.Empty;

        // Operating Activities
        public decimal NetIncome { get; set; }
        public decimal DepreciationAmortization { get; set; }
        public decimal InventoryChange { get; set; }
        public decimal AccountsReceivableChange { get; set; }
        public decimal AccountsPayableChange { get; set; }
        public decimal CashFromOperations { get; set; }

        // Investing Activities
        public decimal CapitalExpenditures { get; set; }
        public decimal AssetPurchases { get; set; }
        public decimal AssetSales { get; set; }
        public decimal CashFromInvesting { get; set; }

        // Financing Activities
        public decimal LoanProceeds { get; set; }
        public decimal LoanRepayments { get; set; }
        public decimal OwnerContributions { get; set; }
        public decimal OwnerWithdrawals { get; set; }
        public decimal CashFromFinancing { get; set; }

        // Net Cash Flow
        public decimal NetCashFlow { get; set; }
        public decimal BeginningCash { get; set; }
        public decimal EndingCash { get; set; }
    }

    /// <summary>
    /// Financial summary for dashboard display
    /// </summary>
    public class FinancialSummaryDashboard
    {
        public DateTime Date { get; set; }
        public decimal TodaysRevenue { get; set; }
        public decimal YesterdaysRevenue { get; set; }
        public decimal RevenueGrowth { get; set; }
        public decimal TodaysProfit { get; set; }
        public decimal ProfitMargin { get; set; }
        public decimal CashOnHand { get; set; }
        public decimal OutstandingCredit { get; set; }
        public decimal MonthToDateRevenue { get; set; }
        public decimal MonthToDateProfit { get; set; }
        public List<PaymentMethodSummary> PaymentBreakdown { get; set; } = new List<PaymentMethodSummary>();
        public List<FinancialKPI> KeyMetrics { get; set; } = new List<FinancialKPI>();
    }

    /// <summary>
    /// Credit and debt analysis
    /// </summary>
    public class CreditDebtAnalysis
    {
        public DateTime AnalysisDate { get; set; }
        public decimal TotalCreditSales { get; set; }
        public decimal TotalCreditPayments { get; set; }
        public decimal OutstandingCredit { get; set; }
        public decimal OverdueCredit { get; set; }
        public decimal CreditUtilizationRate { get; set; }
        public int CustomersWithCredit { get; set; }
        public int CustomersWithOverdueCredit { get; set; }
        public List<CreditAging> CreditAgingBreakdown { get; set; } = new List<CreditAging>();
        public List<TopCreditCustomer> TopCreditCustomers { get; set; } = new List<TopCreditCustomer>();
    }

    /// <summary>
    /// Credit aging breakdown
    /// </summary>
    public class CreditAging
    {
        public string AgeRange { get; set; } = string.Empty; // 0-30 days, 31-60 days, etc.
        public decimal Amount { get; set; }
        public int CustomerCount { get; set; }
        public decimal PercentageOfTotal { get; set; }
    }

    /// <summary>
    /// Top credit customer
    /// </summary>
    public class TopCreditCustomer
    {
        public int CustomerId { get; set; }
        public string CustomerName { get; set; } = string.Empty;
        public decimal CreditBalance { get; set; }
        public decimal CreditLimit { get; set; }
        public decimal CreditUtilization { get; set; }
        public int DaysOverdue { get; set; }
        public DateTime LastPaymentDate { get; set; }
    }
}
