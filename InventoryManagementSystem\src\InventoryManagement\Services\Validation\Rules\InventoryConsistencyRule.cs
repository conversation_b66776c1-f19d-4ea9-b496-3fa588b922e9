using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using InventoryManagement.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace InventoryManagement.Services.Validation.Rules
{
    /// <summary>
    /// Rule to check consistency between inventory records and transaction history
    /// </summary>
    public class InventoryConsistencyRule : IDataIntegrityRule
    {
        private readonly AppDbContext _dbContext;
        private readonly ILogger<InventoryConsistencyRule> _logger;
        
        public string Name => "Inventory Consistency Check";
        
        public string Description => "Verifies that inventory levels match transaction history totals";
        
        public string EntityType => "Inventory";
        
        public InventoryConsistencyRule(
            AppDbContext dbContext,
            ILogger<InventoryConsistencyRule> logger)
        {
            _dbContext = dbContext ?? throw new ArgumentNullException(nameof(dbContext));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }
        
        /// <summary>
        /// Validates that inventory levels are consistent with transaction history
        /// </summary>
        public async Task<List<DataIntegrityIssue>> ValidateAsync()
        {
            _logger.LogInformation("Running {RuleName}", Name);
            var issues = new List<DataIntegrityIssue>();
            
            try
            {
                // Query to check if inventory quantity matches transaction history
                // In a real system, we'd have a more complex query that:
                // 1. Gets current inventory levels for each item
                // 2. Calculates expected inventory based on transactions
                // 3. Compares the two values
                
                // This is a simplified example
                var discrepancies = await _dbContext.Items
                    .Select(item => new 
                    {
                        ItemId = item.Id,
                        ItemCode = item.ItemCode,
                        ItemName = item.Name,
                        CurrentQuantity = item.QuantityOnHand,
                        // Calculate expected quantity from transactions
                        ExpectedQuantity = item.InitialQuantity + 
                            _dbContext.Transactions
                                .Where(t => t.ItemId == item.Id)
                                .Sum(t => t.IsIncoming ? t.Quantity : -t.Quantity)
                    })
                    .Where(result => result.CurrentQuantity != result.ExpectedQuantity)
                    .ToListAsync();
                
                foreach (var discrepancy in discrepancies)
                {
                    issues.Add(new DataIntegrityIssue
                    {
                        EntityType = EntityType,
                        EntityId = discrepancy.ItemId.ToString(),
                        PropertyName = "QuantityOnHand",
                        RuleName = Name,
                        Severity = IssueSeverity.Error,
                        Message = $"Inventory quantity mismatch for item {discrepancy.ItemCode} ({discrepancy.ItemName})",
                        Details = $"Current quantity: {discrepancy.CurrentQuantity}, " +
                                  $"Expected from transactions: {discrepancy.ExpectedQuantity}, " +
                                  $"Difference: {discrepancy.CurrentQuantity - discrepancy.ExpectedQuantity}",
                        ResolutionSuggestion = "Run inventory reconciliation for this item and adjust quantity as needed.",
                        CanAutoFix = false
                    });
                }
                
                _logger.LogInformation("Found {Count} inventory discrepancies", issues.Count);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error running inventory consistency rule");
                issues.Add(new DataIntegrityIssue
                {
                    EntityType = EntityType,
                    PropertyName = "System",
                    RuleName = Name,
                    Severity = IssueSeverity.Error,
                    Message = "Failed to check inventory consistency",
                    Details = ex.ToString(),
                    CanAutoFix = false
                });
            }
            
            return issues;
        }
    }
}
