using System.Windows.Controls;
using InventoryManagement.ViewModels.Reports;

namespace InventoryManagement.Views.Reports
{
    /// <summary>
    /// Interaction logic for ReportsDashboardView.xaml
    /// </summary>
    public partial class ReportsDashboardView : UserControl
    {
        public ReportsDashboardView()
        {
            InitializeComponent();
        }

        public ReportsDashboardView(ReportsDashboardViewModel viewModel) : this()
        {
            DataContext = viewModel;
        }
    }
}
