using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Windows.Input;
using InventoryManagement.Models.Hardware;
using InventoryManagement.Services.Hardware;
using InventoryManagement.ViewModels.Base;
using Microsoft.Extensions.Logging;

namespace InventoryManagement.ViewModels.Settings
{
    /// <summary>
    /// ViewModel for hardware configuration settings
    /// </summary>
    public class HardwareSettingsViewModel : BaseViewModel
    {
        private readonly IBarcodeHardwareService _barcodeService;
        private readonly IPrinterHardwareService _printerService;
        private readonly ILogger<HardwareSettingsViewModel> _logger;

        // Scanner properties
        private ObservableCollection<ScannerConfiguration> _scannerConfigurations;
        private ScannerConfiguration? _selectedScannerConfiguration;
        private ObservableCollection<ScannerDevice> _availableScanners;
        private ScannerDevice? _selectedAvailableScanner;
        private bool _isScannerConnected;
        private string _scannerStatus = "Disconnected";

        // Printer properties
        private ObservableCollection<PrinterConfiguration> _printerConfigurations;
        private PrinterConfiguration? _selectedPrinterConfiguration;
        private ObservableCollection<PrinterDevice> _availablePrinters;
        private PrinterDevice? _selectedAvailablePrinter;
        private bool _isPrinterConnected;
        private string _printerStatus = "Disconnected";

        // UI state
        private bool _isScanning = false;
        private bool _isTesting = false;
        private string _testResults = string.Empty;

        public HardwareSettingsViewModel(
            IBarcodeHardwareService barcodeService,
            IPrinterHardwareService printerService,
            ILogger<HardwareSettingsViewModel> logger)
        {
            _barcodeService = barcodeService;
            _printerService = printerService;
            _logger = logger;

            // Initialize collections
            _scannerConfigurations = new ObservableCollection<ScannerConfiguration>();
            _availableScanners = new ObservableCollection<ScannerDevice>();
            _printerConfigurations = new ObservableCollection<PrinterConfiguration>();
            _availablePrinters = new ObservableCollection<PrinterDevice>();

            // Initialize commands
            InitializeCommands();

            // Subscribe to hardware events
            SubscribeToHardwareEvents();

            // Load initial data
            _ = LoadHardwareConfigurationsAsync();
        }

        #region Properties

        public ObservableCollection<ScannerConfiguration> ScannerConfigurations
        {
            get => _scannerConfigurations;
            set => SetProperty(ref _scannerConfigurations, value);
        }

        public ScannerConfiguration? SelectedScannerConfiguration
        {
            get => _selectedScannerConfiguration;
            set
            {
                if (SetProperty(ref _selectedScannerConfiguration, value))
                {
                    OnPropertyChanged(nameof(IsScannerSelected));
                    ((RelayCommand)ConnectScannerCommand).RaiseCanExecuteChanged();
                    ((RelayCommand)TestScannerCommand).RaiseCanExecuteChanged();
                }
            }
        }

        public ObservableCollection<ScannerDevice> AvailableScanners
        {
            get => _availableScanners;
            set => SetProperty(ref _availableScanners, value);
        }

        public ScannerDevice? SelectedAvailableScanner
        {
            get => _selectedAvailableScanner;
            set => SetProperty(ref _selectedAvailableScanner, value);
        }

        public bool IsScannerConnected
        {
            get => _isScannerConnected;
            set
            {
                if (SetProperty(ref _isScannerConnected, value))
                {
                    ((RelayCommand)ConnectScannerCommand).RaiseCanExecuteChanged();
                    ((RelayCommand)DisconnectScannerCommand).RaiseCanExecuteChanged();
                    ((RelayCommand)TestScannerCommand).RaiseCanExecuteChanged();
                }
            }
        }

        public string ScannerStatus
        {
            get => _scannerStatus;
            set => SetProperty(ref _scannerStatus, value);
        }

        public ObservableCollection<PrinterConfiguration> PrinterConfigurations
        {
            get => _printerConfigurations;
            set => SetProperty(ref _printerConfigurations, value);
        }

        public PrinterConfiguration? SelectedPrinterConfiguration
        {
            get => _selectedPrinterConfiguration;
            set
            {
                if (SetProperty(ref _selectedPrinterConfiguration, value))
                {
                    OnPropertyChanged(nameof(IsPrinterSelected));
                    ((RelayCommand)ConnectPrinterCommand).RaiseCanExecuteChanged();
                    ((RelayCommand)TestPrinterCommand).RaiseCanExecuteChanged();
                }
            }
        }

        public ObservableCollection<PrinterDevice> AvailablePrinters
        {
            get => _availablePrinters;
            set => SetProperty(ref _availablePrinters, value);
        }

        public PrinterDevice? SelectedAvailablePrinter
        {
            get => _selectedAvailablePrinter;
            set => SetProperty(ref _selectedAvailablePrinter, value);
        }

        public bool IsPrinterConnected
        {
            get => _isPrinterConnected;
            set
            {
                if (SetProperty(ref _isPrinterConnected, value))
                {
                    ((RelayCommand)ConnectPrinterCommand).RaiseCanExecuteChanged();
                    ((RelayCommand)DisconnectPrinterCommand).RaiseCanExecuteChanged();
                    ((RelayCommand)TestPrinterCommand).RaiseCanExecuteChanged();
                }
            }
        }

        public string PrinterStatus
        {
            get => _printerStatus;
            set => SetProperty(ref _printerStatus, value);
        }

        public bool IsScanning
        {
            get => _isScanning;
            set => SetProperty(ref _isScanning, value);
        }

        public bool IsTesting
        {
            get => _isTesting;
            set => SetProperty(ref _isTesting, value);
        }

        public string TestResults
        {
            get => _testResults;
            set => SetProperty(ref _testResults, value);
        }

        public bool IsScannerSelected => SelectedScannerConfiguration != null;
        public bool IsPrinterSelected => SelectedPrinterConfiguration != null;

        #endregion

        #region Commands

        public ICommand RefreshHardwareCommand { get; private set; } = null!;
        public ICommand ConnectScannerCommand { get; private set; } = null!;
        public ICommand DisconnectScannerCommand { get; private set; } = null!;
        public ICommand TestScannerCommand { get; private set; } = null!;
        public ICommand ConnectPrinterCommand { get; private set; } = null!;
        public ICommand DisconnectPrinterCommand { get; private set; } = null!;
        public ICommand TestPrinterCommand { get; private set; } = null!;
        public ICommand SaveConfigurationCommand { get; private set; } = null!;
        public ICommand AddScannerConfigCommand { get; private set; } = null!;
        public ICommand AddPrinterConfigCommand { get; private set; } = null!;
        public ICommand DeleteScannerConfigCommand { get; private set; } = null!;
        public ICommand DeletePrinterConfigCommand { get; private set; } = null!;

        private void InitializeCommands()
        {
            RefreshHardwareCommand = new RelayCommand(async () => await RefreshHardwareAsync());
            ConnectScannerCommand = new RelayCommand(async () => await ConnectScannerAsync(), () => IsScannerSelected && !IsScannerConnected);
            DisconnectScannerCommand = new RelayCommand(async () => await DisconnectScannerAsync(), () => IsScannerConnected);
            TestScannerCommand = new RelayCommand(async () => await TestScannerAsync(), () => IsScannerConnected && !IsTesting);
            ConnectPrinterCommand = new RelayCommand(async () => await ConnectPrinterAsync(), () => IsPrinterSelected && !IsPrinterConnected);
            DisconnectPrinterCommand = new RelayCommand(async () => await DisconnectPrinterAsync(), () => IsPrinterConnected);
            TestPrinterCommand = new RelayCommand(async () => await TestPrinterAsync(), () => IsPrinterConnected && !IsTesting);
            SaveConfigurationCommand = new RelayCommand(async () => await SaveConfigurationAsync());
            AddScannerConfigCommand = new RelayCommand(() => AddScannerConfiguration());
            AddPrinterConfigCommand = new RelayCommand(() => AddPrinterConfiguration());
            DeleteScannerConfigCommand = new RelayCommand(() => DeleteScannerConfiguration(), () => IsScannerSelected);
            DeletePrinterConfigCommand = new RelayCommand(() => DeletePrinterConfiguration(), () => IsPrinterSelected);
        }

        #endregion

        #region Methods

        private async Task LoadHardwareConfigurationsAsync()
        {
            try
            {
                IsLoading = true;

                // Load scanner configurations from database
                // This would typically come from a configuration service
                var defaultScannerConfig = new ScannerConfiguration
                {
                    Id = 1,
                    Name = "Default USB Scanner",
                    ConnectionType = ScannerConnectionTypes.USB,
                    EnableBeep = true,
                    AutoEnterAfterScan = true,
                    IsDefault = true
                };

                ScannerConfigurations.Clear();
                ScannerConfigurations.Add(defaultScannerConfig);

                // Load printer configurations from database
                var defaultPrinterConfig = new PrinterConfiguration
                {
                    Id = 1,
                    Name = "Default Receipt Printer",
                    PrinterType = PrinterTypes.Thermal,
                    ConnectionType = PrinterConnectionTypes.USB,
                    PaperWidth = PaperWidths.Width80mm,
                    AutoCut = true,
                    IsDefault = true
                };

                PrinterConfigurations.Clear();
                PrinterConfigurations.Add(defaultPrinterConfig);

                // Discover available hardware
                await RefreshHardwareAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading hardware configurations");
                ShowErrorMessage("Failed to load hardware configurations: " + ex.Message);
            }
            finally
            {
                IsLoading = false;
            }
        }

        private async Task RefreshHardwareAsync()
        {
            try
            {
                IsLoading = true;

                // Discover available scanners
                var scanners = await _barcodeService.DiscoverScannersAsync();
                AvailableScanners.Clear();
                foreach (var scanner in scanners)
                {
                    AvailableScanners.Add(scanner);
                }

                // Discover available printers
                var printers = await _printerService.DiscoverPrintersAsync();
                AvailablePrinters.Clear();
                foreach (var printer in printers)
                {
                    AvailablePrinters.Add(printer);
                }

                ShowSuccessMessage($"Found {scanners.Count} scanner(s) and {printers.Count} printer(s)");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error refreshing hardware");
                ShowErrorMessage("Failed to refresh hardware: " + ex.Message);
            }
            finally
            {
                IsLoading = false;
            }
        }

        private async Task ConnectScannerAsync()
        {
            if (SelectedScannerConfiguration == null) return;

            try
            {
                IsLoading = true;
                ScannerStatus = "Connecting...";

                var success = await _barcodeService.InitializeAsync(SelectedScannerConfiguration);
                if (success)
                {
                    IsScannerConnected = true;
                    ScannerStatus = "Connected";
                    ShowSuccessMessage("Scanner connected successfully");
                }
                else
                {
                    ScannerStatus = "Connection Failed";
                    ShowErrorMessage("Failed to connect to scanner");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error connecting scanner");
                ScannerStatus = "Error";
                ShowErrorMessage("Error connecting scanner: " + ex.Message);
            }
            finally
            {
                IsLoading = false;
            }
        }

        private async Task DisconnectScannerAsync()
        {
            try
            {
                await _barcodeService.DisconnectAsync();
                IsScannerConnected = false;
                ScannerStatus = "Disconnected";
                ShowSuccessMessage("Scanner disconnected");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error disconnecting scanner");
                ShowErrorMessage("Error disconnecting scanner: " + ex.Message);
            }
        }

        private async Task TestScannerAsync()
        {
            try
            {
                IsTesting = true;
                TestResults = "Testing scanner...";

                var testResult = await _barcodeService.TestScannerAsync();
                if (testResult.IsSuccessful)
                {
                    TestResults = $"Scanner test successful!\nResponse time: {testResult.ResponseTime.TotalMilliseconds:F0}ms\nSupported types: {string.Join(", ", testResult.SupportedBarcodeTypes)}";
                    ShowSuccessMessage("Scanner test completed successfully");
                }
                else
                {
                    TestResults = $"Scanner test failed: {testResult.Message}";
                    ShowErrorMessage("Scanner test failed");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error testing scanner");
                TestResults = $"Scanner test error: {ex.Message}";
                ShowErrorMessage("Error testing scanner: " + ex.Message);
            }
            finally
            {
                IsTesting = false;
            }
        }

        private async Task ConnectPrinterAsync()
        {
            if (SelectedPrinterConfiguration == null) return;

            try
            {
                IsLoading = true;
                PrinterStatus = "Connecting...";

                var success = await _printerService.InitializeAsync(SelectedPrinterConfiguration);
                if (success)
                {
                    IsPrinterConnected = true;
                    PrinterStatus = "Connected";
                    ShowSuccessMessage("Printer connected successfully");
                }
                else
                {
                    PrinterStatus = "Connection Failed";
                    ShowErrorMessage("Failed to connect to printer");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error connecting printer");
                PrinterStatus = "Error";
                ShowErrorMessage("Error connecting printer: " + ex.Message);
            }
            finally
            {
                IsLoading = false;
            }
        }

        private async Task DisconnectPrinterAsync()
        {
            try
            {
                await _printerService.DisconnectAsync();
                IsPrinterConnected = false;
                PrinterStatus = "Disconnected";
                ShowSuccessMessage("Printer disconnected");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error disconnecting printer");
                ShowErrorMessage("Error disconnecting printer: " + ex.Message);
            }
        }

        private async Task TestPrinterAsync()
        {
            try
            {
                IsTesting = true;
                TestResults = "Testing printer...";

                var testResult = await _printerService.TestPrinterAsync();
                if (testResult.IsSuccessful)
                {
                    // Print test page
                    var printResult = await _printerService.PrintTestPageAsync();
                    if (printResult.IsSuccessful)
                    {
                        TestResults = $"Printer test successful!\nTest page printed\nCan print: {testResult.CanPrint}\nHas paper: {testResult.HasPaper}\nCash drawer: {testResult.CashDrawerConnected}";
                        ShowSuccessMessage("Printer test completed successfully");
                    }
                    else
                    {
                        TestResults = $"Printer connection OK but print failed: {printResult.Message}";
                        ShowWarningMessage("Printer connected but print test failed");
                    }
                }
                else
                {
                    TestResults = $"Printer test failed: {testResult.Message}";
                    ShowErrorMessage("Printer test failed");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error testing printer");
                TestResults = $"Printer test error: {ex.Message}";
                ShowErrorMessage("Error testing printer: " + ex.Message);
            }
            finally
            {
                IsTesting = false;
            }
        }

        private async Task SaveConfigurationAsync()
        {
            try
            {
                IsLoading = true;

                // Save configurations to database
                // This would typically use a configuration service
                ShowSuccessMessage("Hardware configurations saved successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error saving hardware configurations");
                ShowErrorMessage("Failed to save configurations: " + ex.Message);
            }
            finally
            {
                IsLoading = false;
            }
        }

        private void AddScannerConfiguration()
        {
            var newConfig = new ScannerConfiguration
            {
                Name = "New Scanner Configuration",
                ConnectionType = ScannerConnectionTypes.USB,
                EnableBeep = true,
                AutoEnterAfterScan = true
            };

            ScannerConfigurations.Add(newConfig);
            SelectedScannerConfiguration = newConfig;
        }

        private void AddPrinterConfiguration()
        {
            var newConfig = new PrinterConfiguration
            {
                Name = "New Printer Configuration",
                PrinterType = PrinterTypes.Thermal,
                ConnectionType = PrinterConnectionTypes.USB,
                PaperWidth = PaperWidths.Width80mm,
                AutoCut = true
            };

            PrinterConfigurations.Add(newConfig);
            SelectedPrinterConfiguration = newConfig;
        }

        private void DeleteScannerConfiguration()
        {
            if (SelectedScannerConfiguration != null)
            {
                ScannerConfigurations.Remove(SelectedScannerConfiguration);
                SelectedScannerConfiguration = ScannerConfigurations.FirstOrDefault();
            }
        }

        private void DeletePrinterConfiguration()
        {
            if (SelectedPrinterConfiguration != null)
            {
                PrinterConfigurations.Remove(SelectedPrinterConfiguration);
                SelectedPrinterConfiguration = PrinterConfigurations.FirstOrDefault();
            }
        }

        private void SubscribeToHardwareEvents()
        {
            _barcodeService.BarcodeScanned += OnBarcodeScanned;
            _barcodeService.ScannerStatusChanged += OnScannerStatusChanged;
            _printerService.PrinterStatusChanged += OnPrinterStatusChanged;
        }

        private void OnBarcodeScanned(object? sender, BarcodeScannedEventArgs e)
        {
            // Handle barcode scanned event
            if (e.IsValid)
            {
                TestResults = $"Barcode scanned: {e.Barcode} (Type: {e.BarcodeType})";
            }
            else
            {
                TestResults = $"Invalid barcode: {e.Barcode} - {e.ErrorMessage}";
            }
        }

        private void OnScannerStatusChanged(object? sender, ScannerStatusChangedEventArgs e)
        {
            IsScannerConnected = e.IsConnected;
            ScannerStatus = e.StatusMessage;
        }

        private void OnPrinterStatusChanged(object? sender, PrinterStatusChangedEventArgs e)
        {
            IsPrinterConnected = e.IsConnected;
            PrinterStatus = e.StatusMessage;
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing && !_disposed)
            {
                // Unsubscribe from events
                _barcodeService.BarcodeScanned -= OnBarcodeScanned;
                _barcodeService.ScannerStatusChanged -= OnScannerStatusChanged;
                _printerService.PrinterStatusChanged -= OnPrinterStatusChanged;

                _disposed = true;
            }
            base.Dispose(disposing);
        }

        private bool _disposed = false;

        #endregion
    }
}
