using System;
using System.ComponentModel.DataAnnotations;

namespace InventoryManagement.Models
{
    /// <summary>
    /// Represents an operation queued for processing when offline
    /// </summary>
    public class OfflineQueueItem
    {
        /// <summary>
        /// Gets or sets the unique identifier
        /// </summary>
        [Key]
        public Guid Id { get; set; } = Guid.NewGuid();
        
        /// <summary>
        /// Gets or sets the timestamp when the operation was created
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.Now;
        
        /// <summary>
        /// Gets or sets the type of the entity affected by this operation
        /// </summary>
        [Required, MaxLength(100)]
        public string EntityType { get; set; }
        
        /// <summary>
        /// Gets or sets the ID of the entity affected by this operation
        /// </summary>
        [Required]
        public string EntityId { get; set; }
        
        /// <summary>
        /// Gets or sets the type of operation to perform
        /// </summary>
        [Required]
        public OfflineOperationType OperationType { get; set; }
        
        /// <summary>
        /// Gets or sets the serialized entity data for this operation
        /// </summary>
        public string EntityData { get; set; }
        
        /// <summary>
        /// Gets or sets the user ID who created this operation
        /// </summary>
        public int? UserId { get; set; }
        
        /// <summary>
        /// Gets or sets a value indicating whether this operation has been processed
        /// </summary>
        public bool IsProcessed { get; set; }
        
        /// <summary>
        /// Gets or sets the timestamp when the operation was processed
        /// </summary>
        public DateTime? ProcessedAt { get; set; }
        
        /// <summary>
        /// Gets or sets the result of processing this operation
        /// </summary>
        public string ProcessingResult { get; set; }
        
        /// <summary>
        /// Gets or sets a value indicating whether this operation has encountered errors during processing
        /// </summary>
        public bool HasErrors { get; set; }
        
        /// <summary>
        /// Gets or sets error details if processing failed
        /// </summary>
        public string ErrorDetails { get; set; }
        
        /// <summary>
        /// Gets or sets the number of retry attempts for this operation
        /// </summary>
        public int RetryCount { get; set; }
        
        /// <summary>
        /// Gets or sets the priority level for this operation
        /// </summary>
        public int Priority { get; set; } = 1;
    }

    /// <summary>
    /// Types of operations that can be queued while offline
    /// </summary>
    public enum OfflineOperationType
    {
        /// <summary>
        /// Create a new entity
        /// </summary>
        Create = 0,
        
        /// <summary>
        /// Update an existing entity
        /// </summary>
        Update = 1,
        
        /// <summary>
        /// Delete an existing entity
        /// </summary>
        Delete = 2,
        
        /// <summary>
        /// Custom operation type
        /// </summary>
        Custom = 3
    }
}
