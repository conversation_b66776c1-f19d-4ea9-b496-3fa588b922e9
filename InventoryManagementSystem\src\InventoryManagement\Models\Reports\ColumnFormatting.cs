using System;

namespace InventoryManagement.Models.Reports
{
    /// <summary>
    /// Defines formatting options for a report column
    /// </summary>
    public class ColumnFormatting
    {
        /// <summary>
        /// Column name in the result set
        /// </summary>
        public string ColumnName { get; set; }
        
        /// <summary>
        /// Display name to show in reports
        /// </summary>
        public string DisplayName { get; set; }
        
        /// <summary>
        /// Format string for the column value
        /// </summary>
        public string FormatString { get; set; }
        
        /// <summary>
        /// Width in pixels or percentage for display
        /// </summary>
        public string Width { get; set; }
        
        /// <summary>
        /// Alignment for the column
        /// </summary>
        public ColumnAlignment Alignment { get; set; } = ColumnAlignment.Left;
        
        /// <summary>
        /// Whether to highlight negative values
        /// </summary>
        public bool HighlightNegative { get; set; }
        
        /// <summary>
        /// Whether to format as currency
        /// </summary>
        public bool IsCurrency { get; set; }
        
        /// <summary>
        /// Whether to sum this column in totals
        /// </summary>
        public bool IncludeInTotals { get; set; }
        
        /// <summary>
        /// Whether to highlight the column
        /// </summary>
        public bool Highlight { get; set; }
        
        /// <summary>
        /// Color for highlighting the column (HTML color)
        /// </summary>
        public string HighlightColor { get; set; }
    }
    
    /// <summary>
    /// Alignment options for report columns
    /// </summary>
    public enum ColumnAlignment
    {
        Left,
        Center,
        Right
    }
}
