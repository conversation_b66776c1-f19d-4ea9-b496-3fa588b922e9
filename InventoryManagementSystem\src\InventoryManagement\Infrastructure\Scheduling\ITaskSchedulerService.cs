using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace InventoryManagement.Infrastructure.Scheduling
{
    /// <summary>
    /// Interface for services managing scheduled tasks
    /// </summary>
    public interface ITaskSchedulerService
    {
        /// <summary>
        /// Gets all scheduled tasks
        /// </summary>
        IReadOnlyList<ScheduledTask> GetAllTasks();
        
        /// <summary>
        /// Gets a specific task by ID
        /// </summary>
        ScheduledTask GetTaskById(Guid taskId);
        
        /// <summary>
        /// Adds or updates a scheduled task
        /// </summary>
        Task<ScheduledTask> SaveTaskAsync(ScheduledTask task);
        
        /// <summary>
        /// Deletes a scheduled task
        /// </summary>
        Task<bool> DeleteTaskAsync(Guid taskId);
        
        /// <summary>
        /// Enables or disables a scheduled task
        /// </summary>
        Task<bool> SetTaskEnabledStateAsync(Guid taskId, bool enabled);
        
        /// <summary>
        /// Executes a task immediately, regardless of schedule
        /// </summary>
        Task<bool> ExecuteTaskNowAsync(Guid taskId);
    }
}
