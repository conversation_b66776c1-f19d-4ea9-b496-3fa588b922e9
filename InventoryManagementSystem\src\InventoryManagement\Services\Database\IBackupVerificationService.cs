using System;
using System.Threading.Tasks;

namespace InventoryManagement.Services.Database
{
    /// <summary>
    /// Interface for verifying database backup integrity
    /// </summary>
    public interface IBackupVerificationService
    {
        /// <summary>
        /// Verifies that a backup file exists and is not corrupted (basic check)
        /// </summary>
        /// <param name="backupFilePath">Path to the backup file</param>
        /// <returns>Verification result</returns>
        Task<BackupVerificationResult> VerifyBackupExistsAsync(string backupFilePath);
        
        /// <summary>
        /// Performs a comprehensive verification of backup integrity
        /// including structure validation and sample data checks
        /// </summary>
        /// <param name="backupFilePath">Path to the backup file</param>
        /// <returns>Verification result with detailed diagnostics</returns>
        Task<BackupVerificationResult> VerifyBackupComprehensiveAsync(string backupFilePath);
    }
    
    /// <summary>
    /// Result of a backup verification operation
    /// </summary>
    public class BackupVerificationResult
    {
        /// <summary>
        /// Whether the backup is valid
        /// </summary>
        public bool IsValid { get; set; }
        
        /// <summary>
        /// Error message if verification failed
        /// </summary>
        public string ErrorMessage { get; set; }
        
        /// <summary>
        /// Detailed diagnostic information about the backup
        /// </summary>
        public string DiagnosticInfo { get; set; }
        
        /// <summary>
        /// When the verification was performed
        /// </summary>
        public DateTime VerificationTime { get; set; } = DateTime.Now;
    }
}
