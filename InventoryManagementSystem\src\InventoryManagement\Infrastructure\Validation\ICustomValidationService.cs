using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace InventoryManagement.Infrastructure.Validation
{
    /// <summary>
    /// Interface for managing custom user-defined validation rules
    /// </summary>
    public interface ICustomValidationService
    {
        /// <summary>
        /// Gets all active validation rules
        /// </summary>
        IReadOnlyList<CustomValidationRule> GetActiveRules();
        
        /// <summary>
        /// Gets all rules for a specific entity type
        /// </summary>
        IReadOnlyList<CustomValidationRule> GetRulesForEntityType(string entityType);
        
        /// <summary>
        /// Gets all rules for a specific entity property
        /// </summary>
        IReadOnlyList<CustomValidationRule> GetRulesForProperty(string entityType, string propertyName);
        
        /// <summary>
        /// Adds a new validation rule
        /// </summary>
        Task<CustomValidationRule> AddRuleAsync(CustomValidationRule rule);
        
        /// <summary>
        /// Updates an existing validation rule
        /// </summary>
        Task<bool> UpdateRuleAsync(CustomValidationRule rule);
        
        /// <summary>
        /// Deactivates a validation rule
        /// </summary>
        Task<bool> DeactivateRuleAsync(Guid ruleId, string modifiedBy);
    }
}
