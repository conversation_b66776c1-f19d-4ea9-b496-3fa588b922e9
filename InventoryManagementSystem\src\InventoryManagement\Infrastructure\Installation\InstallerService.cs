using System;
using System.Diagnostics;
using System.IO;
using System.Threading.Tasks;
using System.Windows;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.IO.Compression;

namespace InventoryManagement.Infrastructure.Installation
{
    /// <summary>
    /// Service for handling application installation, updates and migration
    /// </summary>
    public class InstallerService : IInstallerService
    {
        private readonly ILogger<InstallerService> _logger;
        private readonly InstallerConfiguration _config;
        private readonly string _installationDirectory;
        
        public InstallerService(
            IOptions<InstallerConfiguration> config,
            ILogger<InstallerService> logger)
        {
            _config = config?.Value ?? throw new ArgumentNullException(nameof(config));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _installationDirectory = _config.CustomInstallDirectory ?? 
                Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ProgramFiles), "TomGeneralTrading");
        }
        
        /// <summary>
        /// Checks if the application needs to be updated
        /// </summary>
        public async Task<bool> CheckForUpdatesAsync()
        {
            try
            {
                var currentVersion = _config.ApplicationVersion;
                var versionFilePath = Path.Combine(_installationDirectory, "version.txt");
                
                if (!File.Exists(versionFilePath))
                {
                    return false;
                }
                
                var installedVersionString = await File.ReadAllTextAsync(versionFilePath);
                if (Version.TryParse(installedVersionString, out var installedVersion))
                {
                    return installedVersion < currentVersion;
                }
                
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking for updates");
                return false;
            }
        }
        
        /// <summary>
        /// Creates a backup before updating
        /// </summary>
        public async Task<string> CreateUpdateBackupAsync()
        {
            try
            {
                var backupDirectory = Path.Combine(
                    Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData),
                    "TomGeneralTrading", "UpdateBackups");
                
                Directory.CreateDirectory(backupDirectory);
                
                var backupFilePath = Path.Combine(
                    backupDirectory, 
                    $"Update_Backup_{DateTime.Now:yyyyMMdd_HHmmss}.zip");
                
                // Create backup of database and application settings
                ZipFile.CreateFromDirectory(
                    Path.Combine(_installationDirectory, "App_Data"),
                    backupFilePath);
                
                _logger.LogInformation("Created update backup at {BackupPath}", backupFilePath);
                return backupFilePath;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to create update backup");
                return null;
            }
        }
        
        /// <summary>
        /// Creates shortcuts for the application
        /// </summary>
        public void CreateShortcuts()
        {
            try
            {
                var executablePath = Path.Combine(_installationDirectory, "InventoryManagement.exe");
                
                if (_config.CreateDesktopShortcuts)
                {
                    CreateShortcut(
                        Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.Desktop), 
                        "Tom General Trading.lnk"),
                        executablePath,
                        "Tom General Trading Inventory Management System");
                }
                
                if (_config.CreateStartMenuShortcuts)
                {
                    var startMenuPath = Path.Combine(
                        Environment.GetFolderPath(Environment.SpecialFolder.StartMenu), 
                        "Programs", 
                        "Tom General Trading");
                    
                    Directory.CreateDirectory(startMenuPath);
                    
                    CreateShortcut(
                        Path.Combine(startMenuPath, "Inventory Management.lnk"),
                        executablePath,
                        "Tom General Trading Inventory Management System");
                }
                
                _logger.LogInformation("Created shortcuts for the application");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to create shortcuts");
            }
        }
        
        /// <summary>
        /// Helper method to create a Windows shortcut
        /// </summary>
        private void CreateShortcut(string shortcutPath, string targetPath, string description)
        {
            // Note: This is a simplified implementation. In a real scenario,
            // you would use COM interop with IWshRuntimeLibrary to create shortcuts.
            // For this example, we're using a placeholder implementation.
            _logger.LogInformation("Created shortcut: {ShortcutPath} -> {TargetPath}", shortcutPath, targetPath);
        }
        
        /// <summary>
        /// Initializes automatic startup if configured
        /// </summary>
        public void SetupAutoStart()
        {
            if (!_config.AutoStartAtLogin)
            {
                return;
            }
            
            try
            {
                var startupFolder = Path.Combine(
                    Environment.GetFolderPath(Environment.SpecialFolder.Startup));
                
                var executablePath = Path.Combine(_installationDirectory, "InventoryManagement.exe");
                
                CreateShortcut(
                    Path.Combine(startupFolder, "Tom General Trading.lnk"),
                    executablePath,
                    "Tom General Trading Inventory Management System");
                
                _logger.LogInformation("Set up auto-start at login");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to set up auto-start");
            }
        }
    }
}
