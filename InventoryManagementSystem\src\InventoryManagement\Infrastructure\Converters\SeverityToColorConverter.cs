using System;
using System.Globalization;
using System.Windows.Data;
using System.Windows.Media;
using InventoryManagement.Services.Validation;

namespace InventoryManagement.Infrastructure.Converters
{
    /// <summary>
    /// Converts IssueSeverity to a Color value for UI display
    /// </summary>
    public class SeverityToColorConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is IssueSeverity severity)
            {
                return severity switch
                {
                    IssueSeverity.Critical => Color.FromRgb(220, 53, 69),   // Bootstrap danger red
                    IssueSeverity.Error => Color.FromRgb(255, 128, 0),      // Orange
                    IssueSeverity.Warning => Color.FromRgb(255, 193, 7),    // Bootstrap warning yellow
                    IssueSeverity.Info => Color.FromRgb(13, 110, 253),      // Bootstrap primary blue
                    _ => Colors.Gray
                };
            }
            
            return Colors.Gray;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
