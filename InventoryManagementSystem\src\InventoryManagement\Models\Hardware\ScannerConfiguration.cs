using System.ComponentModel.DataAnnotations;

namespace InventoryManagement.Models.Hardware
{
    /// <summary>
    /// Configuration settings for barcode scanners
    /// </summary>
    public class ScannerConfiguration
    {
        public int Id { get; set; }
        
        [Required]
        [MaxLength(100)]
        public string Name { get; set; } = string.Empty;
        
        [Required]
        [MaxLength(50)]
        public string ConnectionType { get; set; } = "USB"; // USB, Serial, Bluetooth
        
        [MaxLength(20)]
        public string ComPort { get; set; } = string.Empty; // For serial connections
        
        public int BaudRate { get; set; } = 9600; // For serial connections
        
        [MaxLength(100)]
        public string DeviceId { get; set; } = string.Empty; // USB device identifier
        
        public bool EnableBeep { get; set; } = true;
        
        public bool EnableVibration { get; set; } = false;
        
        public int ScanTimeout { get; set; } = 5000; // Milliseconds
        
        public bool AutoEnterAfterScan { get; set; } = true;
        
        [MaxLength(10)]
        public string ScanPrefix { get; set; } = string.Empty;
        
        [MaxLength(10)]
        public string ScanSuffix { get; set; } = string.Empty;
        
        public bool IsActive { get; set; } = true;
        
        public bool IsDefault { get; set; } = false;
        
        public DateTime CreatedAt { get; set; } = DateTime.Now;
        
        public DateTime? LastModifiedAt { get; set; }
        
        /// <summary>
        /// Scanner sensitivity level (1-10)
        /// </summary>
        public int SensitivityLevel { get; set; } = 5;
        
        /// <summary>
        /// Supported barcode types (comma-separated)
        /// </summary>
        [MaxLength(500)]
        public string SupportedBarcodeTypes { get; set; } = "Code128,Code39,EAN13,EAN8,UPC-A,UPC-E";
        
        /// <summary>
        /// Additional scanner-specific settings as JSON
        /// </summary>
        public string? AdditionalSettings { get; set; }
    }
    
    /// <summary>
    /// Scanner connection types
    /// </summary>
    public static class ScannerConnectionTypes
    {
        public const string USB = "USB";
        public const string Serial = "Serial";
        public const string Bluetooth = "Bluetooth";
        public const string Network = "Network";
    }
    
    /// <summary>
    /// Common barcode types supported
    /// </summary>
    public static class BarcodeTypes
    {
        public const string Code128 = "Code128";
        public const string Code39 = "Code39";
        public const string EAN13 = "EAN13";
        public const string EAN8 = "EAN8";
        public const string UPCA = "UPC-A";
        public const string UPCE = "UPC-E";
        public const string QRCode = "QRCode";
        public const string DataMatrix = "DataMatrix";
    }
}
