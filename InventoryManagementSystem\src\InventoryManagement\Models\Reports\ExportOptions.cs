using System;
using System.Collections.Generic;

namespace InventoryManagement.Models.Reports
{
    /// <summary>
    /// Options for exporting data to files
    /// </summary>
    public class ExportOptions
    {
        /// <summary>
        /// Directory where exports will be saved if no path is specified
        /// </summary>
        public string ExportDirectory { get; set; }
        
        /// <summary>
        /// Whether to include column headers in the export
        /// </summary>
        public bool IncludeHeaders { get; set; } = true;
        
        /// <summary>
        /// Title to include in the report (for formats that support it)
        /// </summary>
        public string ReportTitle { get; set; }
        
        /// <summary>
        /// Footer text to include in the report (for formats that support it)
        /// </summary>
        public string ReportFooter { get; set; }
        
        /// <summary>
        /// Whether to format JSON with indentation
        /// </summary>
        public bool PrettyPrint { get; set; } = true;
        
        /// <summary>
        /// Whether to include timestamp in the generated file name
        /// </summary>
        public bool IncludeTimestampInFilename { get; set; } = true;
        
        /// <summary>
        /// Custom filename template (if not using auto-generated names)
        /// </summary>
        public string FilenameTemplate { get; set; }
        
        /// <summary>
        /// Column formatting options keyed by column name
        /// </summary>
        public Dictionary<string, ColumnFormatting> ColumnFormats { get; set; } = new();
        
        /// <summary>
        /// Default date format string for date columns
        /// </summary>
        public string DefaultDateFormat { get; set; } = "yyyy-MM-dd";
        
        /// <summary>
        /// Default time format string for time columns
        /// </summary>
        public string DefaultTimeFormat { get; set; } = "HH:mm:ss";
        
        /// <summary>
        /// Default numeric format string for numeric columns
        /// </summary>
        public string DefaultNumericFormat { get; set; } = "#,##0.00";
        
        /// <summary>
        /// Default currency format string for currency columns
        /// </summary>
        public string DefaultCurrencyFormat { get; set; } = "C2";
        
        /// <summary>
        /// Whether to include totals for numeric columns (for formats that support it)
        /// </summary>
        public bool IncludeTotals { get; set; } = true;
        
        /// <summary>
        /// Whether to apply auto-filtering (for formats that support it like Excel)
        /// </summary>
        public bool AutoFilter { get; set; } = true;
        
        /// <summary>
        /// Whether to freeze the header row (for formats that support it like Excel)
        /// </summary>
        public bool FreezeHeaderRow { get; set; } = true;
    }
}
