using System;
using System.Collections.Generic;
using System.Data;

namespace InventoryManagement.Models.Reports
{
    /// <summary>
    /// Result of executing a report
    /// </summary>
    public class ReportResult
    {
        /// <summary>
        /// ID of the report that was executed
        /// </summary>
        public Guid ReportId { get; set; }
        
        /// <summary>
        /// Name of the report
        /// </summary>
        public string ReportName { get; set; }
        
        /// <summary>
        /// When the report was generated
        /// </summary>
        public DateTime GeneratedDate { get; set; } = DateTime.Now;
        
        /// <summary>
        /// User who generated the report
        /// </summary>
        public string GeneratedBy { get; set; }
        
        /// <summary>
        /// Parameters used for this report execution
        /// </summary>
        public Dictionary<string, object> Parameters { get; set; }
        
        /// <summary>
        /// The report data
        /// </summary>
        public DataTable Data { get; set; }
        
        /// <summary>
        /// Any error message if the report failed
        /// </summary>
        public string ErrorMessage { get; set; }
        
        /// <summary>
        /// Whether the report completed successfully
        /// </summary>
        public bool IsSuccess => string.IsNullOrEmpty(ErrorMessage);
        
        /// <summary>
        /// How long the report took to execute (in milliseconds)
        /// </summary>
        public long ExecutionTimeMs { get; set; }
        
        /// <summary>
        /// Row count in the result set
        /// </summary>
        public int RowCount => Data?.Rows.Count ?? 0;
    }
}
