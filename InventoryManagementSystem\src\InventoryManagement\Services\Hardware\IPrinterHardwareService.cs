using InventoryManagement.Models.Hardware;

namespace InventoryManagement.Services.Hardware
{
    /// <summary>
    /// Interface for receipt printer hardware integration
    /// </summary>
    public interface IPrinterHardwareService
    {
        /// <summary>
        /// Event fired when printer status changes
        /// </summary>
        event EventHandler<PrinterStatusChangedEventArgs> PrinterStatusChanged;
        
        /// <summary>
        /// Event fired when a print job completes
        /// </summary>
        event EventHandler<PrintJobCompletedEventArgs> PrintJobCompleted;
        
        /// <summary>
        /// Initialize the printer with the given configuration
        /// </summary>
        /// <param name="configuration">Printer configuration</param>
        /// <returns>True if initialization successful</returns>
        Task<bool> InitializeAsync(PrinterConfiguration configuration);
        
        /// <summary>
        /// Print a receipt with the given content
        /// </summary>
        /// <param name="receiptContent">Receipt content to print</param>
        /// <param name="copies">Number of copies to print</param>
        /// <returns>Print result</returns>
        Task<PrintResult> PrintReceiptAsync(string receiptContent, int copies = 1);
        
        /// <summary>
        /// Print raw text content
        /// </summary>
        /// <param name="content">Raw text content</param>
        /// <param name="formatting">Text formatting options</param>
        /// <returns>Print result</returns>
        Task<PrintResult> PrintTextAsync(string content, TextFormatting? formatting = null);
        
        /// <summary>
        /// Open the cash drawer (if connected)
        /// </summary>
        /// <returns>True if cash drawer opened successfully</returns>
        Task<bool> OpenCashDrawerAsync();
        
        /// <summary>
        /// Cut the paper (if auto-cut is supported)
        /// </summary>
        /// <returns>True if paper cut successfully</returns>
        Task<bool> CutPaperAsync();
        
        /// <summary>
        /// Check if printer is currently connected and ready
        /// </summary>
        bool IsConnected { get; }
        
        /// <summary>
        /// Check if printer is currently printing
        /// </summary>
        bool IsPrinting { get; }
        
        /// <summary>
        /// Get current printer configuration
        /// </summary>
        PrinterConfiguration? CurrentConfiguration { get; }
        
        /// <summary>
        /// Discover available printers
        /// </summary>
        /// <returns>List of available printer devices</returns>
        Task<List<PrinterDevice>> DiscoverPrintersAsync();
        
        /// <summary>
        /// Test printer connection and functionality
        /// </summary>
        /// <returns>Test result with details</returns>
        Task<PrinterTestResult> TestPrinterAsync();
        
        /// <summary>
        /// Disconnect from current printer
        /// </summary>
        Task DisconnectAsync();
        
        /// <summary>
        /// Get printer status information
        /// </summary>
        /// <returns>Current printer status</returns>
        PrinterStatus GetStatus();
        
        /// <summary>
        /// Print a test page
        /// </summary>
        /// <returns>Print result</returns>
        Task<PrintResult> PrintTestPageAsync();
    }
    
    /// <summary>
    /// Print result information
    /// </summary>
    public class PrintResult
    {
        public bool IsSuccessful { get; set; }
        public string Message { get; set; } = string.Empty;
        public string JobId { get; set; } = string.Empty;
        public DateTime PrintTime { get; set; } = DateTime.Now;
        public int PagesPrinted { get; set; }
        public string? ErrorCode { get; set; }
    }
    
    /// <summary>
    /// Text formatting options for printing
    /// </summary>
    public class TextFormatting
    {
        public bool Bold { get; set; } = false;
        public bool Italic { get; set; } = false;
        public bool Underline { get; set; } = false;
        public TextAlignment Alignment { get; set; } = TextAlignment.Left;
        public int FontSize { get; set; } = 12;
        public string FontName { get; set; } = "Arial";
        public int LineSpacing { get; set; } = 1;
    }
    
    /// <summary>
    /// Text alignment options
    /// </summary>
    public enum TextAlignment
    {
        Left,
        Center,
        Right,
        Justify
    }
    
    /// <summary>
    /// Event arguments for printer status changed event
    /// </summary>
    public class PrinterStatusChangedEventArgs : EventArgs
    {
        public bool IsConnected { get; set; }
        public bool IsOnline { get; set; }
        public bool HasPaper { get; set; }
        public string StatusMessage { get; set; } = string.Empty;
        public DateTime StatusTime { get; set; } = DateTime.Now;
    }
    
    /// <summary>
    /// Event arguments for print job completed event
    /// </summary>
    public class PrintJobCompletedEventArgs : EventArgs
    {
        public string JobId { get; set; } = string.Empty;
        public bool IsSuccessful { get; set; }
        public string Message { get; set; } = string.Empty;
        public DateTime CompletionTime { get; set; } = DateTime.Now;
    }
    
    /// <summary>
    /// Represents a discovered printer device
    /// </summary>
    public class PrinterDevice
    {
        public string DeviceId { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string ConnectionType { get; set; } = string.Empty;
        public string Port { get; set; } = string.Empty;
        public bool IsAvailable { get; set; } = true;
        public bool IsDefault { get; set; } = false;
        public string Manufacturer { get; set; } = string.Empty;
        public string Model { get; set; } = string.Empty;
        public List<string> SupportedPaperSizes { get; set; } = new List<string>();
    }
    
    /// <summary>
    /// Printer test result
    /// </summary>
    public class PrinterTestResult
    {
        public bool IsSuccessful { get; set; }
        public string Message { get; set; } = string.Empty;
        public bool CanPrint { get; set; }
        public bool HasPaper { get; set; }
        public bool CashDrawerConnected { get; set; }
        public Dictionary<string, object> AdditionalInfo { get; set; } = new Dictionary<string, object>();
    }
    
    /// <summary>
    /// Printer status information
    /// </summary>
    public class PrinterStatus
    {
        public bool IsConnected { get; set; }
        public bool IsOnline { get; set; }
        public bool IsPrinting { get; set; }
        public bool HasPaper { get; set; }
        public bool HasError { get; set; }
        public string ConnectionType { get; set; } = string.Empty;
        public string DeviceInfo { get; set; } = string.Empty;
        public int PrintJobsToday { get; set; }
        public string? LastError { get; set; }
        public DateTime LastPrintTime { get; set; }
    }
}
