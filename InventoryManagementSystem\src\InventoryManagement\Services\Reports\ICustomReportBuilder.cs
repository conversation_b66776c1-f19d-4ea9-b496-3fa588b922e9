using System;
using System.Collections.Generic;
using System.Data;
using System.Threading.Tasks;
using InventoryManagement.Models.Reports;

namespace InventoryManagement.Services.Reports
{
    /// <summary>
    /// Interface for building and managing custom reports defined by users
    /// </summary>
    public interface ICustomReportBuilder
    {
        /// <summary>
        /// Gets a list of all available custom report definitions
        /// </summary>
        Task<List<CustomReportDefinition>> GetCustomReportsAsync();
        
        /// <summary>
        /// Saves a custom report definition
        /// </summary>
        Task SaveCustomReportAsync(CustomReportDefinition report);
        
        /// <summary>
        /// Deletes a custom report definition
        /// </summary>
        Task DeleteCustomReportAsync(Guid reportId);
        
        /// <summary>
        /// Executes a custom report and returns the result
        /// </summary>
        Task<ReportResult> ExecuteCustomReportAsync(
            CustomReportDefinition report, 
            Dictionary<string, object> parameters = null);
        
        /// <summary>
        /// Exports a report result to a file
        /// </summary>
        Task<string> ExportCustomReportAsync(
            ReportResult result, 
            ExportFormat format, 
            string outputPath = null);
    }
}
