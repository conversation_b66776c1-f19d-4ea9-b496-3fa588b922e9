<UserControl x:Class="InventoryManagement.Views.Reports.ReportsDashboardView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             mc:Ignorable="d" 
             d:DesignHeight="900" d:DesignWidth="1600">
    
    <UserControl.Resources>
        <Style x:Key="DashboardCardStyle" TargetType="Border">
            <Setter Property="Background" Value="White"/>
            <Setter Property="BorderBrush" Value="#E0E0E0"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="CornerRadius" Value="8"/>
            <Setter Property="Padding" Value="20"/>
            <Setter Property="Margin" Value="10"/>
        </Style>
        
        <Style x:Key="MetricValueStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="28"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Foreground" Value="#2C3E50"/>
        </Style>
        
        <Style x:Key="MetricLabelStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Foreground" Value="#7F8C8D"/>
            <Setter Property="Margin" Value="0,5,0,0"/>
        </Style>
        
        <Style x:Key="ActionButtonStyle" TargetType="Button">
            <Setter Property="Padding" Value="15,8"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="MinWidth" Value="120"/>
            <Setter Property="Background" Value="#3498DB"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
        </Style>
    </UserControl.Resources>

    <ScrollViewer VerticalScrollBarVisibility="Auto">
        <Grid Margin="20">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <!-- Header -->
            <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,20">
                <TextBlock Text="Reports Dashboard" FontSize="28" FontWeight="Bold" Foreground="#2C3E50"/>
                <Button Content="Refresh" Command="{Binding RefreshDashboardCommand}" 
                        Style="{StaticResource ActionButtonStyle}" Margin="30,0,0,0"/>
                <DatePicker SelectedDate="{Binding SelectedDate}" Margin="20,0,0,0" VerticalAlignment="Center"/>
                <TextBlock Text="{Binding ReportStatus}" VerticalAlignment="Center" Margin="20,0,0,0" 
                           Foreground="#7F8C8D" FontStyle="Italic"/>
            </StackPanel>

            <!-- Key Metrics Cards -->
            <Grid Grid.Row="1" Margin="0,0,0,30">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- Sales Card -->
                <Border Grid.Column="0" Style="{StaticResource DashboardCardStyle}">
                    <StackPanel>
                        <TextBlock Text="{Binding SalesSummary.TodaysSales, StringFormat=C}" Style="{StaticResource MetricValueStyle}" Foreground="#27AE60"/>
                        <TextBlock Text="Today's Sales" Style="{StaticResource MetricLabelStyle}"/>
                        <StackPanel Orientation="Horizontal" Margin="0,10,0,0">
                            <TextBlock Text="{Binding SalesSummary.SalesGrowth, StringFormat=+{0:F1}%}" Foreground="#27AE60"/>
                            <TextBlock Text=" vs yesterday" Foreground="#95A5A6" Margin="5,0,0,0"/>
                        </StackPanel>
                        <TextBlock Text="{Binding SalesSummary.TodaysTransactions, StringFormat='{0} transactions'}" 
                                   Foreground="#7F8C8D" FontSize="12" Margin="0,5,0,0"/>
                    </StackPanel>
                </Border>

                <!-- Inventory Card -->
                <Border Grid.Column="1" Style="{StaticResource DashboardCardStyle}">
                    <StackPanel>
                        <TextBlock Text="{Binding InventorySummary.TotalInventoryValue, StringFormat=C}" Style="{StaticResource MetricValueStyle}" Foreground="#3498DB"/>
                        <TextBlock Text="Inventory Value" Style="{StaticResource MetricLabelStyle}"/>
                        <StackPanel Orientation="Horizontal" Margin="0,10,0,0">
                            <TextBlock Text="{Binding InventorySummary.LowStockAlerts}" Foreground="#E74C3C"/>
                            <TextBlock Text=" low stock alerts" Foreground="#95A5A6" Margin="5,0,0,0"/>
                        </StackPanel>
                        <TextBlock Text="{Binding InventorySummary.TotalItems, StringFormat='{0} total items'}" 
                                   Foreground="#7F8C8D" FontSize="12" Margin="0,5,0,0"/>
                    </StackPanel>
                </Border>

                <!-- Profit Card -->
                <Border Grid.Column="2" Style="{StaticResource DashboardCardStyle}">
                    <StackPanel>
                        <TextBlock Text="{Binding FinancialSummary.TodaysProfit, StringFormat=C}" Style="{StaticResource MetricValueStyle}" Foreground="#9B59B6"/>
                        <TextBlock Text="Today's Profit" Style="{StaticResource MetricLabelStyle}"/>
                        <StackPanel Orientation="Horizontal" Margin="0,10,0,0">
                            <TextBlock Text="{Binding FinancialSummary.ProfitMargin, StringFormat={}{0:P1}}" Foreground="#8E44AD"/>
                            <TextBlock Text=" profit margin" Foreground="#95A5A6" Margin="5,0,0,0"/>
                        </StackPanel>
                        <TextBlock Text="{Binding FinancialSummary.CashOnHand, StringFormat='Cash: {0:C}'}" 
                                   Foreground="#7F8C8D" FontSize="12" Margin="0,5,0,0"/>
                    </StackPanel>
                </Border>

                <!-- Transactions Card -->
                <Border Grid.Column="3" Style="{StaticResource DashboardCardStyle}">
                    <StackPanel>
                        <TextBlock Text="{Binding SalesSummary.TodaysTransactions}" Style="{StaticResource MetricValueStyle}" Foreground="#E67E22"/>
                        <TextBlock Text="Transactions Today" Style="{StaticResource MetricLabelStyle}"/>
                        <StackPanel Orientation="Horizontal" Margin="0,10,0,0">
                            <TextBlock Text="{Binding SalesSummary.AverageTransactionValue, StringFormat=C}" Foreground="#D35400"/>
                            <TextBlock Text=" avg. value" Foreground="#95A5A6" Margin="5,0,0,0"/>
                        </StackPanel>
                        <TextBlock Text="{Binding FinancialSummary.OutstandingCredit, StringFormat='Credit: {0:C}'}" 
                                   Foreground="#7F8C8D" FontSize="12" Margin="0,5,0,0"/>
                    </StackPanel>
                </Border>
            </Grid>

            <!-- Report Generation Section -->
            <Border Grid.Row="2" Style="{StaticResource DashboardCardStyle}" Margin="10,0,10,20">
                <StackPanel>
                    <TextBlock Text="Generate Reports" FontSize="18" FontWeight="Bold" Foreground="#2C3E50" Margin="0,0,0,15"/>
                    
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <!-- Date Range -->
                        <TextBlock Grid.Row="0" Grid.Column="0" Text="From:" VerticalAlignment="Center" Margin="0,0,10,0"/>
                        <DatePicker Grid.Row="0" Grid.Column="1" SelectedDate="{Binding FromDate}" Margin="0,0,20,0"/>
                        
                        <TextBlock Grid.Row="0" Grid.Column="2" Text="To:" VerticalAlignment="Center" Margin="0,0,10,0"/>
                        <DatePicker Grid.Row="0" Grid.Column="3" SelectedDate="{Binding ToDate}" Margin="0,0,20,0"/>

                        <!-- Report Type -->
                        <StackPanel Grid.Row="0" Grid.Column="4" Orientation="Horizontal">
                            <TextBlock Text="Report Type:" VerticalAlignment="Center" Margin="0,0,10,0"/>
                            <ComboBox ItemsSource="{Binding ReportTypes}" SelectedItem="{Binding SelectedReportType}" 
                                      Width="150" Margin="0,0,20,0"/>
                        </StackPanel>

                        <!-- Action Buttons -->
                        <StackPanel Grid.Row="1" Grid.Column="0" Grid.ColumnSpan="5" Orientation="Horizontal" Margin="0,15,0,0">
                            <Button Content="Generate Sales Report" Command="{Binding GenerateSalesReportCommand}" 
                                    Style="{StaticResource ActionButtonStyle}" Background="#27AE60"/>
                            <Button Content="Generate Inventory Report" Command="{Binding GenerateInventoryReportCommand}" 
                                    Style="{StaticResource ActionButtonStyle}" Background="#3498DB"/>
                            <Button Content="Generate Financial Report" Command="{Binding GenerateFinancialReportCommand}" 
                                    Style="{StaticResource ActionButtonStyle}" Background="#9B59B6"/>
                            
                            <Button Content="Export PDF" Command="{Binding ExportReportCommand}" CommandParameter="PDF"
                                    Style="{StaticResource ActionButtonStyle}" Background="#E74C3C" Margin="20,5,5,5"/>
                            <Button Content="Export Excel" Command="{Binding ExportReportCommand}" CommandParameter="Excel"
                                    Style="{StaticResource ActionButtonStyle}" Background="#F39C12"/>
                            <Button Content="Print Report" Command="{Binding PrintReportCommand}" CommandParameter="{Binding SelectedReportType}"
                                    Style="{StaticResource ActionButtonStyle}" Background="#34495E"/>
                        </StackPanel>
                    </Grid>

                    <!-- Progress Indicator -->
                    <ProgressBar IsIndeterminate="True" Height="4" Margin="0,15,0,0"/>
                </StackPanel>
            </Border>

            <!-- Charts and Data Section -->
            <Grid Grid.Row="3">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="2*"/>
                    <ColumnDefinition Width="10"/>
                    <ColumnDefinition Width="1*"/>
                </Grid.ColumnDefinitions>

                <!-- Charts Section -->
                <StackPanel Grid.Column="0">
                    <!-- Top Selling Items -->
                    <Border Style="{StaticResource DashboardCardStyle}" Margin="10,0,0,20">
                        <StackPanel>
                            <TextBlock Text="Top Selling Items (Last 7 Days)" FontSize="16" FontWeight="Bold" Foreground="#2C3E50" Margin="0,0,0,15"/>
                            <DataGrid ItemsSource="{Binding TopSellingItems}" AutoGenerateColumns="False" 
                                      IsReadOnly="True" GridLinesVisibility="Horizontal" HeadersVisibility="Column"
                                      AlternatingRowBackground="#F8F9FA" MaxHeight="200">
                                <DataGrid.Columns>
                                    <DataGridTextColumn Header="Item" Binding="{Binding ItemName}" Width="*"/>
                                    <DataGridTextColumn Header="Qty Sold" Binding="{Binding QuantitySold}" Width="80"/>
                                    <DataGridTextColumn Header="Revenue" Binding="{Binding TotalRevenue, StringFormat=C}" Width="100"/>
                                </DataGrid.Columns>
                            </DataGrid>
                        </StackPanel>
                    </Border>

                    <!-- Category Sales -->
                    <Border Style="{StaticResource DashboardCardStyle}" Margin="10,0,0,0">
                        <StackPanel>
                            <TextBlock Text="Sales by Category (Last 30 Days)" FontSize="16" FontWeight="Bold" Foreground="#2C3E50" Margin="0,0,0,15"/>
                            <DataGrid ItemsSource="{Binding CategorySalesData}" AutoGenerateColumns="False" 
                                      IsReadOnly="True" GridLinesVisibility="Horizontal" HeadersVisibility="Column"
                                      AlternatingRowBackground="#F8F9FA" MaxHeight="200">
                                <DataGrid.Columns>
                                    <DataGridTextColumn Header="Category" Binding="{Binding CategoryName}" Width="*"/>
                                    <DataGridTextColumn Header="Sales" Binding="{Binding TotalSales, StringFormat=C}" Width="100"/>
                                    <DataGridTextColumn Header="Items Sold" Binding="{Binding ItemsSold}" Width="80"/>
                                    <DataGridTextColumn Header="%" Binding="{Binding PercentageOfTotalSales, StringFormat=P1}" Width="60"/>
                                </DataGrid.Columns>
                            </DataGrid>
                        </StackPanel>
                    </Border>
                </StackPanel>

                <!-- Alerts and Quick Info -->
                <StackPanel Grid.Column="2">
                    <!-- Low Stock Alerts -->
                    <Border Style="{StaticResource DashboardCardStyle}" Margin="0,0,10,20">
                        <StackPanel>
                            <TextBlock Text="Low Stock Alerts" FontSize="16" FontWeight="Bold" Foreground="#E74C3C" Margin="0,0,0,15"/>
                            <ListBox ItemsSource="{Binding LowStockItems}" MaxHeight="200" BorderThickness="0">
                                <ListBox.ItemTemplate>
                                    <DataTemplate>
                                        <StackPanel Margin="0,5">
                                            <TextBlock Text="{Binding ItemName}" FontWeight="Bold" FontSize="12"/>
                                            <StackPanel Orientation="Horizontal">
                                                <TextBlock Text="{Binding CurrentStock, StringFormat='Stock: {0}'}" FontSize="11" Foreground="#7F8C8D"/>
                                                <TextBlock Text="{Binding MinimumStock, StringFormat=' / Min: {0}'}" FontSize="11" Foreground="#E74C3C"/>
                                            </StackPanel>
                                        </StackPanel>
                                    </DataTemplate>
                                </ListBox.ItemTemplate>
                            </ListBox>
                        </StackPanel>
                    </Border>

                    <!-- Quick Actions -->
                    <Border Style="{StaticResource DashboardCardStyle}" Margin="0,0,10,0">
                        <StackPanel>
                            <TextBlock Text="Quick Actions" FontSize="16" FontWeight="Bold" Foreground="#2C3E50" Margin="0,0,0,15"/>
                            <Button Content="View Detailed Sales Report" Command="{Binding ViewDetailedReportCommand}" CommandParameter="Sales"
                                    Style="{StaticResource ActionButtonStyle}" Background="#27AE60" HorizontalAlignment="Stretch" Margin="0,5"/>
                            <Button Content="View Inventory Report" Command="{Binding ViewDetailedReportCommand}" CommandParameter="Inventory"
                                    Style="{StaticResource ActionButtonStyle}" Background="#3498DB" HorizontalAlignment="Stretch" Margin="0,5"/>
                            <Button Content="View Financial Report" Command="{Binding ViewDetailedReportCommand}" CommandParameter="Financial"
                                    Style="{StaticResource ActionButtonStyle}" Background="#9B59B6" HorizontalAlignment="Stretch" Margin="0,5"/>
                        </StackPanel>
                    </Border>
                </StackPanel>
            </Grid>

            <!-- Loading Overlay -->
            <Grid Grid.Row="0" Grid.RowSpan="4" Background="#80000000">
                <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center" Background="White" Padding="30">
                    <ProgressBar IsIndeterminate="True" Width="200" Height="4" Margin="0,0,0,10"/>
                    <TextBlock Text="Loading dashboard data..." HorizontalAlignment="Center"/>
                </StackPanel>
            </Grid>
        </Grid>
    </ScrollViewer>
</UserControl>
