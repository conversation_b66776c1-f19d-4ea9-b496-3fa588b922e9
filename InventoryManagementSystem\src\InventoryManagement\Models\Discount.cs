using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace InventoryManagement.Models
{
    /// <summary>
    /// Represents a discount that can be applied to transactions or items
    /// </summary>
    public class Discount
    {
        [Key]
        public int Id { get; set; }

        /// <summary>
        /// Unique discount code
        /// </summary>
        [Required]
        [MaxLength(50)]
        public string DiscountCode { get; set; } = string.Empty;

        /// <summary>
        /// Display name for the discount
        /// </summary>
        [Required]
        [MaxLength(100)]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// Description of the discount
        /// </summary>
        [MaxLength(500)]
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// Type of discount (Percentage, Fixed Amount, etc.)
        /// </summary>
        [Required]
        public DiscountType Type { get; set; }

        /// <summary>
        /// Discount value (percentage or fixed amount)
        /// </summary>
        [Required]
        [Column(TypeName = "decimal(18,2)")]
        public decimal Value { get; set; }

        /// <summary>
        /// Minimum purchase amount required for discount
        /// </summary>
        [Column(TypeName = "decimal(18,2)")]
        public decimal MinimumPurchaseAmount { get; set; } = 0;

        /// <summary>
        /// Maximum discount amount (for percentage discounts)
        /// </summary>
        [Column(TypeName = "decimal(18,2)")]
        public decimal MaximumDiscountAmount { get; set; } = 0;

        /// <summary>
        /// Start date for discount validity
        /// </summary>
        public DateTime? StartDate { get; set; }

        /// <summary>
        /// End date for discount validity
        /// </summary>
        public DateTime? EndDate { get; set; }

        /// <summary>
        /// Whether the discount is currently active
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// Whether manager approval is required for this discount
        /// </summary>
        public bool RequiresApproval { get; set; } = false;

        /// <summary>
        /// Minimum user role required to apply this discount
        /// </summary>
        [MaxLength(50)]
        public string MinimumUserRole { get; set; } = "Cashier";

        /// <summary>
        /// Maximum number of times this discount can be used
        /// </summary>
        public int? MaxUsageCount { get; set; }

        /// <summary>
        /// Current usage count
        /// </summary>
        public int CurrentUsageCount { get; set; } = 0;

        /// <summary>
        /// Maximum uses per customer
        /// </summary>
        public int? MaxUsesPerCustomer { get; set; }

        /// <summary>
        /// Whether this discount can be combined with other discounts
        /// </summary>
        public bool CanCombineWithOthers { get; set; } = false;

        /// <summary>
        /// Priority for discount application (higher number = higher priority)
        /// </summary>
        public int Priority { get; set; } = 0;

        /// <summary>
        /// Applicable customer types (comma-separated)
        /// </summary>
        [MaxLength(200)]
        public string ApplicableCustomerTypes { get; set; } = string.Empty;

        /// <summary>
        /// Applicable item categories (comma-separated)
        /// </summary>
        [MaxLength(500)]
        public string ApplicableCategories { get; set; } = string.Empty;

        /// <summary>
        /// Excluded item categories (comma-separated)
        /// </summary>
        [MaxLength(500)]
        public string ExcludedCategories { get; set; } = string.Empty;

        /// <summary>
        /// Days of week when discount is valid (comma-separated)
        /// </summary>
        [MaxLength(50)]
        public string ValidDaysOfWeek { get; set; } = string.Empty;

        /// <summary>
        /// Time of day when discount starts being valid
        /// </summary>
        public TimeSpan? ValidFromTime { get; set; }

        /// <summary>
        /// Time of day when discount stops being valid
        /// </summary>
        public TimeSpan? ValidToTime { get; set; }

        /// <summary>
        /// User who created the discount
        /// </summary>
        [Required]
        public int CreatedByUserId { get; set; }

        /// <summary>
        /// Date when discount was created
        /// </summary>
        [Required]
        public DateTime CreatedDate { get; set; } = DateTime.Now;

        /// <summary>
        /// User who last modified the discount
        /// </summary>
        public int? LastModifiedByUserId { get; set; }

        /// <summary>
        /// Date when discount was last modified
        /// </summary>
        public DateTime? LastModifiedDate { get; set; }

        // Navigation properties
        [ForeignKey("CreatedByUserId")]
        public virtual User CreatedByUser { get; set; } = new User();

        [ForeignKey("LastModifiedByUserId")]
        public virtual User? LastModifiedByUser { get; set; }

        /// <summary>
        /// Check if discount is currently valid
        /// </summary>
        /// <returns>True if discount is valid</returns>
        public bool IsCurrentlyValid()
        {
            if (!IsActive) return false;

            var now = DateTime.Now;

            // Check date range
            if (StartDate.HasValue && now < StartDate.Value) return false;
            if (EndDate.HasValue && now > EndDate.Value) return false;

            // Check usage limits
            if (MaxUsageCount.HasValue && CurrentUsageCount >= MaxUsageCount.Value) return false;

            // Check day of week
            if (!string.IsNullOrEmpty(ValidDaysOfWeek))
            {
                var validDays = ValidDaysOfWeek.Split(',').Select(d => d.Trim());
                if (!validDays.Contains(now.DayOfWeek.ToString())) return false;
            }

            // Check time of day
            if (ValidFromTime.HasValue && now.TimeOfDay < ValidFromTime.Value) return false;
            if (ValidToTime.HasValue && now.TimeOfDay > ValidToTime.Value) return false;

            return true;
        }

        /// <summary>
        /// Calculate discount amount for a given purchase amount
        /// </summary>
        /// <param name="purchaseAmount">Purchase amount</param>
        /// <returns>Discount amount</returns>
        public decimal CalculateDiscountAmount(decimal purchaseAmount)
        {
            if (purchaseAmount < MinimumPurchaseAmount) return 0;

            decimal discountAmount = 0;

            switch (Type)
            {
                case DiscountType.Percentage:
                    discountAmount = purchaseAmount * (Value / 100);
                    if (MaximumDiscountAmount > 0 && discountAmount > MaximumDiscountAmount)
                        discountAmount = MaximumDiscountAmount;
                    break;

                case DiscountType.FixedAmount:
                    discountAmount = Math.Min(Value, purchaseAmount);
                    break;

                case DiscountType.BuyXGetYFree:
                    // This would require additional logic based on item quantities
                    // For now, return 0 - implement based on specific requirements
                    discountAmount = 0;
                    break;
            }

            return Math.Round(discountAmount, 2);
        }

        /// <summary>
        /// Check if discount is applicable to a customer type
        /// </summary>
        /// <param name="customerType">Customer type</param>
        /// <returns>True if applicable</returns>
        public bool IsApplicableToCustomerType(string customerType)
        {
            if (string.IsNullOrEmpty(ApplicableCustomerTypes)) return true;

            var applicableTypes = ApplicableCustomerTypes.Split(',').Select(t => t.Trim());
            return applicableTypes.Contains(customerType);
        }

        /// <summary>
        /// Check if discount is applicable to an item category
        /// </summary>
        /// <param name="category">Item category</param>
        /// <returns>True if applicable</returns>
        public bool IsApplicableToCategory(string category)
        {
            // Check excluded categories first
            if (!string.IsNullOrEmpty(ExcludedCategories))
            {
                var excludedCats = ExcludedCategories.Split(',').Select(c => c.Trim());
                if (excludedCats.Contains(category)) return false;
            }

            // If no specific categories defined, applicable to all
            if (string.IsNullOrEmpty(ApplicableCategories)) return true;

            var applicableCats = ApplicableCategories.Split(',').Select(c => c.Trim());
            return applicableCats.Contains(category);
        }
    }

    /// <summary>
    /// Represents a discount application record
    /// </summary>
    public class DiscountApplication
    {
        [Key]
        public int Id { get; set; }

        /// <summary>
        /// Discount that was applied
        /// </summary>
        [Required]
        public int DiscountId { get; set; }

        /// <summary>
        /// Transaction where discount was applied
        /// </summary>
        [Required]
        public int TransactionId { get; set; }

        /// <summary>
        /// Item where discount was applied (null for transaction-level discounts)
        /// </summary>
        public int? ItemId { get; set; }

        /// <summary>
        /// Amount of discount applied
        /// </summary>
        [Required]
        [Column(TypeName = "decimal(18,2)")]
        public decimal DiscountAmount { get; set; }

        /// <summary>
        /// User who applied the discount
        /// </summary>
        [Required]
        public int AppliedByUserId { get; set; }

        /// <summary>
        /// User who approved the discount (if approval was required)
        /// </summary>
        public int? ApprovedByUserId { get; set; }

        /// <summary>
        /// Date when discount was applied
        /// </summary>
        [Required]
        public DateTime AppliedDate { get; set; } = DateTime.Now;

        /// <summary>
        /// Date when discount was approved
        /// </summary>
        public DateTime? ApprovalDate { get; set; }

        /// <summary>
        /// Reason for applying the discount
        /// </summary>
        [MaxLength(200)]
        public string Reason { get; set; } = string.Empty;

        // Navigation properties
        [ForeignKey("DiscountId")]
        public virtual Discount Discount { get; set; } = new Discount();

        [ForeignKey("TransactionId")]
        public virtual Transaction Transaction { get; set; } = new Transaction();

        [ForeignKey("ItemId")]
        public virtual Item? Item { get; set; }

        [ForeignKey("AppliedByUserId")]
        public virtual User AppliedByUser { get; set; } = new User();

        [ForeignKey("ApprovedByUserId")]
        public virtual User? ApprovedByUser { get; set; }
    }

    /// <summary>
    /// Types of discounts
    /// </summary>
    public enum DiscountType
    {
        Percentage = 0,
        FixedAmount = 1,
        BuyXGetYFree = 2,
        BuyXGetYPercent = 3
    }

    /// <summary>
    /// Common discount codes
    /// </summary>
    public static class CommonDiscountCodes
    {
        public const string Employee = "EMPLOYEE";
        public const string Senior = "SENIOR";
        public const string Student = "STUDENT";
        public const string Loyalty = "LOYALTY";
        public const string Bulk = "BULK";
        public const string Clearance = "CLEARANCE";
        public const string Manager = "MANAGER";
    }
}
