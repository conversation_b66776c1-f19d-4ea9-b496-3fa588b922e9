using System;
using System.Collections.Generic;

namespace InventoryManagement.Models.Database
{
    /// <summary>
    /// Information about database size
    /// </summary>
    public class DatabaseSizeInfo
    {
        /// <summary>
        /// Name of the database
        /// </summary>
        public string DatabaseName { get; set; }
        
        /// <summary>
        /// Human-readable size string (e.g., "1.2 GB")
        /// </summary>
        public string Size { get; set; }
        
        /// <summary>
        /// Size in bytes
        /// </summary>
        public long SizeInBytes { get; set; }
    }
    
    /// <summary>
    /// Information about table sizes
    /// </summary>
    public class TableSizeInfo
    {
        /// <summary>
        /// Name of the table
        /// </summary>
        public string TableName { get; set; }
        
        /// <summary>
        /// Human-readable total size (table + indexes)
        /// </summary>
        public string TotalSize { get; set; }
        
        /// <summary>
        /// Size in bytes
        /// </summary>
        public long SizeInBytes { get; set; }
        
        /// <summary>
        /// Human-readable table size (without indexes)
        /// </summary>
        public string TableSize { get; set; }
        
        /// <summary>
        /// Human-readable index size
        /// </summary>
        public string IndexSize { get; set; }
        
        /// <summary>
        /// Number of rows in the table
        /// </summary>
        public long RowCount { get; set; }
    }
    
    /// <summary>
    /// Statistics about database connections
    /// </summary>
    public class ConnectionStats
    {
        /// <summary>
        /// Number of active connections
        /// </summary>
        public int ActiveConnections { get; set; }
        
        /// <summary>
        /// Maximum allowed connections
        /// </summary>
        public int MaxConnections { get; set; }
        
        /// <summary>
        /// Connection utilization percentage
        /// </summary>
        public double ConnectionUtilization { get; set; }
    }
    
    /// <summary>
    /// Statistics about cache hit ratio
    /// </summary>
    public class CacheHitRatio
    {
        /// <summary>
        /// Number of blocks read from disk
        /// </summary>
        public long HeapRead { get; set; }
        
        /// <summary>
        /// Number of blocks found in cache
        /// </summary>
        public long HeapHit { get; set; }
        
        /// <summary>
        /// Cache hit ratio percentage
        /// </summary>
        public double Ratio { get; set; }
    }
    
    /// <summary>
    /// Information about index usage
    /// </summary>
    public class IndexUsageInfo
    {
        /// <summary>
        /// Name of the table the index belongs to
        /// </summary>
        public string TableName { get; set; }
        
        /// <summary>
        /// Name of the index
        /// </summary>
        public string IndexName { get; set; }
        
        /// <summary>
        /// Number of times the index has been used
        /// </summary>
        public long Scans { get; set; }
        
        /// <summary>
        /// Human-readable size of the index
        /// </summary>
        public string Size { get; set; }
        
        /// <summary>
        /// Size in bytes
        /// </summary>
        public long SizeInBytes { get; set; }
    }
    
    /// <summary>
    /// Information about dead tuples (rows)
    /// </summary>
    public class DeadTupleInfo
    {
        /// <summary>
        /// Name of the table
        /// </summary>
        public string TableName { get; set; }
        
        /// <summary>
        /// Number of dead tuples
        /// </summary>
        public long DeadTuples { get; set; }
        
        /// <summary>
        /// Number of live tuples
        /// </summary>
        public long LiveTuples { get; set; }
        
        /// <summary>
        /// Percentage of dead tuples
        /// </summary>
        public double DeadTuplePercentage { get; set; }
    }
    
    /// <summary>
    /// Information about slow queries
    /// </summary>
    public class SlowQueryInfo
    {
        /// <summary>
        /// Text of the query
        /// </summary>
        public string QueryText { get; set; }
        
        /// <summary>
        /// Total execution time in milliseconds
        /// </summary>
        public double TotalTimeMs { get; set; }
        
        /// <summary>
        /// Number of times the query has been executed
        /// </summary>
        public long Calls { get; set; }
        
        /// <summary>
        /// Average execution time in milliseconds
        /// </summary>
        public double MeanTimeMs { get; set; }
        
        /// <summary>
        /// Percentage of total query time
        /// </summary>
        public double Percentage { get; set; }
    }
    
    /// <summary>
    /// Statistics about vacuum operations
    /// </summary>
    public class VacuumStats
    {
        /// <summary>
        /// Name of the table
        /// </summary>
        public string TableName { get; set; }
        
        /// <summary>
        /// Last time a manual vacuum was run
        /// </summary>
        public DateTime? LastVacuum { get; set; }
        
        /// <summary>
        /// Last time an automatic vacuum was run
        /// </summary>
        public DateTime? LastAutoVacuum { get; set; }
        
        /// <summary>
        /// Last time a manual analyze was run
        /// </summary>
        public DateTime? LastAnalyze { get; set; }
        
        /// <summary>
        /// Last time an automatic analyze was run
        /// </summary>
        public DateTime? LastAutoAnalyze { get; set; }
    }
    
    /// <summary>
    /// Result of database maintenance operations
    /// </summary>
    public class MaintenanceResult
    {
        /// <summary>
        /// When maintenance started
        /// </summary>
        public DateTime StartTime { get; set; }
        
        /// <summary>
        /// When maintenance completed
        /// </summary>
        public DateTime EndTime { get; set; }
        
        /// <summary>
        /// Whether maintenance was successful
        /// </summary>
        public bool Success { get; set; }
        
        /// <summary>
        /// Error message if maintenance failed
        /// </summary>
        public string ErrorMessage { get; set; }
        
        /// <summary>
        /// List of operations performed
        /// </summary>
        public List<string> Operations { get; set; } = new List<string>();
        
        /// <summary>
        /// Duration of maintenance
        /// </summary>
        public TimeSpan Duration => EndTime - StartTime;
    }
}
