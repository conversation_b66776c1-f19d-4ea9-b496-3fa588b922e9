using System.Threading;
using System.Threading.Tasks;

namespace InventoryManagement.Infrastructure.Scheduling
{
    /// <summary>
    /// Interface for task handler implementations that can be executed by the task scheduler
    /// </summary>
    public interface ITaskHandler
    {
        /// <summary>
        /// Executes the task with the given parameters
        /// </summary>
        Task ExecuteAsync(ScheduledTask task, CancellationToken cancellationToken);
    }
}
