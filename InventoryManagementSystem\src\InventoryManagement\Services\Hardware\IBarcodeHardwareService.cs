using InventoryManagement.Models.Hardware;

namespace InventoryManagement.Services.Hardware
{
    /// <summary>
    /// Interface for barcode scanner hardware integration
    /// </summary>
    public interface IBarcodeHardwareService
    {
        /// <summary>
        /// Event fired when a barcode is successfully scanned
        /// </summary>
        event EventHandler<BarcodeScannedEventArgs> BarcodeScanned;
        
        /// <summary>
        /// Event fired when scanner connection status changes
        /// </summary>
        event EventHandler<ScannerStatusChangedEventArgs> ScannerStatusChanged;
        
        /// <summary>
        /// Initialize the barcode scanner with the given configuration
        /// </summary>
        /// <param name="configuration">Scanner configuration</param>
        /// <returns>True if initialization successful</returns>
        Task<bool> InitializeAsync(ScannerConfiguration configuration);
        
        /// <summary>
        /// Start scanning for barcodes
        /// </summary>
        /// <returns>True if scanning started successfully</returns>
        Task<bool> StartScanningAsync();
        
        /// <summary>
        /// Stop scanning for barcodes
        /// </summary>
        /// <returns>True if scanning stopped successfully</returns>
        Task<bool> StopScanningAsync();
        
        /// <summary>
        /// Check if scanner is currently connected and ready
        /// </summary>
        /// <returns>True if scanner is ready</returns>
        bool IsConnected { get; }
        
        /// <summary>
        /// Check if scanner is currently scanning
        /// </summary>
        /// <returns>True if currently scanning</returns>
        bool IsScanning { get; }
        
        /// <summary>
        /// Get current scanner configuration
        /// </summary>
        ScannerConfiguration? CurrentConfiguration { get; }
        
        /// <summary>
        /// Discover available barcode scanners
        /// </summary>
        /// <returns>List of available scanner devices</returns>
        Task<List<ScannerDevice>> DiscoverScannersAsync();
        
        /// <summary>
        /// Test scanner connection and functionality
        /// </summary>
        /// <returns>Test result with details</returns>
        Task<ScannerTestResult> TestScannerAsync();
        
        /// <summary>
        /// Disconnect from current scanner
        /// </summary>
        Task DisconnectAsync();
        
        /// <summary>
        /// Configure scanner beep settings
        /// </summary>
        /// <param name="enableBeep">Enable or disable beep</param>
        /// <param name="volume">Beep volume (0-10)</param>
        Task<bool> ConfigureBeepAsync(bool enableBeep, int volume = 5);
        
        /// <summary>
        /// Get scanner status information
        /// </summary>
        /// <returns>Current scanner status</returns>
        ScannerStatus GetStatus();
    }
    
    /// <summary>
    /// Event arguments for barcode scanned event
    /// </summary>
    public class BarcodeScannedEventArgs : EventArgs
    {
        public string Barcode { get; set; } = string.Empty;
        public string BarcodeType { get; set; } = string.Empty;
        public DateTime ScanTime { get; set; } = DateTime.Now;
        public bool IsValid { get; set; } = true;
        public string? ErrorMessage { get; set; }
    }
    
    /// <summary>
    /// Event arguments for scanner status changed event
    /// </summary>
    public class ScannerStatusChangedEventArgs : EventArgs
    {
        public bool IsConnected { get; set; }
        public string StatusMessage { get; set; } = string.Empty;
        public DateTime StatusTime { get; set; } = DateTime.Now;
    }
    
    /// <summary>
    /// Represents a discovered scanner device
    /// </summary>
    public class ScannerDevice
    {
        public string DeviceId { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string ConnectionType { get; set; } = string.Empty;
        public string Port { get; set; } = string.Empty;
        public bool IsAvailable { get; set; } = true;
        public string Manufacturer { get; set; } = string.Empty;
        public string Model { get; set; } = string.Empty;
    }
    
    /// <summary>
    /// Scanner test result
    /// </summary>
    public class ScannerTestResult
    {
        public bool IsSuccessful { get; set; }
        public string Message { get; set; } = string.Empty;
        public TimeSpan ResponseTime { get; set; }
        public List<string> SupportedBarcodeTypes { get; set; } = new List<string>();
        public Dictionary<string, object> AdditionalInfo { get; set; } = new Dictionary<string, object>();
    }
    
    /// <summary>
    /// Scanner status information
    /// </summary>
    public class ScannerStatus
    {
        public bool IsConnected { get; set; }
        public bool IsScanning { get; set; }
        public string ConnectionType { get; set; } = string.Empty;
        public string DeviceInfo { get; set; } = string.Empty;
        public DateTime LastScanTime { get; set; }
        public int TotalScansToday { get; set; }
        public string? LastError { get; set; }
    }
}
