using System;
using System.Collections.Generic;
using System.IO;
using Microsoft.Extensions.Logging;

namespace InventoryManagement.Infrastructure.Installation
{
    /// <summary>
    /// Configuration for the application installer
    /// </summary>
    public class InstallerConfiguration
    {
        /// <summary>
        /// Current application version
        /// </summary>
        public Version ApplicationVersion { get; set; } = new Version(1, 0, 0);
        
        /// <summary>
        /// Minimum supported PostgreSQL version
        /// </summary>
        public Version MinimumPostgreSQLVersion { get; set; } = new Version(11, 0, 0);
        
        /// <summary>
        /// Whether to install as a silent installation
        /// </summary>
        public bool SilentInstallation { get; set; }
        
        /// <summary>
        /// Custom installation directory
        /// </summary>
        public string CustomInstallDirectory { get; set; }
        
        /// <summary>
        /// Whether to create desktop shortcuts
        /// </summary>
        public bool CreateDesktopShortcuts { get; set; } = true;
        
        /// <summary>
        /// Whether to create start menu shortcuts
        /// </summary>
        public bool CreateStartMenuShortcuts { get; set; } = true;
        
        /// <summary>
        /// Whether to auto-start the application at login
        /// </summary>
        public bool AutoStartAtLogin { get; set; }
        
        /// <summary>
        /// Default database connection string template
        /// </summary>
        public string DefaultConnectionStringTemplate { get; set; } = 
            "Server=localhost;Port=5432;Database=TomGeneralTrading;User Id={0};Password=***;";
        
        /// <summary>
        /// Default backup directory relative to installation path
        /// </summary>
        public string DefaultBackupDirectory { get; set; } = "App_Data\\Backups";
    }
}
