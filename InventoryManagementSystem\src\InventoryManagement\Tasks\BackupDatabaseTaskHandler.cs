using System;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using InventoryManagement.Infrastructure.Scheduling;
using InventoryManagement.Services.Database;
using Microsoft.Extensions.Logging;

namespace InventoryManagement.Tasks
{
    /// <summary>
    /// Task handler for database backups
    /// </summary>
    public class BackupDatabaseTaskHandler : ITaskHandler
    {
        private readonly ILocalDatabaseBackupService _backupService;
        private readonly ILogger<BackupDatabaseTaskHandler> _logger;
        
        public BackupDatabaseTaskHandler(
            ILocalDatabaseBackupService backupService,
            ILogger<BackupDatabaseTaskHandler> logger)
        {
            _backupService = backupService ?? throw new ArgumentNullException(nameof(backupService));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }
        
        /// <summary>
        /// Executes the database backup task
        /// </summary>
        public async Task ExecuteAsync(ScheduledTask task, CancellationToken cancellationToken)
        {
            _logger.LogInformation("Starting scheduled database backup");
            
            try
            {
                // Parse parameters
                BackupParameters parameters;
                if (!string.IsNullOrEmpty(task.TaskParametersJson))
                {
                    parameters = JsonSerializer.Deserialize<BackupParameters>(task.TaskParametersJson);
                }
                else
                {
                    parameters = new BackupParameters();
                }
                
                // Create a backup with the specified name or default
                string backupPath = await _backupService.CreateBackupAsync(parameters.CustomBackupName);
                
                _logger.LogInformation("Successfully created scheduled backup at: {BackupPath}", backupPath);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error executing scheduled database backup");
                throw;
            }
        }
        
        /// <summary>
        /// Parameters for database backup task
        /// </summary>
        public class BackupParameters
        {
            /// <summary>
            /// Custom name for the backup file (optional)
            /// </summary>
            public string CustomBackupName { get; set; }
        }
    }
}
