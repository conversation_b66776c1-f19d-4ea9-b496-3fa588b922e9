using System.Collections.ObjectModel;
using System.Windows.Input;
using InventoryManagement.Models.Reports;
using InventoryManagement.Services.Reports;
using InventoryManagement.ViewModels.Base;
using Microsoft.Extensions.Logging;

namespace InventoryManagement.ViewModels.Reports
{
    /// <summary>
    /// ViewModel for the reports dashboard
    /// </summary>
    public class ReportsDashboardViewModel : BaseViewModel
    {
        private readonly ISalesReportService _salesReportService;
        private readonly IInventoryReportService _inventoryReportService;
        private readonly IFinancialReportService _financialReportService;
        private readonly ILogger<ReportsDashboardViewModel> _logger;

        // Dashboard data
        private SalesSummaryDashboard? _salesSummary;
        private InventorySummaryDashboard? _inventorySummary;
        private FinancialSummaryDashboard? _financialSummary;

        // Report parameters
        private DateTime _selectedDate = DateTime.Today;
        private DateTime _fromDate = DateTime.Today.AddDays(-30);
        private DateTime _toDate = DateTime.Today;
        private string _selectedReportType = "Sales";

        // Collections
        private ObservableCollection<TopSellingItem> _topSellingItems;
        private ObservableCollection<LowStockItem> _lowStockItems;
        private ObservableCollection<HourlySales> _hourlySalesData;
        private ObservableCollection<CategorySales> _categorySalesData;

        // UI state
        private bool _isGeneratingReport = false;
        private string _reportStatus = "Ready";

        public ReportsDashboardViewModel(
            ISalesReportService salesReportService,
            IInventoryReportService inventoryReportService,
            IFinancialReportService financialReportService,
            ILogger<ReportsDashboardViewModel> logger)
        {
            _salesReportService = salesReportService;
            _inventoryReportService = inventoryReportService;
            _financialReportService = financialReportService;
            _logger = logger;

            // Initialize collections
            _topSellingItems = new ObservableCollection<TopSellingItem>();
            _lowStockItems = new ObservableCollection<LowStockItem>();
            _hourlySalesData = new ObservableCollection<HourlySales>();
            _categorySalesData = new ObservableCollection<CategorySales>();

            // Initialize commands
            InitializeCommands();

            // Load initial dashboard data
            _ = LoadDashboardDataAsync();
        }

        #region Properties

        public SalesSummaryDashboard? SalesSummary
        {
            get => _salesSummary;
            set => SetProperty(ref _salesSummary, value);
        }

        public InventorySummaryDashboard? InventorySummary
        {
            get => _inventorySummary;
            set => SetProperty(ref _inventorySummary, value);
        }

        public FinancialSummaryDashboard? FinancialSummary
        {
            get => _financialSummary;
            set => SetProperty(ref _financialSummary, value);
        }

        public DateTime SelectedDate
        {
            get => _selectedDate;
            set
            {
                if (SetProperty(ref _selectedDate, value))
                {
                    _ = LoadDashboardDataAsync();
                }
            }
        }

        public DateTime FromDate
        {
            get => _fromDate;
            set => SetProperty(ref _fromDate, value);
        }

        public DateTime ToDate
        {
            get => _toDate;
            set => SetProperty(ref _toDate, value);
        }

        public string SelectedReportType
        {
            get => _selectedReportType;
            set => SetProperty(ref _selectedReportType, value);
        }

        public ObservableCollection<TopSellingItem> TopSellingItems
        {
            get => _topSellingItems;
            set => SetProperty(ref _topSellingItems, value);
        }

        public ObservableCollection<LowStockItem> LowStockItems
        {
            get => _lowStockItems;
            set => SetProperty(ref _lowStockItems, value);
        }

        public ObservableCollection<HourlySales> HourlySalesData
        {
            get => _hourlySalesData;
            set => SetProperty(ref _hourlySalesData, value);
        }

        public ObservableCollection<CategorySales> CategorySalesData
        {
            get => _categorySalesData;
            set => SetProperty(ref _categorySalesData, value);
        }

        public bool IsGeneratingReport
        {
            get => _isGeneratingReport;
            set => SetProperty(ref _isGeneratingReport, value);
        }

        public string ReportStatus
        {
            get => _reportStatus;
            set => SetProperty(ref _reportStatus, value);
        }

        public List<string> ReportTypes { get; } = new List<string>
        {
            "Sales", "Inventory", "Financial", "Customer", "Product Performance"
        };

        public List<string> ExportFormats { get; } = new List<string>
        {
            "PDF", "Excel", "CSV"
        };

        #endregion

        #region Commands

        public ICommand RefreshDashboardCommand { get; private set; } = null!;
        public ICommand GenerateSalesReportCommand { get; private set; } = null!;
        public ICommand GenerateInventoryReportCommand { get; private set; } = null!;
        public ICommand GenerateFinancialReportCommand { get; private set; } = null!;
        public ICommand ExportReportCommand { get; private set; } = null!;
        public ICommand ViewDetailedReportCommand { get; private set; } = null!;
        public ICommand PrintReportCommand { get; private set; } = null!;

        private void InitializeCommands()
        {
            RefreshDashboardCommand = new RelayCommand(async () => await LoadDashboardDataAsync());
            GenerateSalesReportCommand = new RelayCommand(async () => await GenerateSalesReportAsync());
            GenerateInventoryReportCommand = new RelayCommand(async () => await GenerateInventoryReportAsync());
            GenerateFinancialReportCommand = new RelayCommand(async () => await GenerateFinancialReportAsync());
            ExportReportCommand = new RelayCommand<string>(async (format) => await ExportReportAsync(format ?? "PDF"));
            ViewDetailedReportCommand = new RelayCommand<string>((reportType) => ViewDetailedReport(reportType ?? "Sales"));
            PrintReportCommand = new RelayCommand<string>(async (reportType) => await PrintReportAsync(reportType ?? "Sales"));
        }

        #endregion

        #region Methods

        private async Task LoadDashboardDataAsync()
        {
            try
            {
                IsLoading = true;
                ReportStatus = "Loading dashboard data...";

                // Load sales summary
                SalesSummary = await _salesReportService.GetSalesSummaryForDashboardAsync(SelectedDate);

                // Load inventory summary
                InventorySummary = await _inventoryReportService.GetInventorySummaryForDashboardAsync();

                // Load financial summary
                FinancialSummary = await _financialReportService.GetFinancialSummaryForDashboardAsync(SelectedDate);

                // Load top selling items
                var topItems = await _salesReportService.GetTopSellingItemsAsync(SelectedDate.AddDays(-7), SelectedDate, 10);
                TopSellingItems.Clear();
                foreach (var item in topItems)
                {
                    TopSellingItems.Add(item);
                }

                // Load low stock items
                var lowStock = await _inventoryReportService.GetLowStockItemsAsync();
                LowStockItems.Clear();
                foreach (var item in lowStock.Take(10))
                {
                    LowStockItems.Add(item);
                }

                // Load hourly sales data
                var hourlySales = await _salesReportService.GetHourlySalesBreakdownAsync(SelectedDate);
                HourlySalesData.Clear();
                foreach (var hour in hourlySales)
                {
                    HourlySalesData.Add(hour);
                }

                // Load category sales data
                var categorySales = await _salesReportService.GetSalesByCategoryAsync(SelectedDate.AddDays(-30), SelectedDate);
                CategorySalesData.Clear();
                foreach (var category in categorySales.Take(10))
                {
                    CategorySalesData.Add(category);
                }

                ReportStatus = "Dashboard loaded successfully";
                _logger.LogInformation("Dashboard data loaded for date {Date}", SelectedDate);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading dashboard data");
                ReportStatus = "Error loading dashboard data";
                ShowErrorMessage("Failed to load dashboard data: " + ex.Message);
            }
            finally
            {
                IsLoading = false;
            }
        }

        private async Task GenerateSalesReportAsync()
        {
            try
            {
                IsGeneratingReport = true;
                ReportStatus = "Generating sales report...";

                var parameters = new SalesReportParameters
                {
                    FromDate = FromDate,
                    ToDate = ToDate,
                    IncludeCashierPerformance = true,
                    IncludeCustomerAnalytics = true,
                    IncludeDiscounts = true,
                    IncludeRefunds = true,
                    TopItemsCount = 20
                };

                var report = await _salesReportService.GenerateSalesReportAsync(parameters);
                
                // Navigate to detailed report view or show in popup
                ShowSuccessMessage($"Sales report generated successfully. Total sales: {report.NetSales:C}");
                ReportStatus = "Sales report generated";

                _logger.LogInformation("Sales report generated for period {FromDate} to {ToDate}", FromDate, ToDate);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating sales report");
                ReportStatus = "Error generating sales report";
                ShowErrorMessage("Failed to generate sales report: " + ex.Message);
            }
            finally
            {
                IsGeneratingReport = false;
            }
        }

        private async Task GenerateInventoryReportAsync()
        {
            try
            {
                IsGeneratingReport = true;
                ReportStatus = "Generating inventory report...";

                var parameters = new InventoryReportParameters
                {
                    AsOfDate = ToDate,
                    IncludeLowStockAlerts = true,
                    IncludeOutOfStockAlerts = true,
                    IncludeOverstockAlerts = true,
                    IncludeTurnoverAnalysis = true,
                    IncludeValuation = true
                };

                var report = await _inventoryReportService.GenerateInventoryReportAsync(parameters);
                
                ShowSuccessMessage($"Inventory report generated successfully. Total value: {report.TotalInventoryValue:C}");
                ReportStatus = "Inventory report generated";

                _logger.LogInformation("Inventory report generated as of {AsOfDate}", ToDate);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating inventory report");
                ReportStatus = "Error generating inventory report";
                ShowErrorMessage("Failed to generate inventory report: " + ex.Message);
            }
            finally
            {
                IsGeneratingReport = false;
            }
        }

        private async Task GenerateFinancialReportAsync()
        {
            try
            {
                IsGeneratingReport = true;
                ReportStatus = "Generating financial report...";

                var parameters = new FinancialReportParameters
                {
                    FromDate = FromDate,
                    ToDate = ToDate,
                    IncludeProfitabilityAnalysis = true,
                    IncludeComparativeAnalysis = true,
                    IncludeCashReconciliation = true
                };

                var report = await _financialReportService.GenerateFinancialReportAsync(parameters);
                
                ShowSuccessMessage($"Financial report generated successfully. Net revenue: {report.NetRevenue:C}");
                ReportStatus = "Financial report generated";

                _logger.LogInformation("Financial report generated for period {FromDate} to {ToDate}", FromDate, ToDate);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating financial report");
                ReportStatus = "Error generating financial report";
                ShowErrorMessage("Failed to generate financial report: " + ex.Message);
            }
            finally
            {
                IsGeneratingReport = false;
            }
        }

        private async Task ExportReportAsync(string format)
        {
            try
            {
                IsGeneratingReport = true;
                ReportStatus = $"Exporting report to {format}...";

                // Generate the appropriate report based on selected type
                switch (SelectedReportType.ToLower())
                {
                    case "sales":
                        var salesReport = await _salesReportService.GenerateSalesReportByDateRangeAsync(FromDate, ToDate);
                        var salesFormat = Enum.Parse<SalesReportFormat>(format);
                        var salesData = await _salesReportService.ExportSalesReportAsync(salesReport, salesFormat);
                        SaveReportFile(salesData, $"SalesReport_{DateTime.Now:yyyyMMdd}.{format.ToLower()}");
                        break;

                    case "inventory":
                        var inventoryReport = await _inventoryReportService.GenerateInventoryReportAsync(new InventoryReportParameters { AsOfDate = ToDate });
                        var inventoryFormat = Enum.Parse<InventoryReportFormat>(format);
                        var inventoryData = await _inventoryReportService.ExportInventoryReportAsync(inventoryReport, inventoryFormat);
                        SaveReportFile(inventoryData, $"InventoryReport_{DateTime.Now:yyyyMMdd}.{format.ToLower()}");
                        break;

                    case "financial":
                        var financialReport = await _financialReportService.GenerateFinancialReportAsync(new FinancialReportParameters { FromDate = FromDate, ToDate = ToDate });
                        var financialFormat = Enum.Parse<FinancialReportFormat>(format);
                        var financialData = await _financialReportService.ExportFinancialReportAsync(financialReport, financialFormat);
                        SaveReportFile(financialData, $"FinancialReport_{DateTime.Now:yyyyMMdd}.{format.ToLower()}");
                        break;
                }

                ShowSuccessMessage($"Report exported to {format} successfully");
                ReportStatus = $"Report exported to {format}";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error exporting report to {Format}", format);
                ReportStatus = "Error exporting report";
                ShowErrorMessage($"Failed to export report: {ex.Message}");
            }
            finally
            {
                IsGeneratingReport = false;
            }
        }

        private void ViewDetailedReport(string reportType)
        {
            // Navigate to detailed report view
            ShowInfoMessage($"Opening detailed {reportType} report view");
        }

        private async Task PrintReportAsync(string reportType)
        {
            try
            {
                IsGeneratingReport = true;
                ReportStatus = $"Preparing {reportType} report for printing...";

                // Generate and print the report
                ShowSuccessMessage($"{reportType} report sent to printer");
                ReportStatus = $"{reportType} report printed";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error printing {ReportType} report", reportType);
                ReportStatus = "Error printing report";
                ShowErrorMessage($"Failed to print report: {ex.Message}");
            }
            finally
            {
                IsGeneratingReport = false;
            }
        }

        private void SaveReportFile(byte[] data, string fileName)
        {
            // Save file to user-selected location
            // This would typically use a file dialog
            var filePath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.Desktop), fileName);
            File.WriteAllBytes(filePath, data);
            
            _logger.LogInformation("Report saved to {FilePath}", filePath);
        }

        #endregion
    }
}
