using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using InventoryManagement.Infrastructure.Validation;
using Microsoft.Extensions.Logging;

namespace InventoryManagement.Services.Validation
{
    /// <summary>
    /// Service for verifying data integrity across the inventory system
    /// </summary>
    public class DataIntegrityService : IDataIntegrityService
    {
        private readonly ILogger<DataIntegrityService> _logger;
        private readonly ICustomValidationService _customValidationService;
        private readonly List<IDataIntegrityRule> _integrityRules;
        
        public DataIntegrityService(
            ILogger<DataIntegrityService> logger,
            ICustomValidationService customValidationService,
            IEnumerable<IDataIntegrityRule> integrityRules = null)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _customValidationService = customValidationService ?? throw new ArgumentNullException(nameof(customValidationService));
            _integrityRules = integrityRules?.ToList() ?? new List<IDataIntegrityRule>();
        }
        
        /// <summary>
        /// Registers an integrity rule to be checked during validation
        /// </summary>
        public void RegisterRule(IDataIntegrityRule rule)
        {
            if (rule == null)
            {
                throw new ArgumentNullException(nameof(rule));
            }
            
            _integrityRules.Add(rule);
            _logger.LogInformation("Registered data integrity rule: {RuleName}", rule.Name);
        }
        
        /// <summary>
        /// Performs a comprehensive data integrity check across the system
        /// </summary>
        public async Task<DataIntegrityReport> VerifyDataIntegrityAsync()
        {
            _logger.LogInformation("Starting comprehensive data integrity verification");
            var startTime = DateTime.Now;
            var report = new DataIntegrityReport
            {
                StartTime = startTime,
                Issues = new List<DataIntegrityIssue>()
            };
            
            try
            {
                // Get custom validation rules
                var customRules = await _customValidationService.GetActiveValidationRulesAsync();
                _logger.LogInformation("Retrieved {Count} custom validation rules", customRules.Count);
                
                // Run all registered integrity rules
                foreach (var rule in _integrityRules)
                {
                    try
                    {
                        _logger.LogDebug("Running integrity rule: {RuleName}", rule.Name);
                        var ruleIssues = await rule.ValidateAsync();
                        
                        if (ruleIssues.Any())
                        {
                            _logger.LogWarning("Rule {RuleName} found {Count} issues", rule.Name, ruleIssues.Count);
                            report.Issues.AddRange(ruleIssues);
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Error running integrity rule: {RuleName}", rule.Name);
                        report.Issues.Add(new DataIntegrityIssue
                        {
                            EntityType = "System",
                            PropertyName = "IntegrityCheck",
                            Severity = IssueSeverity.Error,
                            Message = $"Rule execution failed: {rule.Name}",
                            Details = ex.ToString(),
                            RuleName = rule.Name
                        });
                    }
                }
                
                // Apply custom validation rules
                foreach (var rule in customRules)
                {
                    try
                    {
                        var ruleIssues = await ApplyCustomRuleAsync(rule);
                        report.Issues.AddRange(ruleIssues);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Error applying custom validation rule: {RuleId}", rule.Id);
                        report.Issues.Add(new DataIntegrityIssue
                        {
                            EntityType = rule.EntityType,
                            PropertyName = rule.PropertyName,
                            Severity = ConvertSeverity(rule.Severity),
                            Message = $"Custom rule execution failed: {rule.Name}",
                            Details = ex.ToString(),
                            RuleName = rule.Name
                        });
                    }
                }
                
                // Sort issues by severity
                report.Issues = report.Issues
                    .OrderByDescending(i => (int)i.Severity)
                    .ThenBy(i => i.EntityType)
                    .ThenBy(i => i.PropertyName)
                    .ToList();
                
                report.EndTime = DateTime.Now;
                report.Success = true;
                
                _logger.LogInformation(
                    "Completed data integrity verification in {Duration}ms. Found {IssueCount} issues.",
                    (report.EndTime - report.StartTime).TotalMilliseconds,
                    report.Issues.Count);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during data integrity verification");
                report.Success = false;
                report.ErrorMessage = ex.Message;
                report.EndTime = DateTime.Now;
            }
            
            return report;
        }
        
        /// <summary>
        /// Applies a single custom validation rule to verify data integrity
        /// </summary>
        private async Task<List<DataIntegrityIssue>> ApplyCustomRuleAsync(CustomValidationRule rule)
        {
            // In a real implementation, this would evaluate the rule expression against the database
            // For now, we'll just simulate it
            _logger.LogInformation("Applying custom validation rule: {RuleName}", rule.Name);
            
            // This is where you would query the database using the rule expression
            // and determine if there are any violations
            
            // For demo purposes, we'll just return an empty list
            return new List<DataIntegrityIssue>();
        }
        
        /// <summary>
        /// Converts custom validation severity to issue severity
        /// </summary>
        private IssueSeverity ConvertSeverity(ValidationSeverity severity)
        {
            return severity switch
            {
                ValidationSeverity.Warning => IssueSeverity.Warning,
                ValidationSeverity.Error => IssueSeverity.Error,
                ValidationSeverity.Critical => IssueSeverity.Critical,
                _ => IssueSeverity.Info
            };
        }
    }
}
