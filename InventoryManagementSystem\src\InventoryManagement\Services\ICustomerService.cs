using InventoryManagement.Models;

namespace InventoryManagement.Services
{
    /// <summary>
    /// Interface for customer management services
    /// </summary>
    public interface ICustomerService
    {
        /// <summary>
        /// Get customer by ID
        /// </summary>
        /// <param name="id">Customer ID</param>
        /// <returns>Customer or null if not found</returns>
        Task<Customer?> GetCustomerByIdAsync(int id);

        /// <summary>
        /// Get customer by customer number
        /// </summary>
        /// <param name="customerNumber">Customer number</param>
        /// <returns>Customer or null if not found</returns>
        Task<Customer?> GetCustomerByNumberAsync(string customerNumber);

        /// <summary>
        /// Get customer by phone number
        /// </summary>
        /// <param name="phoneNumber">Phone number</param>
        /// <returns>Customer or null if not found</returns>
        Task<Customer?> GetCustomerByPhoneAsync(string phoneNumber);

        /// <summary>
        /// Get customer by email address
        /// </summary>
        /// <param name="email">Email address</param>
        /// <returns>Customer or null if not found</returns>
        Task<Customer?> GetCustomerByEmailAsync(string email);

        /// <summary>
        /// Get all active customers
        /// </summary>
        /// <returns>List of customers</returns>
        Task<List<Customer>> GetAllCustomersAsync();

        /// <summary>
        /// Search customers by name, customer number, phone, or email
        /// </summary>
        /// <param name="searchTerm">Search term</param>
        /// <returns>List of matching customers</returns>
        Task<List<Customer>> SearchCustomersAsync(string searchTerm);

        /// <summary>
        /// Get customers by type
        /// </summary>
        /// <param name="customerType">Customer type</param>
        /// <returns>List of customers</returns>
        Task<List<Customer>> GetCustomersByTypeAsync(string customerType);

        /// <summary>
        /// Get top customers by total purchases
        /// </summary>
        /// <param name="count">Number of customers to return</param>
        /// <returns>List of top customers</returns>
        Task<List<Customer>> GetTopCustomersAsync(int count = 10);

        /// <summary>
        /// Create a new customer
        /// </summary>
        /// <param name="customer">Customer to create</param>
        /// <param name="userId">ID of user creating the customer</param>
        /// <returns>Created customer</returns>
        Task<Customer> CreateCustomerAsync(Customer customer, int userId);

        /// <summary>
        /// Update an existing customer
        /// </summary>
        /// <param name="customer">Customer to update</param>
        /// <param name="userId">ID of user updating the customer</param>
        /// <returns>Updated customer</returns>
        Task<Customer> UpdateCustomerAsync(Customer customer, int userId);

        /// <summary>
        /// Delete a customer (soft delete)
        /// </summary>
        /// <param name="customerId">Customer ID</param>
        /// <param name="userId">ID of user deleting the customer</param>
        /// <returns>True if deleted successfully</returns>
        Task<bool> DeleteCustomerAsync(int customerId, int userId);

        /// <summary>
        /// Get customer transaction history
        /// </summary>
        /// <param name="customerId">Customer ID</param>
        /// <param name="fromDate">Start date (optional)</param>
        /// <param name="toDate">End date (optional)</param>
        /// <returns>List of transactions</returns>
        Task<List<Transaction>> GetCustomerTransactionHistoryAsync(int customerId, DateTime? fromDate = null, DateTime? toDate = null);

        /// <summary>
        /// Get customer summary with statistics
        /// </summary>
        /// <param name="customerId">Customer ID</param>
        /// <returns>Customer summary</returns>
        Task<CustomerSummary> GetCustomerSummaryAsync(int customerId);

        /// <summary>
        /// Validate customer data
        /// </summary>
        /// <param name="customer">Customer to validate</param>
        /// <returns>True if valid, throws exception if invalid</returns>
        Task<bool> ValidateCustomerAsync(Customer customer);

        /// <summary>
        /// Update customer's total purchase amounts
        /// </summary>
        /// <param name="customerId">Customer ID</param>
        Task UpdateCustomerPurchaseTotalsAsync(int customerId);
    }
}
