<UserControl x:Class="InventoryManagement.Views.DataIntegrity.DataIntegrityView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:InventoryManagement.Views.DataIntegrity"
             xmlns:vm="clr-namespace:InventoryManagement.ViewModels.DataIntegrity"
             mc:Ignorable="d" 
             d:DataContext="{d:DesignInstance Type=vm:DataIntegrityViewModel}"
             d:DesignHeight="600" d:DesignWidth="800">
    
    <UserControl.Resources>
        <ResourceDictionary>
            <Style x:Key="SeverityIndicator" TargetType="Ellipse">
                <Setter Property="Width" Value="16"/>
                <Setter Property="Height" Value="16"/>
                <Setter Property="Margin" Value="0,0,8,0"/>
                <Setter Property="VerticalAlignment" Value="Center"/>
            </Style>
            
            <Style x:Key="HeaderStyle" TargetType="TextBlock">
                <Setter Property="FontWeight" Value="Bold"/>
                <Setter Property="FontSize" Value="14"/>
                <Setter Property="Margin" Value="0,0,0,8"/>
            </Style>
            
            <Style x:Key="SectionHeaderStyle" TargetType="TextBlock">
                <Setter Property="FontWeight" Value="SemiBold"/>
                <Setter Property="FontSize" Value="12"/>
                <Setter Property="Margin" Value="0,8,0,4"/>
            </Style>
            
            <Style x:Key="ActionButtonStyle" TargetType="Button">
                <Setter Property="Padding" Value="12,6"/>
                <Setter Property="Margin" Value="0,0,8,0"/>
                <Setter Property="MinWidth" Value="100"/>
            </Style>
            
            <Style x:Key="InfoBlockStyle" TargetType="TextBlock">
                <Setter Property="TextWrapping" Value="Wrap"/>
                <Setter Property="Margin" Value="0,2"/>
            </Style>
        </ResourceDictionary>
    </UserControl.Resources>
    
    <Grid Margin="16">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- Header -->
        <StackPanel Grid.Row="0">
            <TextBlock Text="Data Integrity Validation" 
                       FontSize="20" FontWeight="Bold" 
                       Margin="0,0,0,16"/>
            
            <TextBlock Text="Run comprehensive integrity checks on your data to ensure consistency and reliability. Identify and resolve issues to maintain the health of your database."
                       TextWrapping="Wrap" Margin="0,0,0,16"/>
            
            <!-- Action Buttons -->
            <StackPanel Orientation="Horizontal" Margin="0,0,0,16">
                <Button Content="Run Integrity Check" 
                        Command="{Binding RunCheckCommand}"
                        Style="{StaticResource ActionButtonStyle}"
                        IsEnabled="{Binding CanRunCheck}"/>
                
                <Button Content="Database Health" 
                        Command="{Binding RunDatabaseHealthCheckCommand}"
                        Style="{StaticResource ActionButtonStyle}"
                        IsEnabled="{Binding CanRunCheck}"/>
                
                <Button Content="Maintenance" 
                        Command="{Binding RunMaintenanceCommand}"
                        Style="{StaticResource ActionButtonStyle}"
                        IsEnabled="{Binding CanRunCheck}"
                        ToolTip="Perform database maintenance tasks like vacuum, analyze, and reindex"/>
                
                <Button Content="Export Report" 
                        Command="{Binding ExportReportCommand}"
                        Style="{StaticResource ActionButtonStyle}"
                        IsEnabled="{Binding HasReport}"/>
            </StackPanel>
            
            <!-- Status Message -->
            <Border Background="#FFE8EAF6" Padding="8" BorderThickness="1" BorderBrush="#FFCCCCCC">
                <TextBlock Text="{Binding StatusMessage}" TextWrapping="Wrap"/>
            </Border>
        </StackPanel>
        
        <!-- Summary Section -->
        <Grid Grid.Row="1" Margin="0,16,0,0" Visibility="{Binding HasReport, Converter={StaticResource BooleanToVisibilityConverter}}">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>
            
            <Border Grid.Column="0" Background="#FFF8D7DA" Margin="0,0,8,0" Padding="8" BorderThickness="1" BorderBrush="#FFCCCCCC">
                <StackPanel>
                    <TextBlock Text="Critical Issues" FontWeight="Bold"/>
                    <TextBlock Text="{Binding CriticalIssueCount}" FontSize="24"/>
                </StackPanel>
            </Border>
            
            <Border Grid.Column="1" Background="#FFFFF3CD" Margin="8,0,8,0" Padding="8" BorderThickness="1" BorderBrush="#FFCCCCCC">
                <StackPanel>
                    <TextBlock Text="Errors" FontWeight="Bold"/>
                    <TextBlock Text="{Binding ErrorIssueCount}" FontSize="24"/>
                </StackPanel>
            </Border>
            
            <Border Grid.Column="2" Background="#FFD1ECF1" Margin="8,0,0,0" Padding="8" BorderThickness="1" BorderBrush="#FFCCCCCC">
                <StackPanel>
                    <TextBlock Text="Warnings" FontWeight="Bold"/>
                    <TextBlock Text="{Binding WarningIssueCount}" FontSize="24"/>
                </StackPanel>
            </Border>
        </Grid>
        
        <!-- Database Health Summary -->
        <Border Grid.Row="1" Margin="0,16,0,0" Padding="8" BorderThickness="1" BorderBrush="#FFCCCCCC"
                Background="#FFE8F5E9" Visibility="{Binding HasHealthReport, Converter={StaticResource BooleanToVisibilityConverter}}">
            <StackPanel>
                <TextBlock Text="Database Health Score" FontWeight="Bold"/>
                <TextBlock>
                    <Run Text="{Binding HealthReport.HealthScore}" FontSize="24"/>
                    <Run Text="/100 -"/>
                    <Run Text="{Binding HealthReport.HealthStatus}" FontWeight="SemiBold"/>
                </TextBlock>
                
                <!-- Database Size -->
                <TextBlock Style="{StaticResource SectionHeaderStyle}" Text="Database Size"/>
                <TextBlock Style="{StaticResource InfoBlockStyle}">
                    <Run Text="{Binding HealthReport.DatabaseSize.DatabaseName}"/>
                    <Run Text=": "/>
                    <Run Text="{Binding HealthReport.DatabaseSize.Size}"/>
                </TextBlock>
                
                <!-- Cache Hit Ratio -->
                <TextBlock Style="{StaticResource SectionHeaderStyle}" Text="Cache Hit Ratio"/>
                <TextBlock Style="{StaticResource InfoBlockStyle}">
                    <Run Text="{Binding HealthReport.CacheHitRatio.Ratio, StringFormat=P2}"/>
                </TextBlock>
                
                <!-- Active Connections -->
                <TextBlock Style="{StaticResource SectionHeaderStyle}" Text="Connection Utilization"/>
                <TextBlock Style="{StaticResource InfoBlockStyle}">
                    <Run Text="{Binding HealthReport.ConnectionStats.ActiveConnections}"/>
                    <Run Text=" of "/>
                    <Run Text="{Binding HealthReport.ConnectionStats.MaxConnections}"/>
                    <Run Text=" ("/>
                    <Run Text="{Binding HealthReport.ConnectionStats.ConnectionUtilization, StringFormat=P0}"/>
                    <Run Text=")"/>
                </TextBlock>
                
                <!-- Recommendations -->
                <TextBlock Style="{StaticResource SectionHeaderStyle}" Text="Recommendations"/>
                <ItemsControl ItemsSource="{Binding HealthReport.Recommendations}">
                    <ItemsControl.ItemTemplate>
                        <DataTemplate>
                            <TextBlock Text="{Binding}" Style="{StaticResource InfoBlockStyle}" Margin="16,2,0,2"/>
                        </DataTemplate>
                    </ItemsControl.ItemTemplate>
                </ItemsControl>
            </StackPanel>
        </Border>
        
        <!-- Results Section -->
        <Grid Grid.Row="2" Margin="0,16,0,0">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>
            
            <!-- Issue List -->
            <ListView ItemsSource="{Binding Issues}" 
                      SelectedItem="{Binding SelectedIssue}"
                      Grid.Column="0"
                      BorderThickness="1"
                      BorderBrush="#FFCCCCCC">
                <ListView.ItemTemplate>
                    <DataTemplate>
                        <Grid Margin="2">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            
                            <!-- Severity Indicator -->
                            <Ellipse Style="{StaticResource SeverityIndicator}" Grid.Column="0">
                                <Ellipse.Fill>
                                    <SolidColorBrush Color="{Binding Severity, Converter={StaticResource SeverityToColorConverter}}"/>
                                </Ellipse.Fill>
                            </Ellipse>
                            
                            <!-- Issue Message -->
                            <TextBlock Text="{Binding Message}" Grid.Column="1" 
                                       TextWrapping="Wrap" VerticalAlignment="Center"/>
                            
                            <!-- Entity Type -->
                            <TextBlock Text="{Binding EntityType}" Grid.Column="2" 
                                       Margin="8,0,0,0" Opacity="0.6"
                                       VerticalAlignment="Center"/>
                        </Grid>
                    </DataTemplate>
                </ListView.ItemTemplate>
            </ListView>
            
            <!-- Issue Details -->
            <Border Grid.Column="1" Width="300" Margin="16,0,0,0"
                    BorderThickness="1" BorderBrush="#FFCCCCCC"
                    Padding="8" Background="#FFF5F5F5"
                    Visibility="{Binding CanViewDetails, Converter={StaticResource BooleanToVisibilityConverter}}">
                <ScrollViewer VerticalScrollBarVisibility="Auto">
                    <StackPanel>
                        <TextBlock Text="Issue Details" Style="{StaticResource HeaderStyle}"/>
                        
                        <TextBlock Text="Entity" Style="{StaticResource SectionHeaderStyle}"/>
                        <TextBlock Text="{Binding SelectedIssue.EntityType}" Style="{StaticResource InfoBlockStyle}"/>
                        
                        <TextBlock Text="Property" Style="{StaticResource SectionHeaderStyle}"/>
                        <TextBlock Text="{Binding SelectedIssue.PropertyName}" Style="{StaticResource InfoBlockStyle}"/>
                        
                        <TextBlock Text="Entity ID" Style="{StaticResource SectionHeaderStyle}" 
                                   Visibility="{Binding SelectedIssue.EntityId, Converter={StaticResource NullToVisibilityConverter}}"/>
                        <TextBlock Text="{Binding SelectedIssue.EntityId}" Style="{StaticResource InfoBlockStyle}"
                                   Visibility="{Binding SelectedIssue.EntityId, Converter={StaticResource NullToVisibilityConverter}}"/>
                        
                        <TextBlock Text="Detected" Style="{StaticResource SectionHeaderStyle}"/>
                        <TextBlock Text="{Binding SelectedIssue.DetectedTimeFormatted}" Style="{StaticResource InfoBlockStyle}"/>
                        
                        <TextBlock Text="Severity" Style="{StaticResource SectionHeaderStyle}"/>
                        <StackPanel Orientation="Horizontal">
                            <Ellipse Style="{StaticResource SeverityIndicator}">
                                <Ellipse.Fill>
                                    <SolidColorBrush Color="{Binding SelectedIssue.Severity, Converter={StaticResource SeverityToColorConverter}}"/>
                                </Ellipse.Fill>
                            </Ellipse>
                            <TextBlock Text="{Binding SelectedIssue.SeverityText}" Style="{StaticResource InfoBlockStyle}"/>
                        </StackPanel>
                        
                        <TextBlock Text="Details" Style="{StaticResource SectionHeaderStyle}"/>
                        <TextBlock Text="{Binding SelectedIssue.Details}" Style="{StaticResource InfoBlockStyle}"/>
                        
                        <TextBlock Text="Resolution" Style="{StaticResource SectionHeaderStyle}"/>
                        <TextBlock Text="{Binding SelectedIssue.ResolutionSuggestion}" Style="{StaticResource InfoBlockStyle}"/>
                        
                        <TextBlock Text="SQL Fix" Style="{StaticResource SectionHeaderStyle}"
                                   Visibility="{Binding SelectedIssue.RemediationQuery, Converter={StaticResource NullToVisibilityConverter}}"/>
                        <TextBox Text="{Binding SelectedIssue.RemediationQuery, Mode=OneWay}" 
                                 IsReadOnly="True"
                                 TextWrapping="Wrap"
                                 BorderThickness="1"
                                 MaxHeight="100"
                                 Visibility="{Binding SelectedIssue.RemediationQuery, Converter={StaticResource NullToVisibilityConverter}}"
                                 Margin="0,2"/>
                        
                        <Button Content="Apply Fix" 
                                Command="{Binding ApplyFixCommand}"
                                HorizontalAlignment="Left"
                                Style="{StaticResource ActionButtonStyle}"
                                IsEnabled="{Binding CanApplyFix}"
                                Margin="0,16,0,0"/>
                    </StackPanel>
                </ScrollViewer>
            </Border>
        </Grid>
        
        <!-- Footer -->
        <StackPanel Grid.Row="3" Margin="0,16,0,0">
            <TextBlock Text="Regular data integrity checks help maintain system reliability and prevent data corruption. We recommend running these checks weekly."
                       TextWrapping="Wrap" Opacity="0.7"/>
        </StackPanel>
    </Grid>
</UserControl>
