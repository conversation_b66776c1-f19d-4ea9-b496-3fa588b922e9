using System;

namespace InventoryManagement.Infrastructure.Shortcuts
{
    /// <summary>
    /// Defines a keyboard shortcut for the application
    /// </summary>
    public class ShortcutDefinition
    {
        /// <summary>
        /// Unique identifier for this shortcut
        /// </summary>
        public string Name { get; set; }
        
        /// <summary>
        /// User-friendly name for the shortcut
        /// </summary>
        public string DisplayName { get; set; }
        
        /// <summary>
        /// Key combination as string (e.g., "Ctrl+S" or "F5")
        /// </summary>
        public string KeyCombination { get; set; }
        
        /// <summary>
        /// Description of what this shortcut does
        /// </summary>
        public string Description { get; set; }
        
        /// <summary>
        /// Category for grouping shortcuts
        /// </summary>
        public string Category { get; set; }
        
        /// <summary>
        /// Whether this shortcut is currently active
        /// </summary>
        public bool IsActive { get; set; } = true;
    }
}
