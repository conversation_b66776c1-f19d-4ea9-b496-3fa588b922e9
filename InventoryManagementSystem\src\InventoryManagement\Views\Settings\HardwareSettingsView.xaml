<UserControl x:Class="InventoryManagement.Views.Settings.HardwareSettingsView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             mc:Ignorable="d" 
             d:DesignHeight="800" d:DesignWidth="1200">
    
    <UserControl.Resources>
        <Style x:Key="SectionHeaderStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="18"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Foreground" Value="#2C3E50"/>
            <Setter Property="Margin" Value="0,20,0,10"/>
        </Style>
        
        <Style x:Key="StatusIndicatorStyle" TargetType="Ellipse">
            <Setter Property="Width" Value="12"/>
            <Setter Property="Height" Value="12"/>
            <Setter Property="Margin" Value="5,0"/>
        </Style>
        
        <Style x:Key="ActionButtonStyle" TargetType="Button">
            <Setter Property="Padding" Value="15,8"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="MinWidth" Value="100"/>
        </Style>
    </UserControl.Resources>

    <ScrollViewer VerticalScrollBarVisibility="Auto">
        <Grid Margin="20">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <!-- Header -->
            <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,20">
                <TextBlock Text="Hardware Configuration" FontSize="24" FontWeight="Bold" Foreground="#2C3E50"/>
                <Button Content="Refresh Hardware" Command="{Binding RefreshHardwareCommand}" 
                        Style="{StaticResource ActionButtonStyle}" Margin="20,0,0,0"/>
            </StackPanel>

            <!-- Main Content -->
            <TabControl Grid.Row="1">
                
                <!-- Barcode Scanner Tab -->
                <TabItem Header="Barcode Scanner">
                    <Grid Margin="20">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="1*"/>
                            <ColumnDefinition Width="20"/>
                            <ColumnDefinition Width="1*"/>
                        </Grid.ColumnDefinitions>

                        <!-- Scanner Configuration -->
                        <GroupBox Grid.Column="0" Header="Scanner Configuration" Padding="15">
                            <StackPanel>
                                <!-- Configuration List -->
                                <TextBlock Text="Scanner Configurations" Style="{StaticResource SectionHeaderStyle}"/>
                                <ListBox ItemsSource="{Binding ScannerConfigurations}" 
                                         SelectedItem="{Binding SelectedScannerConfiguration}"
                                         Height="150" Margin="0,0,0,10">
                                    <ListBox.ItemTemplate>
                                        <DataTemplate>
                                            <StackPanel Orientation="Horizontal">
                                                <Ellipse Style="{StaticResource StatusIndicatorStyle}">
                                                    <Ellipse.Fill>
                                                        <SolidColorBrush Color="{Binding IsDefault, Converter={StaticResource BoolToColorConverter}}"/>
                                                    </Ellipse.Fill>
                                                </Ellipse>
                                                <TextBlock Text="{Binding Name}" Margin="5,0"/>
                                                <TextBlock Text="{Binding ConnectionType}" Foreground="Gray" Margin="10,0,0,0"/>
                                            </StackPanel>
                                        </DataTemplate>
                                    </ListBox.ItemTemplate>
                                </ListBox>

                                <!-- Configuration Actions -->
                                <StackPanel Orientation="Horizontal" Margin="0,10">
                                    <Button Content="Add" Command="{Binding AddScannerConfigCommand}" Style="{StaticResource ActionButtonStyle}"/>
                                    <Button Content="Delete" Command="{Binding DeleteScannerConfigCommand}" Style="{StaticResource ActionButtonStyle}"/>
                                    <Button Content="Save" Command="{Binding SaveConfigurationCommand}" Style="{StaticResource ActionButtonStyle}"/>
                                </StackPanel>

                                <!-- Configuration Details -->
                                <GroupBox Header="Configuration Details" Margin="0,20,0,0" Padding="10">
                                    <Grid IsEnabled="{Binding IsScannerSelected}">
                                        <Grid.RowDefinitions>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="Auto"/>
                                        </Grid.RowDefinitions>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>

                                        <TextBlock Grid.Row="0" Grid.Column="0" Text="Name:" Margin="0,5"/>
                                        <TextBox Grid.Row="0" Grid.Column="1" Text="{Binding SelectedScannerConfiguration.Name}" Margin="10,5"/>

                                        <TextBlock Grid.Row="1" Grid.Column="0" Text="Connection Type:" Margin="0,5"/>
                                        <ComboBox Grid.Row="1" Grid.Column="1" SelectedValue="{Binding SelectedScannerConfiguration.ConnectionType}" Margin="10,5">
                                            <ComboBoxItem Content="USB"/>
                                            <ComboBoxItem Content="Serial"/>
                                            <ComboBoxItem Content="Bluetooth"/>
                                        </ComboBox>

                                        <TextBlock Grid.Row="2" Grid.Column="0" Text="COM Port:" Margin="0,5"/>
                                        <TextBox Grid.Row="2" Grid.Column="1" Text="{Binding SelectedScannerConfiguration.ComPort}" Margin="10,5"/>

                                        <TextBlock Grid.Row="3" Grid.Column="0" Text="Baud Rate:" Margin="0,5"/>
                                        <ComboBox Grid.Row="3" Grid.Column="1" SelectedValue="{Binding SelectedScannerConfiguration.BaudRate}" Margin="10,5">
                                            <ComboBoxItem Content="9600"/>
                                            <ComboBoxItem Content="19200"/>
                                            <ComboBoxItem Content="38400"/>
                                            <ComboBoxItem Content="57600"/>
                                            <ComboBoxItem Content="115200"/>
                                        </ComboBox>

                                        <CheckBox Grid.Row="4" Grid.Column="1" Content="Enable Beep" IsChecked="{Binding SelectedScannerConfiguration.EnableBeep}" Margin="10,5"/>
                                        <CheckBox Grid.Row="5" Grid.Column="1" Content="Auto Enter After Scan" IsChecked="{Binding SelectedScannerConfiguration.AutoEnterAfterScan}" Margin="10,5"/>
                                    </Grid>
                                </GroupBox>
                            </StackPanel>
                        </GroupBox>

                        <!-- Scanner Status and Testing -->
                        <GroupBox Grid.Column="2" Header="Scanner Status & Testing" Padding="15">
                            <StackPanel>
                                <!-- Status -->
                                <TextBlock Text="Scanner Status" Style="{StaticResource SectionHeaderStyle}"/>
                                <StackPanel Orientation="Horizontal" Margin="0,10">
                                    <Ellipse Style="{StaticResource StatusIndicatorStyle}">
                                        <Ellipse.Fill>
                                            <SolidColorBrush Color="{Binding IsScannerConnected, Converter={StaticResource BoolToColorConverter}}"/>
                                        </Ellipse.Fill>
                                    </Ellipse>
                                    <TextBlock Text="{Binding ScannerStatus}" VerticalAlignment="Center"/>
                                </StackPanel>

                                <!-- Connection Actions -->
                                <StackPanel Orientation="Horizontal" Margin="0,20">
                                    <Button Content="Connect" Command="{Binding ConnectScannerCommand}" Style="{StaticResource ActionButtonStyle}"/>
                                    <Button Content="Disconnect" Command="{Binding DisconnectScannerCommand}" Style="{StaticResource ActionButtonStyle}"/>
                                    <Button Content="Test" Command="{Binding TestScannerCommand}" Style="{StaticResource ActionButtonStyle}"/>
                                </StackPanel>

                                <!-- Available Scanners -->
                                <TextBlock Text="Available Scanners" Style="{StaticResource SectionHeaderStyle}"/>
                                <ListBox ItemsSource="{Binding AvailableScanners}" 
                                         SelectedItem="{Binding SelectedAvailableScanner}"
                                         Height="120" Margin="0,10">
                                    <ListBox.ItemTemplate>
                                        <DataTemplate>
                                            <StackPanel>
                                                <TextBlock Text="{Binding Name}" FontWeight="Bold"/>
                                                <TextBlock Text="{Binding ConnectionType}" Foreground="Gray"/>
                                                <TextBlock Text="{Binding Port}" Foreground="Gray"/>
                                            </StackPanel>
                                        </DataTemplate>
                                    </ListBox.ItemTemplate>
                                </ListBox>

                                <!-- Test Results -->
                                <TextBlock Text="Test Results" Style="{StaticResource SectionHeaderStyle}"/>
                                <TextBox Text="{Binding TestResults}" IsReadOnly="True" 
                                         Height="100" TextWrapping="Wrap" VerticalScrollBarVisibility="Auto"
                                         Background="#F8F9FA" Margin="0,10"/>

                                <!-- Loading Indicator -->
                                <ProgressBar IsIndeterminate="True" Height="4" 
                                             Visibility="{Binding IsTesting, Converter={StaticResource BoolToVisibilityConverter}}"/>
                            </StackPanel>
                        </GroupBox>
                    </Grid>
                </TabItem>

                <!-- Receipt Printer Tab -->
                <TabItem Header="Receipt Printer">
                    <Grid Margin="20">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="1*"/>
                            <ColumnDefinition Width="20"/>
                            <ColumnDefinition Width="1*"/>
                        </Grid.ColumnDefinitions>

                        <!-- Printer Configuration -->
                        <GroupBox Grid.Column="0" Header="Printer Configuration" Padding="15">
                            <StackPanel>
                                <!-- Configuration List -->
                                <TextBlock Text="Printer Configurations" Style="{StaticResource SectionHeaderStyle}"/>
                                <ListBox ItemsSource="{Binding PrinterConfigurations}" 
                                         SelectedItem="{Binding SelectedPrinterConfiguration}"
                                         Height="150" Margin="0,0,0,10">
                                    <ListBox.ItemTemplate>
                                        <DataTemplate>
                                            <StackPanel Orientation="Horizontal">
                                                <Ellipse Style="{StaticResource StatusIndicatorStyle}">
                                                    <Ellipse.Fill>
                                                        <SolidColorBrush Color="{Binding IsDefault, Converter={StaticResource BoolToColorConverter}}"/>
                                                    </Ellipse.Fill>
                                                </Ellipse>
                                                <TextBlock Text="{Binding Name}" Margin="5,0"/>
                                                <TextBlock Text="{Binding PrinterType}" Foreground="Gray" Margin="10,0,0,0"/>
                                            </StackPanel>
                                        </DataTemplate>
                                    </ListBox.ItemTemplate>
                                </ListBox>

                                <!-- Configuration Actions -->
                                <StackPanel Orientation="Horizontal" Margin="0,10">
                                    <Button Content="Add" Command="{Binding AddPrinterConfigCommand}" Style="{StaticResource ActionButtonStyle}"/>
                                    <Button Content="Delete" Command="{Binding DeletePrinterConfigCommand}" Style="{StaticResource ActionButtonStyle}"/>
                                    <Button Content="Save" Command="{Binding SaveConfigurationCommand}" Style="{StaticResource ActionButtonStyle}"/>
                                </StackPanel>

                                <!-- Configuration Details -->
                                <GroupBox Header="Configuration Details" Margin="0,20,0,0" Padding="10">
                                    <Grid IsEnabled="{Binding IsPrinterSelected}">
                                        <Grid.RowDefinitions>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="Auto"/>
                                        </Grid.RowDefinitions>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>

                                        <TextBlock Grid.Row="0" Grid.Column="0" Text="Name:" Margin="0,5"/>
                                        <TextBox Grid.Row="0" Grid.Column="1" Text="{Binding SelectedPrinterConfiguration.Name}" Margin="10,5"/>

                                        <TextBlock Grid.Row="1" Grid.Column="0" Text="Printer Type:" Margin="0,5"/>
                                        <ComboBox Grid.Row="1" Grid.Column="1" SelectedValue="{Binding SelectedPrinterConfiguration.PrinterType}" Margin="10,5">
                                            <ComboBoxItem Content="Thermal"/>
                                            <ComboBoxItem Content="DotMatrix"/>
                                            <ComboBoxItem Content="Inkjet"/>
                                        </ComboBox>

                                        <TextBlock Grid.Row="2" Grid.Column="0" Text="Connection:" Margin="0,5"/>
                                        <ComboBox Grid.Row="2" Grid.Column="1" SelectedValue="{Binding SelectedPrinterConfiguration.ConnectionType}" Margin="10,5">
                                            <ComboBoxItem Content="USB"/>
                                            <ComboBoxItem Content="Serial"/>
                                            <ComboBoxItem Content="Network"/>
                                        </ComboBox>

                                        <TextBlock Grid.Row="3" Grid.Column="0" Text="Paper Width:" Margin="0,5"/>
                                        <ComboBox Grid.Row="3" Grid.Column="1" SelectedValue="{Binding SelectedPrinterConfiguration.PaperWidth}" Margin="10,5">
                                            <ComboBoxItem Content="58mm"/>
                                            <ComboBoxItem Content="80mm"/>
                                        </ComboBox>

                                        <CheckBox Grid.Row="4" Grid.Column="1" Content="Auto Cut" IsChecked="{Binding SelectedPrinterConfiguration.AutoCut}" Margin="10,5"/>
                                        <CheckBox Grid.Row="5" Grid.Column="1" Content="Enable Cash Drawer" IsChecked="{Binding SelectedPrinterConfiguration.EnableCashDrawer}" Margin="10,5"/>
                                        <CheckBox Grid.Row="6" Grid.Column="1" Content="Print Logo" IsChecked="{Binding SelectedPrinterConfiguration.PrintLogo}" Margin="10,5"/>
                                    </Grid>
                                </GroupBox>
                            </StackPanel>
                        </GroupBox>

                        <!-- Printer Status and Testing -->
                        <GroupBox Grid.Column="2" Header="Printer Status & Testing" Padding="15">
                            <StackPanel>
                                <!-- Status -->
                                <TextBlock Text="Printer Status" Style="{StaticResource SectionHeaderStyle}"/>
                                <StackPanel Orientation="Horizontal" Margin="0,10">
                                    <Ellipse Style="{StaticResource StatusIndicatorStyle}">
                                        <Ellipse.Fill>
                                            <SolidColorBrush Color="{Binding IsPrinterConnected, Converter={StaticResource BoolToColorConverter}}"/>
                                        </Ellipse.Fill>
                                    </Ellipse>
                                    <TextBlock Text="{Binding PrinterStatus}" VerticalAlignment="Center"/>
                                </StackPanel>

                                <!-- Connection Actions -->
                                <StackPanel Orientation="Horizontal" Margin="0,20">
                                    <Button Content="Connect" Command="{Binding ConnectPrinterCommand}" Style="{StaticResource ActionButtonStyle}"/>
                                    <Button Content="Disconnect" Command="{Binding DisconnectPrinterCommand}" Style="{StaticResource ActionButtonStyle}"/>
                                    <Button Content="Test Print" Command="{Binding TestPrinterCommand}" Style="{StaticResource ActionButtonStyle}"/>
                                </StackPanel>

                                <!-- Available Printers -->
                                <TextBlock Text="Available Printers" Style="{StaticResource SectionHeaderStyle}"/>
                                <ListBox ItemsSource="{Binding AvailablePrinters}" 
                                         SelectedItem="{Binding SelectedAvailablePrinter}"
                                         Height="120" Margin="0,10">
                                    <ListBox.ItemTemplate>
                                        <DataTemplate>
                                            <StackPanel>
                                                <TextBlock Text="{Binding Name}" FontWeight="Bold"/>
                                                <TextBlock Text="{Binding ConnectionType}" Foreground="Gray"/>
                                                <TextBlock Text="{Binding Port}" Foreground="Gray"/>
                                            </StackPanel>
                                        </DataTemplate>
                                    </ListBox.ItemTemplate>
                                </ListBox>

                                <!-- Test Results -->
                                <TextBlock Text="Test Results" Style="{StaticResource SectionHeaderStyle}"/>
                                <TextBox Text="{Binding TestResults}" IsReadOnly="True" 
                                         Height="100" TextWrapping="Wrap" VerticalScrollBarVisibility="Auto"
                                         Background="#F8F9FA" Margin="0,10"/>

                                <!-- Loading Indicator -->
                                <ProgressBar IsIndeterminate="True" Height="4" 
                                             Visibility="{Binding IsTesting, Converter={StaticResource BoolToVisibilityConverter}}"/>
                            </StackPanel>
                        </GroupBox>
                    </Grid>
                </TabItem>
            </TabControl>

            <!-- Loading Overlay -->
            <Grid Grid.Row="0" Grid.RowSpan="2" Background="#80000000" 
                  Visibility="{Binding IsLoading, Converter={StaticResource BoolToVisibilityConverter}}">
                <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center" Background="White" Padding="30">
                    <ProgressBar IsIndeterminate="True" Width="200" Height="4" Margin="0,0,0,10"/>
                    <TextBlock Text="Loading hardware configurations..." HorizontalAlignment="Center"/>
                </StackPanel>
            </Grid>
        </Grid>
    </ScrollViewer>
</UserControl>
