using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using InventoryManagement.Services.Database;
using Microsoft.Extensions.Logging;

namespace InventoryManagement.Services.Validation.Rules
{
    /// <summary>
    /// Rule to check for recent database backups and verify backup integrity
    /// </summary>
    public class BackupVerificationRule : IDataIntegrityRule
    {
        private readonly ILocalDatabaseBackupService _backupService;
        private readonly IBackupVerificationService _verificationService;
        private readonly ILogger<BackupVerificationRule> _logger;
        private readonly string _backupDirectory;
        
        public string Name => "Backup Verification";
        
        public string Description => "Verifies that recent backups exist and are valid";
        
        public string EntityType => "DatabaseBackup";
        
        // Maximum age allowed for most recent backup (24 hours)
        private readonly TimeSpan _maxBackupAge = TimeSpan.FromHours(24);
        
        public BackupVerificationRule(
            ILocalDatabaseBackupService backupService,
            IBackupVerificationService verificationService,
            ILogger<BackupVerificationRule> logger)
        {
            _backupService = backupService ?? throw new ArgumentNullException(nameof(backupService));
            _verificationService = verificationService ?? throw new ArgumentNullException(nameof(verificationService));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            
            // Get backup directory from backup service configuration
            _backupDirectory = Path.Combine(AppContext.BaseDirectory, "App_Data", "Backups");
        }
        
        /// <summary>
        /// Validates that there are recent and valid database backups
        /// </summary>
        public async Task<List<DataIntegrityIssue>> ValidateAsync()
        {
            _logger.LogInformation("Running {RuleName}", Name);
            var issues = new List<DataIntegrityIssue>();
            
            try
            {
                // Check if backup directory exists
                if (!Directory.Exists(_backupDirectory))
                {
                    issues.Add(new DataIntegrityIssue
                    {
                        EntityType = EntityType,
                        PropertyName = "BackupDirectory",
                        RuleName = Name,
                        Severity = IssueSeverity.Critical,
                        Message = "Backup directory does not exist",
                        Details = $"Backup directory not found: {_backupDirectory}",
                        ResolutionSuggestion = "Create the backup directory and configure automated backups",
                        CanAutoFix = true
                    });
                    
                    return issues;
                }
                
                // Get backup files ordered by last modified date
                var backupFiles = Directory.GetFiles(_backupDirectory, "*.sql")
                    .OrderByDescending(f => File.GetLastWriteTime(f))
                    .ToList();
                
                if (backupFiles.Count == 0)
                {
                    issues.Add(new DataIntegrityIssue
                    {
                        EntityType = EntityType,
                        PropertyName = "BackupFiles",
                        RuleName = Name,
                        Severity = IssueSeverity.Critical,
                        Message = "No database backups found",
                        Details = $"No backup files found in directory: {_backupDirectory}",
                        ResolutionSuggestion = "Create a database backup immediately",
                        CanAutoFix = true
                    });
                }
                else
                {
                    // Check age of most recent backup
                    var mostRecentBackup = backupFiles.First();
                    var backupDate = File.GetLastWriteTime(mostRecentBackup);
                    var backupAge = DateTime.Now - backupDate;
                    
                    if (backupAge > _maxBackupAge)
                    {
                        issues.Add(new DataIntegrityIssue
                        {
                            EntityType = EntityType,
                            PropertyName = "BackupAge",
                            RuleName = Name,
                            Severity = IssueSeverity.Error,
                            Message = "Most recent backup is too old",
                            Details = $"Most recent backup is {backupAge.TotalHours:F1} hours old (from {backupDate}). " +
                                     $"Maximum allowed age is {_maxBackupAge.TotalHours} hours.",
                            ResolutionSuggestion = "Create a new database backup and verify scheduled backups are working",
                            CanAutoFix = true
                        });
                    }
                    
                    // Verify integrity of the most recent backup
                    var verificationResult = await _verificationService.VerifyBackupComprehensiveAsync(mostRecentBackup);
                    
                    if (!verificationResult.IsValid)
                    {
                        issues.Add(new DataIntegrityIssue
                        {
                            EntityType = EntityType,
                            PropertyName = "BackupIntegrity",
                            RuleName = Name,
                            Severity = IssueSeverity.Critical,
                            Message = "Most recent backup failed integrity check",
                            Details = $"Backup file: {Path.GetFileName(mostRecentBackup)}, " +
                                     $"Error: {verificationResult.ErrorMessage}",
                            ResolutionSuggestion = "Create a new database backup immediately and investigate backup failures",
                            CanAutoFix = true
                        });
                    }
                    
                    // Check if there are too many backup files (cleanup policy)
                    int maxBackups = 10; // Should match the configured max backups
                    if (backupFiles.Count > maxBackups)
                    {
                        issues.Add(new DataIntegrityIssue
                        {
                            EntityType = EntityType,
                            PropertyName = "BackupCount",
                            RuleName = Name,
                            Severity = IssueSeverity.Warning,
                            Message = "Too many backup files",
                            Details = $"Found {backupFiles.Count} backup files, maximum allowed is {maxBackups}",
                            ResolutionSuggestion = "Clean up old backup files to save disk space",
                            CanAutoFix = true
                        });
                    }
                }
                
                _logger.LogInformation("Completed backup verification check");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error running backup verification rule");
                issues.Add(new DataIntegrityIssue
                {
                    EntityType = EntityType,
                    PropertyName = "System",
                    RuleName = Name,
                    Severity = IssueSeverity.Error,
                    Message = "Failed to check database backups",
                    Details = ex.ToString(),
                    CanAutoFix = false
                });
            }
            
            return issues;
        }
    }
}
