using System.Threading.Tasks;
using InventoryManagement.Models.Database;

namespace InventoryManagement.Services.Database.Monitoring
{
    /// <summary>
    /// Interface for monitoring PostgreSQL database health and performance
    /// </summary>
    public interface IDatabaseHealthMonitor
    {
        /// <summary>
        /// Gets comprehensive database health statistics including size, performance metrics,
        /// connection stats, cache hit ratios, and more
        /// </summary>
        /// <returns>A detailed report on database health</returns>
        Task<DatabaseHealthReport> GetDatabaseHealthReportAsync();
        
        /// <summary>
        /// Performs maintenance operations on the database to improve performance,
        /// such as vacuum, analyze, and reindexing operations
        /// </summary>
        /// <returns>Results of the maintenance operations</returns>
        Task<MaintenanceResult> PerformMaintenanceAsync();
    }
}
