using System;
using System.Collections.Generic;
using System.Linq;

namespace InventoryManagement.Services.Validation
{
    /// <summary>
    /// Report containing the results of a data integrity check
    /// </summary>
    public class DataIntegrityReport
    {
        /// <summary>
        /// When the integrity check was started
        /// </summary>
        public DateTime StartTime { get; set; }
        
        /// <summary>
        /// When the integrity check was completed
        /// </summary>
        public DateTime EndTime { get; set; }
        
        /// <summary>
        /// Whether the integrity check completed successfully
        /// </summary>
        public bool Success { get; set; }
        
        /// <summary>
        /// Error message if the integrity check failed
        /// </summary>
        public string ErrorMessage { get; set; }
        
        /// <summary>
        /// Issues found during the integrity check
        /// </summary>
        public List<DataIntegrityIssue> Issues { get; set; } = new List<DataIntegrityIssue>();
        
        /// <summary>
        /// User who initiated the integrity check
        /// </summary>
        public string InitiatedBy { get; set; }
        
        /// <summary>
        /// Duration of the integrity check
        /// </summary>
        public TimeSpan Duration => EndTime - StartTime;
        
        /// <summary>
        /// Number of critical issues found
        /// </summary>
        public int CriticalIssueCount => Issues?.Count(i => i.Severity == IssueSeverity.Critical) ?? 0;
        
        /// <summary>
        /// Number of error issues found
        /// </summary>
        public int ErrorIssueCount => Issues?.Count(i => i.Severity == IssueSeverity.Error) ?? 0;
        
        /// <summary>
        /// Number of warning issues found
        /// </summary>
        public int WarningIssueCount => Issues?.Count(i => i.Severity == IssueSeverity.Warning) ?? 0;
        
        /// <summary>
        /// Number of info issues found
        /// </summary>
        public int InfoIssueCount => Issues?.Count(i => i.Severity == IssueSeverity.Info) ?? 0;
        
        /// <summary>
        /// Total number of issues found
        /// </summary>
        public int TotalIssueCount => Issues?.Count ?? 0;
        
        /// <summary>
        /// Whether there are any critical issues
        /// </summary>
        public bool HasCriticalIssues => CriticalIssueCount > 0;
        
        /// <summary>
        /// Whether there are any error or critical issues
        /// </summary>
        public bool HasErrors => ErrorIssueCount > 0 || CriticalIssueCount > 0;
        
        /// <summary>
        /// Gets a summary of the report
        /// </summary>
        public string GetSummary()
        {
            string status = Success ? "completed successfully" : "failed";
            string durationMs = Duration.TotalMilliseconds.ToString("F0");
            
            return $"Data integrity check {status} in {durationMs}ms with " +
                   $"{CriticalIssueCount} critical, " +
                   $"{ErrorIssueCount} error, " +
                   $"{WarningIssueCount} warning issues";
        }
        
        /// <summary>
        /// Gets issues grouped by entity type
        /// </summary>
        public Dictionary<string, List<DataIntegrityIssue>> GetIssuesByEntityType()
        {
            return Issues
                .GroupBy(i => i.EntityType)
                .ToDictionary(g => g.Key, g => g.ToList());
        }
        
        /// <summary>
        /// Gets issues grouped by severity
        /// </summary>
        public Dictionary<IssueSeverity, List<DataIntegrityIssue>> GetIssuesBySeverity()
        {
            return Issues
                .GroupBy(i => i.Severity)
                .ToDictionary(g => g.Key, g => g.ToList());
        }
    }
}
