<Application x:Class="InventoryManagement.App"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:local="clr-namespace:InventoryManagement"
             xmlns:converters="clr-namespace:InventoryManagement.Converters"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             StartupUri="Views/LoginWindow.xaml">
    <Application.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <!-- Application Resources - Load First to Ensure Converters are Available -->
                <ResourceDictionary Source="/InventoryManagement;component/Resources/ReconciliationResources.xaml" />
                
                <!-- Material Design Theme -->
                <materialDesign:BundledTheme BaseTheme="Light" PrimaryColor="Blue" SecondaryColor="Orange" />
                <ResourceDictionary Source="pack://application:,,,/MaterialDesignThemes.Wpf;component/Themes/MaterialDesignTheme.Defaults.xaml" />
                
                <!-- Material Design Typography -->
                <ResourceDictionary Source="pack://application:,,,/MaterialDesignThemes.Wpf;component/Themes/MaterialDesignTheme.Typography.xaml" />
                
                <!-- Material Design Controls -->
                <ResourceDictionary Source="pack://application:,,,/MaterialDesignThemes.Wpf;component/Themes/MaterialDesignTheme.TextBlock.xaml" />
                <ResourceDictionary Source="pack://application:,,,/MaterialDesignThemes.Wpf;component/Themes/MaterialDesignTheme.Button.xaml" />
                <ResourceDictionary Source="pack://application:,,,/MaterialDesignThemes.Wpf;component/Themes/MaterialDesignTheme.Card.xaml" />
            </ResourceDictionary.MergedDictionaries>
            
            <!-- Material Design Settings -->
            <SolidColorBrush x:Key="PrimaryHueLightBrush" Color="#42A5F5"/>
            <SolidColorBrush x:Key="PrimaryHueMidBrush" Color="#1E88E5"/>
            <SolidColorBrush x:Key="PrimaryHueDarkBrush" Color="#1565C0"/>
            <SolidColorBrush x:Key="SecondaryHueMidBrush" Color="#FB8C00"/>
            
            <!-- Directly add the converter to solve the resource not found issue -->
            <converters:NumericToColorConverter x:Key="FinancialImpactToColorConverter" />

            <!-- Add missing converters -->
            <converters:BoolToVisibilityConverter x:Key="BoolToVisibilityConverter" />
            <converters:BoolToColorConverter x:Key="BoolToColorConverter" />
            
            <!-- Global Styles -->
            <Style x:Key="MainButtonStyle" TargetType="Button">
                <Setter Property="Padding" Value="15,8"/>
                <Setter Property="Margin" Value="5"/>
                <Setter Property="Background" Value="#1E88E5"/>
                <Setter Property="Foreground" Value="White"/>
                <Setter Property="BorderThickness" Value="0"/>
                <Setter Property="Template">
                    <Setter.Value>
                        <ControlTemplate TargetType="Button">
                            <Border Background="{TemplateBinding Background}" 
                                    BorderBrush="{TemplateBinding BorderBrush}" 
                                    BorderThickness="{TemplateBinding BorderThickness}"
                                    CornerRadius="4">
                                <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                            </Border>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
                <Style.Triggers>
                    <Trigger Property="IsMouseOver" Value="True">
                        <Setter Property="Background" Value="#1976D2"/>
                    </Trigger>
                </Style.Triggers>
            </Style>
            
            <Style x:Key="DashboardCardStyle" TargetType="Border">
                <Setter Property="Background" Value="White"/>
                <Setter Property="BorderBrush" Value="#EEEEEE"/>
                <Setter Property="BorderThickness" Value="1"/>
                <Setter Property="CornerRadius" Value="5"/>
                <Setter Property="Padding" Value="15"/>
                <Setter Property="Margin" Value="10"/>
                <Setter Property="Effect">
                    <Setter.Value>
                        <DropShadowEffect BlurRadius="10" ShadowDepth="1" Opacity="0.2"/>
                    </Setter.Value>
                </Setter>
            </Style>
            
            <Style x:Key="PageTitleStyle" TargetType="TextBlock">
                <Setter Property="FontSize" Value="24"/>
                <Setter Property="FontWeight" Value="SemiBold"/>
                <Setter Property="Margin" Value="10,20,10,10"/>
            </Style>
            
            <Style x:Key="SectionTitleStyle" TargetType="TextBlock">
                <Setter Property="FontSize" Value="18"/>
                <Setter Property="FontWeight" Value="SemiBold"/>
                <Setter Property="Margin" Value="10,10,10,5"/>
            </Style>
        </ResourceDictionary>
    </Application.Resources>
</Application>
