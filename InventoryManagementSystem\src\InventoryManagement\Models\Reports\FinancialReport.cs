using System.ComponentModel.DataAnnotations;

namespace InventoryManagement.Models.Reports
{
    /// <summary>
    /// Financial report model containing financial analytics and metrics
    /// </summary>
    public class FinancialReport
    {
        public DateTime ReportDate { get; set; } = DateTime.Now;
        public DateTime FromDate { get; set; }
        public DateTime ToDate { get; set; }
        public string ReportPeriod { get; set; } = string.Empty;
        public string GeneratedBy { get; set; } = string.Empty;

        // Revenue metrics
        public decimal GrossRevenue { get; set; }
        public decimal NetRevenue { get; set; }
        public decimal TotalRefunds { get; set; }
        public decimal TotalDiscounts { get; set; }

        // Cost metrics
        public decimal CostOfGoodsSold { get; set; }
        public decimal GrossProfit { get; set; }
        public decimal GrossProfitMargin { get; set; }

        // Cash flow
        public decimal CashSales { get; set; }
        public decimal CreditSales { get; set; }
        public decimal CashRefunds { get; set; }
        public decimal NetCashFlow { get; set; }

        // Payment method breakdown
        public List<PaymentMethodSummary> PaymentMethodData { get; set; } = new List<PaymentMethodSummary>();

        // Daily financial summary
        public List<DailyFinancialSummary> DailyFinancialData { get; set; } = new List<DailyFinancialSummary>();

        // Category profitability
        public List<CategoryProfitability> CategoryProfitabilityData { get; set; } = new List<CategoryProfitability>();

        // Location profitability
        public List<LocationProfitability> LocationProfitabilityData { get; set; } = new List<LocationProfitability>();

        // Cashier financial performance
        public List<CashierFinancialPerformance> CashierFinancialData { get; set; } = new List<CashierFinancialPerformance>();

        // Tax information (if applicable)
        public decimal TotalTaxCollected { get; set; }
        public List<TaxSummary> TaxSummaryData { get; set; } = new List<TaxSummary>();

        // Credit and debt management
        public decimal TotalCreditSales { get; set; }
        public decimal TotalCreditPayments { get; set; }
        public decimal OutstandingCredit { get; set; }
        public decimal OverdueCredit { get; set; }

        // Expense tracking
        public List<ExpenseSummary> ExpenseData { get; set; } = new List<ExpenseSummary>();
        public decimal TotalExpenses { get; set; }
        public decimal NetProfit { get; set; }

        // Key performance indicators
        public decimal RevenueGrowth { get; set; }
        public decimal ProfitGrowth { get; set; }
        public decimal AverageTransactionValue { get; set; }
        public decimal TransactionsPerDay { get; set; }
        public decimal RevenuePerSquareFoot { get; set; }

        // Comparative analysis
        public FinancialComparison PreviousPeriodComparison { get; set; } = new FinancialComparison();
        public FinancialComparison YearOverYearComparison { get; set; } = new FinancialComparison();
    }

    /// <summary>
    /// Payment method summary
    /// </summary>
    public class PaymentMethodSummary
    {
        public string PaymentMethod { get; set; } = string.Empty;
        public decimal Amount { get; set; }
        public int TransactionCount { get; set; }
        public decimal AverageTransactionValue { get; set; }
        public decimal PercentageOfTotal { get; set; }
        public decimal ProcessingFees { get; set; }
        public decimal NetAmount { get; set; }
    }

    /// <summary>
    /// Daily financial summary
    /// </summary>
    public class DailyFinancialSummary
    {
        public DateTime Date { get; set; }
        public string DayOfWeek { get; set; } = string.Empty;
        public decimal GrossRevenue { get; set; }
        public decimal NetRevenue { get; set; }
        public decimal CostOfGoodsSold { get; set; }
        public decimal GrossProfit { get; set; }
        public decimal GrossProfitMargin { get; set; }
        public int TransactionCount { get; set; }
        public decimal AverageTransactionValue { get; set; }
        public decimal TotalDiscounts { get; set; }
        public decimal TotalRefunds { get; set; }
        public decimal CashFlow { get; set; }
    }

    /// <summary>
    /// Category profitability analysis
    /// </summary>
    public class CategoryProfitability
    {
        public string CategoryName { get; set; } = string.Empty;
        public decimal Revenue { get; set; }
        public decimal CostOfGoodsSold { get; set; }
        public decimal GrossProfit { get; set; }
        public decimal GrossProfitMargin { get; set; }
        public int UnitsSold { get; set; }
        public decimal AverageSellingPrice { get; set; }
        public decimal AverageCost { get; set; }
        public decimal PercentageOfTotalRevenue { get; set; }
        public decimal PercentageOfTotalProfit { get; set; }
    }

    /// <summary>
    /// Location profitability analysis
    /// </summary>
    public class LocationProfitability
    {
        public int LocationId { get; set; }
        public string LocationName { get; set; } = string.Empty;
        public decimal Revenue { get; set; }
        public decimal CostOfGoodsSold { get; set; }
        public decimal GrossProfit { get; set; }
        public decimal GrossProfitMargin { get; set; }
        public int TransactionCount { get; set; }
        public decimal AverageTransactionValue { get; set; }
        public decimal OperatingExpenses { get; set; }
        public decimal NetProfit { get; set; }
        public decimal RevenuePerSquareFoot { get; set; }
        public decimal PercentageOfTotalRevenue { get; set; }
    }

    /// <summary>
    /// Cashier financial performance
    /// </summary>
    public class CashierFinancialPerformance
    {
        public int CashierId { get; set; }
        public string CashierName { get; set; } = string.Empty;
        public decimal TotalSales { get; set; }
        public decimal GrossProfit { get; set; }
        public decimal AverageTransactionValue { get; set; }
        public int TransactionCount { get; set; }
        public decimal TotalDiscountsGiven { get; set; }
        public decimal DiscountPercentage { get; set; }
        public decimal TotalRefundsProcessed { get; set; }
        public decimal CashHandled { get; set; }
        public decimal CashVariance { get; set; }
        public TimeSpan WorkHours { get; set; }
        public decimal SalesPerHour { get; set; }
    }

    /// <summary>
    /// Tax summary information
    /// </summary>
    public class TaxSummary
    {
        public string TaxType { get; set; } = string.Empty;
        public decimal TaxRate { get; set; }
        public decimal TaxableAmount { get; set; }
        public decimal TaxAmount { get; set; }
        public int TransactionCount { get; set; }
    }

    /// <summary>
    /// Expense summary
    /// </summary>
    public class ExpenseSummary
    {
        public string ExpenseCategory { get; set; } = string.Empty;
        public decimal Amount { get; set; }
        public int TransactionCount { get; set; }
        public decimal PercentageOfTotalExpenses { get; set; }
        public decimal PercentageOfRevenue { get; set; }
        public string Description { get; set; } = string.Empty;
    }

    /// <summary>
    /// Financial comparison data
    /// </summary>
    public class FinancialComparison
    {
        public string ComparisonPeriod { get; set; } = string.Empty;
        public decimal PreviousRevenue { get; set; }
        public decimal CurrentRevenue { get; set; }
        public decimal RevenueChange { get; set; }
        public decimal RevenueChangePercentage { get; set; }
        
        public decimal PreviousProfit { get; set; }
        public decimal CurrentProfit { get; set; }
        public decimal ProfitChange { get; set; }
        public decimal ProfitChangePercentage { get; set; }
        
        public int PreviousTransactionCount { get; set; }
        public int CurrentTransactionCount { get; set; }
        public int TransactionCountChange { get; set; }
        public decimal TransactionCountChangePercentage { get; set; }
        
        public decimal PreviousAverageTransactionValue { get; set; }
        public decimal CurrentAverageTransactionValue { get; set; }
        public decimal AverageTransactionValueChange { get; set; }
        public decimal AverageTransactionValueChangePercentage { get; set; }
    }

    /// <summary>
    /// Cash register reconciliation
    /// </summary>
    public class CashRegisterReconciliation
    {
        public int CashierId { get; set; }
        public string CashierName { get; set; } = string.Empty;
        public DateTime Date { get; set; }
        public decimal OpeningCash { get; set; }
        public decimal CashSales { get; set; }
        public decimal CashRefunds { get; set; }
        public decimal ExpectedCash { get; set; }
        public decimal ActualCash { get; set; }
        public decimal CashVariance { get; set; }
        public decimal VariancePercentage { get; set; }
        public string Notes { get; set; } = string.Empty;
        public bool IsReconciled { get; set; }
    }

    /// <summary>
    /// Financial report parameters
    /// </summary>
    public class FinancialReportParameters
    {
        public DateTime FromDate { get; set; }
        public DateTime ToDate { get; set; }
        public List<int> LocationIds { get; set; } = new List<int>();
        public List<int> CashierIds { get; set; } = new List<int>();
        public bool IncludeTaxAnalysis { get; set; } = false;
        public bool IncludeCreditAnalysis { get; set; } = true;
        public bool IncludeExpenseAnalysis { get; set; } = false;
        public bool IncludeComparativeAnalysis { get; set; } = true;
        public bool IncludeCashReconciliation { get; set; } = true;
        public bool IncludeProfitabilityAnalysis { get; set; } = true;
        public FinancialReportGrouping Grouping { get; set; } = FinancialReportGrouping.Daily;
        public string BaseCurrency { get; set; } = "USD";
    }

    /// <summary>
    /// Financial report grouping options
    /// </summary>
    public enum FinancialReportGrouping
    {
        Daily,
        Weekly,
        Monthly,
        Quarterly,
        Yearly
    }

    /// <summary>
    /// Financial report format
    /// </summary>
    public enum FinancialReportFormat
    {
        PDF,
        Excel,
        CSV,
        JSON
    }

    /// <summary>
    /// Financial KPI (Key Performance Indicator)
    /// </summary>
    public class FinancialKPI
    {
        public string Name { get; set; } = string.Empty;
        public decimal Value { get; set; }
        public decimal Target { get; set; }
        public decimal PreviousValue { get; set; }
        public decimal ChangeFromPrevious { get; set; }
        public decimal ChangePercentage { get; set; }
        public string Unit { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty; // Above Target, Below Target, On Target
        public string Trend { get; set; } = string.Empty; // Improving, Declining, Stable
    }
}
