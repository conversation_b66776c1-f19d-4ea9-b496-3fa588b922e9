using System;
using System.Collections.Generic;

namespace InventoryManagement.Infrastructure.Scheduling
{
    /// <summary>
    /// Defines the schedule for a recurring task
    /// </summary>
    public class TaskSchedule
    {
        /// <summary>
        /// Type of recurrence schedule
        /// </summary>
        public RecurrenceType RecurrenceType { get; set; } = RecurrenceType.Daily;
        
        /// <summary>
        /// Interval for recurrence (e.g., every 2 days)
        /// </summary>
        public int RecurrenceInterval { get; set; } = 1;
        
        /// <summary>
        /// For weekly recurrence, days of the week to run
        /// </summary>
        public List<DayOfWeek> WeeklyDays { get; set; } = new List<DayOfWeek>();
        
        /// <summary>
        /// For monthly recurrence, days of the month to run
        /// </summary>
        public List<int> MonthlyDays { get; set; } = new List<int>();
        
        /// <summary>
        /// Start time of day for execution (hh:mm)
        /// </summary>
        public TimeSpan StartTime { get; set; } = new TimeSpan(0, 0, 0);
        
        /// <summary>
        /// Date from which the schedule becomes active
        /// </summary>
        public DateTime StartDate { get; set; } = DateTime.Today;
        
        /// <summary>
        /// Date until which the schedule is active (null = indefinite)
        /// </summary>
        public DateTime? EndDate { get; set; }
        
        /// <summary>
        /// For non-recurring tasks, the specific date and time to execute
        /// </summary>
        public DateTime? OneTimeExecutionTime { get; set; }
        
        /// <summary>
        /// Calculates the next run time based on the schedule
        /// </summary>
        public DateTime CalculateNextRunTime(DateTime? lastRunTime = null)
        {
            // For one-time execution
            if (RecurrenceType == RecurrenceType.OneTime && OneTimeExecutionTime.HasValue)
            {
                return OneTimeExecutionTime.Value;
            }
            
            DateTime baseTime = lastRunTime ?? DateTime.Now;
            DateTime candidate = baseTime;
            
            switch (RecurrenceType)
            {
                case RecurrenceType.Minutely:
                    candidate = baseTime.AddMinutes(RecurrenceInterval);
                    break;
                    
                case RecurrenceType.Hourly:
                    candidate = baseTime.AddHours(RecurrenceInterval);
                    break;
                    
                case RecurrenceType.Daily:
                    candidate = baseTime.Date.Add(StartTime);
                    if (candidate <= baseTime)
                    {
                        candidate = candidate.AddDays(RecurrenceInterval);
                    }
                    break;
                    
                case RecurrenceType.Weekly:
                    candidate = GetNextWeeklyRunTime(baseTime);
                    break;
                    
                case RecurrenceType.Monthly:
                    candidate = GetNextMonthlyRunTime(baseTime);
                    break;
            }
            
            // Check if we're past the end date
            if (EndDate.HasValue && candidate > EndDate.Value)
            {
                throw new InvalidOperationException("Schedule has ended, no next run time available");
            }
            
            return candidate;
        }
        
        /// <summary>
        /// Gets the next run time for weekly recurrence
        /// </summary>
        private DateTime GetNextWeeklyRunTime(DateTime baseTime)
        {
            // Ensure we have days configured
            if (WeeklyDays.Count == 0)
            {
                WeeklyDays.Add(DayOfWeek.Monday);
            }
            
            // Start with today's date
            DateTime candidate = baseTime.Date.Add(StartTime);
            
            // Find the next day of week that matches
            int daysChecked = 0;
            while (daysChecked < 7)
            {
                // If today matches a day and we haven't passed the start time
                if (WeeklyDays.Contains(candidate.DayOfWeek) && candidate > baseTime)
                {
                    return candidate;
                }
                
                candidate = candidate.AddDays(1);
                daysChecked++;
            }
            
            // If we've checked a full week and found nothing,
            // add the recurrence interval weeks and start over
            candidate = candidate.AddDays(7 * (RecurrenceInterval - 1));
            
            // Find the next matching day
            daysChecked = 0;
            while (daysChecked < 7)
            {
                if (WeeklyDays.Contains(candidate.DayOfWeek))
                {
                    return candidate;
                }
                
                candidate = candidate.AddDays(1);
                daysChecked++;
            }
            
            // Fallback
            return baseTime.AddDays(7 * RecurrenceInterval);
        }
        
        /// <summary>
        /// Gets the next run time for monthly recurrence
        /// </summary>
        private DateTime GetNextMonthlyRunTime(DateTime baseTime)
        {
            // Ensure we have days configured
            if (MonthlyDays.Count == 0)
            {
                MonthlyDays.Add(1);
            }
            
            // Start with current month
            int year = baseTime.Year;
            int month = baseTime.Month;
            
            // Try current month first
            foreach (var day in MonthlyDays)
            {
                if (day < 1 || day > 31) continue;
                
                // Check if this day exists in the month
                int lastDayOfMonth = DateTime.DaysInMonth(year, month);
                int actualDay = Math.Min(day, lastDayOfMonth);
                
                DateTime candidate = new DateTime(year, month, actualDay).Add(StartTime);
                if (candidate > baseTime)
                {
                    return candidate;
                }
            }
            
            // Move to the next month
            month += RecurrenceInterval;
            while (month > 12)
            {
                month -= 12;
                year++;
            }
            
            // Get the first valid day
            foreach (var day in MonthlyDays)
            {
                if (day < 1 || day > 31) continue;
                
                // Check if this day exists in the month
                int lastDayOfMonth = DateTime.DaysInMonth(year, month);
                int actualDay = Math.Min(day, lastDayOfMonth);
                
                return new DateTime(year, month, actualDay).Add(StartTime);
            }
            
            // Fallback
            return new DateTime(year, month, 1).Add(StartTime);
        }
    }
    
    /// <summary>
    /// Types of recurrence schedule
    /// </summary>
    public enum RecurrenceType
    {
        /// <summary>
        /// Execute once at a specific time
        /// </summary>
        OneTime,
        
        /// <summary>
        /// Execute every X minutes
        /// </summary>
        Minutely,
        
        /// <summary>
        /// Execute every X hours
        /// </summary>
        Hourly,
        
        /// <summary>
        /// Execute every X days
        /// </summary>
        Daily,
        
        /// <summary>
        /// Execute on specific days of the week, every X weeks
        /// </summary>
        Weekly,
        
        /// <summary>
        /// Execute on specific days of the month, every X months
        /// </summary>
        Monthly
    }
}
