using System;
using System.Globalization;
using System.Windows;
using System.Windows.Data;

namespace InventoryManagement.Infrastructure.Converters
{
    /// <summary>
    /// Converts null/empty values to Visibility values for UI display
    /// </summary>
    public class NullToVisibilityConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            bool isVisible = false;
            
            if (value != null)
            {
                if (value is string str)
                {
                    isVisible = !string.IsNullOrWhiteSpace(str);
                }
                else
                {
                    isVisible = true;
                }
            }
            
            // Invert the result if parameter is specified and is "Invert"
            if (parameter is string param && param.Equals("Invert", StringComparison.OrdinalIgnoreCase))
            {
                isVisible = !isVisible;
            }
            
            return isVisible ? Visibility.Visible : Visibility.Collapsed;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
