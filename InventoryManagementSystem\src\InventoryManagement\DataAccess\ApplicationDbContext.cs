using InventoryManagement.Models;
using InventoryManagement.Services;
using InventoryManagement.Infrastructure.Auditing;
using Microsoft.EntityFrameworkCore;
using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace InventoryManagement.DataAccess
{
    public class ApplicationDbContext : DbContext
    {
        private readonly EntityChangeAuditor _entityChangeAuditor;
        
        // Current user ID for auditing - set by services before SaveChanges
        public Guid? CurrentUserId { get; set; }
        
        public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options, EntityChangeAuditor entityChangeAuditor = null) 
            : base(options)
        {
            // Enable PostgreSQL specific features
            AppContext.SetSwitch("Npgsql.EnableLegacyTimestampBehavior", true);
            
            // EntityChangeAuditor is optional to allow for testing and migrations
            _entityChangeAuditor = entityChangeAuditor;
        }
        
        /// <summary>
        /// Override SaveChanges to automatically audit entity changes
        /// </summary>
        public override int SaveChanges(bool acceptAllChangesOnSuccess)
        {
            AuditChanges();
            return base.SaveChanges(acceptAllChangesOnSuccess);
        }
        
        /// <summary>
        /// Override SaveChangesAsync to automatically audit entity changes
        /// </summary>
        public override async Task<int> SaveChangesAsync(bool acceptAllChangesOnSuccess, CancellationToken cancellationToken = default)
        {
            await AuditChangesAsync(cancellationToken);
            return await base.SaveChangesAsync(acceptAllChangesOnSuccess, cancellationToken);
        }
        
        /// <summary>
        /// Audit changes synchronously
        /// </summary>
        private void AuditChanges()
        {
            // Skip auditing if no auditor or no current user
            if (_entityChangeAuditor == null || !CurrentUserId.HasValue)
                return;
                
            // Synchronously wait for the async method (not ideal, but required for the sync SaveChanges method)
            AuditChangesAsync().GetAwaiter().GetResult();
        }
        
        /// <summary>
        /// Audit changes asynchronously
        /// </summary>
        private Task AuditChangesAsync(CancellationToken cancellationToken = default)
        {
            // Skip auditing if no auditor or no current user
            if (_entityChangeAuditor == null || !CurrentUserId.HasValue)
                return Task.CompletedTask;
                
            // Audit changes using the entity change auditor
            var auditRecords = _entityChangeAuditor.CaptureChanges(CurrentUserId.Value);
            
            // Add audit records to the AuditLogs DbSet
            if (auditRecords.Count > 0)
            {
                AuditLogs.AddRange(auditRecords.Select(record => new AuditLog
                {
                    UserId = record.UserId,
                    EntityType = record.EntityType,
                    EntityId = record.EntityId,
                    Action = record.Action,
                    Timestamp = record.Timestamp,
                    Changes = record.Changes
                }));
            }
            
            return Task.CompletedTask;
        }

        // Original entities
        public DbSet<User> Users { get; set; }
        public DbSet<Item> Items { get; set; }
        public DbSet<ItemStock> ItemStocks { get; set; }
        public DbSet<Location> Locations { get; set; }
        public DbSet<Transaction> Transactions { get; set; }
        public DbSet<TransactionDetail> TransactionDetails { get; set; }
        public DbSet<Payment> Payments { get; set; }
        public DbSet<BankAccount> BankAccounts { get; set; }
        public DbSet<ExpenseCategory> ExpenseCategories { get; set; }
        public DbSet<ItemExchange> ItemExchanges { get; set; }
        public DbSet<ItemExchangeDetail> ItemExchangeDetails { get; set; }
        public DbSet<DefectiveItem> DefectiveItems { get; set; }
        public DbSet<DefectiveItemStatusHistory> DefectiveItemStatusHistory { get; set; }
        
        // Core entities for inventory management
        public DbSet<AuditLog> AuditLogs { get; set; }
        public DbSet<Models.ReportTemplate> ReportTemplates { get; set; }
        public DbSet<Models.Notification> Notifications { get; set; }
        public DbSet<NotificationTemplate> NotificationTemplates { get; set; }
        public DbSet<Models.InventoryMetrics> InventoryMetrics { get; set; }
        public DbSet<StockCount> StockCounts { get; set; }
        public DbSet<Models.Inventory> Inventory { get; set; }
        public DbSet<Models.AppSetting> AppSettings { get; set; }
        public DbSet<Models.Vendor> Vendors { get; set; }
        public DbSet<Models.Permission> Permissions { get; set; }
        public DbSet<Models.ItemSupplier> ItemSuppliers { get; set; }
        public DbSet<Models.UserSession> UserSessions { get; set; }

        // New DbSet for offline operations
        public DbSet<OfflineOperation> OfflineOperations { get; set; }
        public DbSet<SyncSession> SyncSessions { get; set; }
        public DbSet<OfflineQueueItem> OfflineQueueItems { get; set; }
        
        // Credit and financial management
        public DbSet<CreditTransaction> CreditTransactions { get; set; }
        public DbSet<FinancialTransaction> FinancialTransactions { get; set; }
        public DbSet<Customer> Customers { get; set; }

        // Hardware configuration
        public DbSet<Models.Hardware.ScannerConfiguration> ScannerConfigurations { get; set; }
        public DbSet<Models.Hardware.PrinterConfiguration> PrinterConfigurations { get; set; }

        // Returns and refunds
        public DbSet<ReturnTransaction> ReturnTransactions { get; set; }
        public DbSet<ReturnItem> ReturnItems { get; set; }

        // Discounts
        public DbSet<Discount> Discounts { get; set; }
        public DbSet<DiscountApplication> DiscountApplications { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // Configure original entity relationships
            modelBuilder.Entity<ItemStock>()
                .HasOne(s => s.Item)
                .WithMany(i => i.StockByLocation)
                .HasForeignKey(s => s.ItemId);

            // Configure indexes for better database performance
            modelBuilder.ConfigureIndexes();
            
            modelBuilder.Entity<ItemStock>()
                .HasOne(s => s.Location)
                .WithMany(l => l.ItemStocks)
                .HasForeignKey(s => s.LocationId);

            modelBuilder.Entity<TransactionDetail>()
                .HasOne(td => td.Transaction)
                .WithMany(t => t.Details)
                .HasForeignKey(td => td.TransactionId);

            modelBuilder.Entity<Payment>()
                .HasOne(p => p.Transaction)
                .WithMany(t => t.Payments)
                .HasForeignKey(p => p.TransactionId);

            // Configure Transaction User relationships to fix foreign key errors
            modelBuilder.Entity<Transaction>()
                .HasOne(t => t.User)
                .WithMany()
                .HasForeignKey(t => t.UserId)
                .OnDelete(DeleteBehavior.Restrict);
                
            modelBuilder.Entity<Transaction>()
                .HasOne(t => t.VerifiedByUser)
                .WithMany()
                .HasForeignKey(t => t.VerifiedBy)
                .IsRequired(false) // VerifiedBy is nullable (int?)
                .OnDelete(DeleteBehavior.Restrict);
                
            // Configure relationships for ItemExchange
            modelBuilder.Entity<ItemExchangeDetail>()
                .HasOne(ed => ed.ItemExchange)
                .WithMany(e => e.ExchangeDetails)
                .HasForeignKey(ed => ed.ItemExchangeId);

            // Configure relationships for DefectiveItem
            modelBuilder.Entity<DefectiveItemStatusHistory>()
                .HasOne(h => h.DefectiveItem)
                .WithMany(d => d.StatusHistory)
                .HasForeignKey(h => h.DefectiveItemId);
                
            // Configure soft delete filter for entities that support it
            ConfigureSoftDeleteFilters(modelBuilder);
            
            // Configure InventoryMetrics to ignore dictionary properties that EF tries to treat as navigation properties
            modelBuilder.Entity<InventoryMetrics>(entity => {
                entity.Ignore(m => m.InventoryCountByCategory);
                entity.Ignore(m => m.InventoryValueByCategory);
                
                // Configure DateTimeRange as an owned entity (complex type)
                entity.OwnsOne(m => m.DateRange);
                
                // Ignore collection navigation properties that don't need to be mapped to database tables
                entity.Ignore(m => m.RecentlyAddedItems);
                entity.Ignore(m => m.RecentStockAdjustments);
            });
            
            // Configure CreditTransaction relationships
            modelBuilder.Entity<CreditTransaction>()
                .HasOne(ct => ct.Customer)
                .WithMany(c => c.CreditTransactions)
                .HasForeignKey(ct => ct.CustomerId)
                .OnDelete(DeleteBehavior.Restrict);
                
            modelBuilder.Entity<CreditTransaction>()
                .HasOne(ct => ct.Transaction)
                .WithMany()
                .HasForeignKey(ct => ct.TransactionId)
                .OnDelete(DeleteBehavior.Restrict);
                
            modelBuilder.Entity<CreditTransaction>()
                .HasOne(ct => ct.CreatedByUser)
                .WithMany()
                .HasForeignKey(ct => ct.CreatedByUserId)
                .OnDelete(DeleteBehavior.Restrict);
                
            // Configure FinancialTransaction relationships
            modelBuilder.Entity<FinancialTransaction>()
                .HasOne(ft => ft.RecordedByUser)
                .WithMany()
                .HasForeignKey(ft => ft.RecordedByUserId)
                .OnDelete(DeleteBehavior.Restrict);
                
            modelBuilder.Entity<FinancialTransaction>()
                .HasOne(ft => ft.ReconciledByUser)
                .WithMany()
                .HasForeignKey(ft => ft.ReconciledByUserId)
                .IsRequired(false)
                .OnDelete(DeleteBehavior.Restrict);
                
            modelBuilder.Entity<FinancialTransaction>()
                .HasOne(ft => ft.Customer)
                .WithMany(c => c.FinancialTransactions)
                .HasForeignKey(ft => ft.CustomerId)
                .IsRequired(false)
                .OnDelete(DeleteBehavior.Restrict);
            
            // Configure relationships for new entities
            
            // Audit Log relationships
            modelBuilder.Entity<AuditLog>()
                .HasOne(a => a.User)
                .WithMany(u => u.UserAuditLogs)
                .HasForeignKey(a => a.UserId);
                
            // Supplier and ItemSupplier relationships are commented out for offline version
            // These will be implemented when supplier management is added
            /*
            // Supplier relationships
            modelBuilder.Entity<Supplier>()
                .HasOne(s => s.CreatedByUser)
                .WithMany()
                .HasForeignKey(s => s.CreatedByUserId)
                .OnDelete(DeleteBehavior.Restrict);
                
            modelBuilder.Entity<Supplier>()
                .HasOne(s => s.LastModifiedByUser)
                .WithMany()
                .HasForeignKey(s => s.LastModifiedByUserId)
                .OnDelete(DeleteBehavior.Restrict);
                
            modelBuilder.Entity<Supplier>()
                .HasOne(s => s.DeletedByUser)
                .WithMany()
                .HasForeignKey(s => s.DeletedByUserId)
                .OnDelete(DeleteBehavior.Restrict);
                
            // ItemSupplier relationships
            modelBuilder.Entity<ItemSupplier>()
                .HasOne(i => i.Item)
                .WithMany(i => i.ItemSuppliers)
                .HasForeignKey(i => i.ItemId);
                
            modelBuilder.Entity<ItemSupplier>()
                .HasOne(i => i.Supplier)
                .WithMany(s => s.SuppliedItems)
                .HasForeignKey(i => i.SupplierId);
                
            modelBuilder.Entity<ItemSupplier>()
                .HasOne(i => i.CreatedByUser)
                .WithMany()
                .HasForeignKey(i => i.CreatedByUserId)
                .OnDelete(DeleteBehavior.Restrict);
            */
                
            // Permission, UserPreference, and ErrorLog relationships are commented out for offline version
            /*
            // Permission relationships
            modelBuilder.Entity<RolePermission>()
                .HasOne(rp => rp.Permission)
                .WithMany(p => p.RolePermissions)
                .HasForeignKey(rp => rp.PermissionId);
                
            modelBuilder.Entity<RolePermission>()
                .HasOne(rp => rp.CreatedByUser)
                .WithMany()
                .HasForeignKey(rp => rp.CreatedByUserId)
                .OnDelete(DeleteBehavior.Restrict);
                
            modelBuilder.Entity<UserPermission>()
                .HasOne(up => up.User)
                .WithMany()
                .HasForeignKey(up => up.UserId);
                
            modelBuilder.Entity<UserPermission>()
                .HasOne(up => up.Permission)
                .WithMany(p => p.UserPermissions)
                .HasForeignKey(up => up.PermissionId);
                
            modelBuilder.Entity<UserPermission>()
                .HasOne(up => up.CreatedByUser)
                .WithMany()
                .HasForeignKey(up => up.CreatedByUserId)
                .OnDelete(DeleteBehavior.Restrict);
                
            // User Preference relationships
            modelBuilder.Entity<UserPreference>()
                .HasOne(up => up.User)
                .WithMany()
                .HasForeignKey(up => up.UserId);
                
            // Error Log relationship
            modelBuilder.Entity<ErrorLog>()
                .HasOne(e => e.User)
                .WithMany()
                .HasForeignKey(e => e.UserId)
                .OnDelete(DeleteBehavior.Restrict);
            */
                
            // Item relationships
            modelBuilder.Entity<Item>()
                .HasOne(i => i.CreatedByUser)
                .WithMany()
                .HasForeignKey(i => i.CreatedByUserId)
                .OnDelete(DeleteBehavior.Restrict);
                
            modelBuilder.Entity<Item>()
                .HasOne(i => i.LastModifiedByUser)
                .WithMany()
                .HasForeignKey(i => i.LastModifiedByUserId)
                .OnDelete(DeleteBehavior.Restrict);
                
            modelBuilder.Entity<Item>()
                .HasOne(i => i.DeletedByUser)
                .WithMany()
                .HasForeignKey(i => i.DeletedByUserId)
                .OnDelete(DeleteBehavior.Restrict);
                
            // Notification relationships
            modelBuilder.Entity<Notification>()
                .HasOne(n => n.User)
                .WithMany()
                .HasForeignKey(n => n.UserId)
                .OnDelete(DeleteBehavior.Cascade);
                
            modelBuilder.Entity<NotificationTemplate>()
                .HasOne(nt => nt.CreatedByUser)
                .WithMany()
                .HasForeignKey(nt => nt.CreatedByUserId)
                .OnDelete(DeleteBehavior.Restrict);
                
            modelBuilder.Entity<NotificationTemplate>()
                .HasOne(nt => nt.LastUpdatedByUser)
                .WithMany()
                .HasForeignKey(nt => nt.LastUpdatedByUserId)
                .OnDelete(DeleteBehavior.Restrict);

            // Configure OfflineOperation entity
            modelBuilder.Entity<OfflineOperation>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.EntityType).IsRequired().HasMaxLength(100);
                entity.Property(e => e.EntityId).IsRequired();
                entity.Property(e => e.EntityData).IsRequired();
                entity.Property(e => e.Timestamp).IsRequired();
                entity.Property(e => e.TerminalId).IsRequired().HasMaxLength(50);
                
                // Useful indexes for common queries
                entity.HasIndex(e => e.Status);
                entity.HasIndex(e => e.Timestamp);
                entity.HasIndex(e => new { e.Status, e.Timestamp });
                entity.HasIndex(e => new { e.EntityType, e.Status });
                entity.HasIndex(e => e.TerminalId);
            });

            // Configure default data
            SeedDefaultData(modelBuilder);
        }
        
        /// <summary>
        /// Configures global query filters for soft delete functionality
        /// </summary>
        /// <param name="modelBuilder">The model builder to configure</param>
        private void ConfigureSoftDeleteFilters(ModelBuilder modelBuilder)
        {
            // Item entity
            modelBuilder.Entity<Item>().HasQueryFilter(i => !i.IsDeleted);
            
            // User entity (if marked as soft delete)
            modelBuilder.Entity<User>().HasQueryFilter(u => u.IsActive);
            
            // Location entity
            modelBuilder.Entity<Location>().HasQueryFilter(l => l.IsActive);
            
            // Note: The following entities don't have IsDeleted property in the offline version
            // These will be added when full soft-delete functionality is implemented
            /*
            // Other entities that support soft delete
            modelBuilder.Entity<Transaction>().HasQueryFilter(t => !t.IsDeleted);
            modelBuilder.Entity<TransactionDetail>().HasQueryFilter(td => !td.IsDeleted);
            modelBuilder.Entity<ItemExchange>().HasQueryFilter(ie => !ie.IsDeleted);
            modelBuilder.Entity<DefectiveItem>().HasQueryFilter(di => !di.IsDeleted);
            */
        }

        private void SeedDefaultData(ModelBuilder modelBuilder)
        {
            // Seed default admin user if no users exist
            modelBuilder.Entity<User>().HasData(
                new User
                {
                    Id = 1,
                    Username = "admin",
                    PasswordHash = "AQAAAAEAACcQAAAAEO8xPCLpf3BK3flkTnQtZlPzvbkPYjaTbY5ncuiGe5QkCNHYVmQXNJEVTUKyYnWWpA==", // default password: Admin123!
                    FullName = "System Administrator",
                    Role = UserRole.Admin,
                    IsActive = true,
                    CreatedDate = new DateTime(2023, 1, 1),
                    Email = "<EMAIL>",
                    PhoneNumber = "+1234567890"
                }
            );
            
            // Notification templates seeding is commented out for the offline version
            /*
            // Seed default notification templates
            var defaultTemplates = DefaultNotificationTemplates.GetDefaultTemplates(1); // Created by admin (ID=1)
            
            for (int i = 0; i < defaultTemplates.Count; i++)
            {
                var template = defaultTemplates[i];
                template.Id = i + 1; // Assign sequential IDs starting from 1
                
                modelBuilder.Entity<NotificationTemplate>().HasData(template);
            }
            */
            
            // Default user preferences are now handled in the application layer
            // rather than being seeded in the database
            
            // Default locations (3 basements and 1 showroom)
            modelBuilder.Entity<Location>().HasData(
                new Location
                {
                    Id = 1,
                    Name = "Basement A",
                    Description = "Main basement storage",
                    Type = LocationType.Basement,
                    IsActive = true,
                    CreatedDate = DateTime.Now
                },
                new Location
                {
                    Id = 2,
                    Name = "Basement B",
                    Description = "Secondary basement storage",
                    Type = LocationType.Basement,
                    IsActive = true,
                    CreatedDate = DateTime.Now
                },
                new Location
                {
                    Id = 3,
                    Name = "Basement C",
                    Description = "Auxiliary basement storage",
                    Type = LocationType.Basement,
                    IsActive = true,
                    CreatedDate = DateTime.Now
                },
                new Location
                {
                    Id = 4,
                    Name = "Main Showroom",
                    Description = "Customer-facing display area",
                    Type = LocationType.Showroom,
                    IsActive = true,
                    CreatedDate = DateTime.Now
                }
            );

            // Default expense categories
            var categories = ExpenseCategory.GetDefaultCategories();
            for (int i = 0; i < categories.Count; i++)
            {
                categories[i].Id = i + 1;
                categories[i].IsActive = true;
                categories[i].CreatedDate = DateTime.Now;
            }
            modelBuilder.Entity<ExpenseCategory>().HasData(categories);
        }
    }
}
