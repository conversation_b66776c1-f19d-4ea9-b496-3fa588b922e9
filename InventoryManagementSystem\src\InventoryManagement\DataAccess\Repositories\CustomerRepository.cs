using InventoryManagement.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace InventoryManagement.DataAccess.Repositories
{
    /// <summary>
    /// Repository for customer data operations
    /// </summary>
    public class CustomerRepository : ICustomerRepository
    {
        private readonly ApplicationDbContext _context;
        private readonly ILogger<CustomerRepository> _logger;

        public CustomerRepository(ApplicationDbContext context, ILogger<CustomerRepository> logger)
        {
            _context = context;
            _logger = logger;
        }

        public async Task<Customer?> GetByIdAsync(int id)
        {
            try
            {
                return await _context.Customers
                    .Include(c => c.Transactions)
                    .Include(c => c.CreditTransactions)
                    .Include(c => c.FinancialTransactions)
                    .FirstOrDefaultAsync(c => c.Id == id && c.IsActive);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting customer by ID {CustomerId}", id);
                throw;
            }
        }

        public async Task<Customer?> GetByCustomerNumberAsync(string customerNumber)
        {
            try
            {
                return await _context.Customers
                    .Include(c => c.Transactions)
                    .Include(c => c.CreditTransactions)
                    .Include(c => c.FinancialTransactions)
                    .FirstOrDefaultAsync(c => c.CustomerNumber == customerNumber && c.IsActive);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting customer by number {CustomerNumber}", customerNumber);
                throw;
            }
        }

        public async Task<Customer?> GetByPhoneAsync(string phoneNumber)
        {
            try
            {
                return await _context.Customers
                    .FirstOrDefaultAsync(c => c.PhoneNumber == phoneNumber && c.IsActive);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting customer by phone {PhoneNumber}", phoneNumber);
                throw;
            }
        }

        public async Task<Customer?> GetByEmailAsync(string email)
        {
            try
            {
                return await _context.Customers
                    .FirstOrDefaultAsync(c => c.Email == email && c.IsActive);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting customer by email {Email}", email);
                throw;
            }
        }

        public async Task<List<Customer>> GetAllAsync()
        {
            try
            {
                return await _context.Customers
                    .Where(c => c.IsActive)
                    .OrderBy(c => c.Name)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting all customers");
                throw;
            }
        }

        public async Task<List<Customer>> SearchAsync(string searchTerm)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(searchTerm))
                    return await GetAllAsync();

                searchTerm = searchTerm.ToLower().Trim();

                return await _context.Customers
                    .Where(c => c.IsActive && (
                        c.Name.ToLower().Contains(searchTerm) ||
                        c.CustomerNumber.ToLower().Contains(searchTerm) ||
                        c.PhoneNumber.Contains(searchTerm) ||
                        c.Email.ToLower().Contains(searchTerm)
                    ))
                    .OrderBy(c => c.Name)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error searching customers with term {SearchTerm}", searchTerm);
                throw;
            }
        }

        public async Task<List<Customer>> GetByTypeAsync(string customerType)
        {
            try
            {
                return await _context.Customers
                    .Where(c => c.IsActive && c.CustomerType == customerType)
                    .OrderBy(c => c.Name)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting customers by type {CustomerType}", customerType);
                throw;
            }
        }

        public async Task<List<Customer>> GetWithOverduePaymentsAsync()
        {
            try
            {
                return await _context.Customers
                    .Where(c => c.IsActive && c.HasOverduePayments)
                    .Include(c => c.CreditTransactions)
                    .OrderBy(c => c.Name)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting customers with overdue payments");
                throw;
            }
        }

        public async Task<List<Customer>> GetTopCustomersAsync(int count = 10)
        {
            try
            {
                return await _context.Customers
                    .Where(c => c.IsActive)
                    .OrderByDescending(c => c.TotalPurchases)
                    .Take(count)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting top {Count} customers", count);
                throw;
            }
        }

        public async Task<Customer> CreateAsync(Customer customer)
        {
            try
            {
                // Generate customer number if not provided
                if (string.IsNullOrEmpty(customer.CustomerNumber))
                {
                    customer.CustomerNumber = await GenerateCustomerNumberAsync();
                }

                customer.CreatedDate = DateTime.Now;
                _context.Customers.Add(customer);
                await _context.SaveChangesAsync();

                _logger.LogInformation("Created new customer {CustomerName} with ID {CustomerId}", customer.Name, customer.Id);
                return customer;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating customer {CustomerName}", customer.Name);
                throw;
            }
        }

        public async Task<Customer> UpdateAsync(Customer customer)
        {
            try
            {
                _context.Customers.Update(customer);
                await _context.SaveChangesAsync();

                _logger.LogInformation("Updated customer {CustomerName} with ID {CustomerId}", customer.Name, customer.Id);
                return customer;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating customer {CustomerId}", customer.Id);
                throw;
            }
        }

        public async Task<bool> DeleteAsync(int id)
        {
            try
            {
                var customer = await _context.Customers.FindAsync(id);
                if (customer == null)
                    return false;

                // Soft delete
                customer.IsActive = false;
                await _context.SaveChangesAsync();

                _logger.LogInformation("Deleted customer {CustomerName} with ID {CustomerId}", customer.Name, customer.Id);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting customer {CustomerId}", id);
                throw;
            }
        }

        public async Task<bool> ExistsAsync(int id)
        {
            try
            {
                return await _context.Customers.AnyAsync(c => c.Id == id && c.IsActive);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking if customer exists {CustomerId}", id);
                throw;
            }
        }

        public async Task<bool> CustomerNumberExistsAsync(string customerNumber)
        {
            try
            {
                return await _context.Customers.AnyAsync(c => c.CustomerNumber == customerNumber && c.IsActive);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking if customer number exists {CustomerNumber}", customerNumber);
                throw;
            }
        }

        public async Task<List<Transaction>> GetCustomerTransactionsAsync(int customerId, DateTime? fromDate = null, DateTime? toDate = null)
        {
            try
            {
                var query = _context.Transactions
                    .Where(t => t.CustomerId == customerId)
                    .Include(t => t.Items);

                if (fromDate.HasValue)
                    query = query.Where(t => t.TransactionDate >= fromDate.Value);

                if (toDate.HasValue)
                    query = query.Where(t => t.TransactionDate <= toDate.Value);

                return await query
                    .OrderByDescending(t => t.TransactionDate)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting transactions for customer {CustomerId}", customerId);
                throw;
            }
        }

        public async Task<decimal> GetCustomerTotalPurchasesAsync(int customerId, DateTime? fromDate = null, DateTime? toDate = null)
        {
            try
            {
                var query = _context.Transactions
                    .Where(t => t.CustomerId == customerId && t.Type == TransactionType.Sale);

                if (fromDate.HasValue)
                    query = query.Where(t => t.TransactionDate >= fromDate.Value);

                if (toDate.HasValue)
                    query = query.Where(t => t.TransactionDate <= toDate.Value);

                return await query.SumAsync(t => t.Total);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting total purchases for customer {CustomerId}", customerId);
                throw;
            }
        }

        public async Task UpdateCustomerTotalPurchasesAsync(int customerId)
        {
            try
            {
                var customer = await _context.Customers.FindAsync(customerId);
                if (customer == null) return;

                var totalPurchases = await GetCustomerTotalPurchasesAsync(customerId);
                customer.TotalPurchases = totalPurchases;

                await _context.SaveChangesAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating total purchases for customer {CustomerId}", customerId);
                throw;
            }
        }

        private async Task<string> GenerateCustomerNumberAsync()
        {
            try
            {
                var lastCustomer = await _context.Customers
                    .Where(c => c.CustomerNumber.StartsWith("CUST"))
                    .OrderByDescending(c => c.Id)
                    .FirstOrDefaultAsync();

                int nextNumber = 1;
                if (lastCustomer != null && !string.IsNullOrEmpty(lastCustomer.CustomerNumber))
                {
                    var numberPart = lastCustomer.CustomerNumber.Substring(4);
                    if (int.TryParse(numberPart, out int lastNumber))
                    {
                        nextNumber = lastNumber + 1;
                    }
                }

                return $"CUST{nextNumber:D6}"; // CUST000001, CUST000002, etc.
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating customer number");
                return $"CUST{DateTime.Now.Ticks % 1000000:D6}";
            }
        }
    }
}
