using System.Windows.Controls;
using InventoryManagement.ViewModels;

namespace InventoryManagement.Views
{
    /// <summary>
    /// Interaction logic for CustomerManagementView.xaml
    /// </summary>
    public partial class CustomerManagementView : UserControl
    {
        public CustomerManagementView()
        {
            InitializeComponent();
        }

        public CustomerManagementView(CustomerManagementViewModel viewModel) : this()
        {
            DataContext = viewModel;
        }
    }
}
