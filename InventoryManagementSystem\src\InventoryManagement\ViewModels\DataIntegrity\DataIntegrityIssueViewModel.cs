using System;
using InventoryManagement.Services.Validation;

namespace InventoryManagement.ViewModels.DataIntegrity
{
    /// <summary>
    /// ViewModel for displaying a data integrity issue
    /// </summary>
    public class DataIntegrityIssueViewModel : ViewModelBase
    {
        private readonly DataIntegrityIssue _issue;
        private bool _isResolved;
        
        /// <summary>
        /// Type of entity with the issue
        /// </summary>
        public string EntityType => _issue.EntityType;
        
        /// <summary>
        /// Property with the issue
        /// </summary>
        public string PropertyName => _issue.PropertyName;
        
        /// <summary>
        /// Entity identifier
        /// </summary>
        public string EntityId => _issue.EntityId;
        
        /// <summary>
        /// Rule that found the issue
        /// </summary>
        public string RuleName => _issue.RuleName;
        
        /// <summary>
        /// Issue severity
        /// </summary>
        public IssueSeverity Severity => _issue.Severity;
        
        /// <summary>
        /// String representation of severity
        /// </summary>
        public string SeverityText => _issue.Severity.ToString();
        
        /// <summary>
        /// Message describing the issue
        /// </summary>
        public string Message => _issue.Message;
        
        /// <summary>
        /// Technical details about the issue
        /// </summary>
        public string Details => _issue.Details;
        
        /// <summary>
        /// When the issue was detected
        /// </summary>
        public DateTime DetectedTime => _issue.DetectedTime;
        
        /// <summary>
        /// Formatted detection time
        /// </summary>
        public string DetectedTimeFormatted => _issue.DetectedTime.ToString("g");
        
        /// <summary>
        /// Suggestion for resolving the issue
        /// </summary>
        public string ResolutionSuggestion => _issue.ResolutionSuggestion;
        
        /// <summary>
        /// SQL query that can help fix the issue
        /// </summary>
        public string RemediationQuery => _issue.RemediationQuery;
        
        /// <summary>
        /// Whether this issue can be automatically fixed
        /// </summary>
        public bool CanAutoFix => _issue.CanAutoFix;
        
        /// <summary>
        /// Whether this issue has been resolved
        /// </summary>
        public bool IsResolved
        {
            get => _isResolved || _issue.IsResolved;
            set
            {
                if (_isResolved != value)
                {
                    _isResolved = value;
                    _issue.IsResolved = value;
                    OnPropertyChanged(nameof(IsResolved));
                }
            }
        }
        
        /// <summary>
        /// CSS class for the severity indicator
        /// </summary>
        public string SeverityCssClass
        {
            get
            {
                return Severity switch
                {
                    IssueSeverity.Critical => "severity-critical",
                    IssueSeverity.Error => "severity-error",
                    IssueSeverity.Warning => "severity-warning",
                    IssueSeverity.Info => "severity-info",
                    _ => "severity-info"
                };
            }
        }
        
        /// <summary>
        /// Creates a new DataIntegrityIssueViewModel
        /// </summary>
        public DataIntegrityIssueViewModel(DataIntegrityIssue issue)
        {
            _issue = issue ?? throw new ArgumentNullException(nameof(issue));
            _isResolved = issue.IsResolved;
        }
    }
}
