using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using InventoryManagement.Models.Reports;
using Microsoft.Extensions.Logging;

namespace InventoryManagement.Services.Export
{
    /// <summary>
    /// Service for exporting data to various file formats
    /// </summary>
    public class DataExportService : IDataExportService
    {
        private readonly ILogger<DataExportService> _logger;
        
        public DataExportService(ILogger<DataExportService> logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }
        
        /// <summary>
        /// Exports data to a file in the specified format
        /// </summary>
        public async Task<string> ExportDataAsync(
            DataTable data, 
            ExportFormat format, 
            string filePath = null,
            ExportOptions options = null)
        {
            if (data == null)
            {
                throw new ArgumentNullException(nameof(data));
            }
            
            options ??= new ExportOptions();
            
            // Generate file path if not provided
            if (string.IsNullOrEmpty(filePath))
            {
                string extension = GetFileExtension(format);
                string timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
                string fileName = $"Export_{timestamp}{extension}";
                filePath = Path.Combine(
                    options.ExportDirectory ?? Path.Combine(AppContext.BaseDirectory, "Exports"),
                    fileName);
            }
            
            // Ensure directory exists
            Directory.CreateDirectory(Path.GetDirectoryName(filePath));
            
            _logger.LogInformation("Exporting data to {Format} file: {FilePath}", format, filePath);
            
            try
            {
                switch (format)
                {
                    case ExportFormat.Excel:
                        await ExportToExcelAsync(data, filePath, options);
                        break;
                    case ExportFormat.Csv:
                        await ExportToCsvAsync(data, filePath, options);
                        break;
                    case ExportFormat.Pdf:
                        await ExportToPdfAsync(data, filePath, options);
                        break;
                    case ExportFormat.Text:
                        await ExportToTextAsync(data, filePath, options);
                        break;
                    default:
                        throw new ArgumentException($"Unsupported export format: {format}");
                }
                
                _logger.LogInformation("Successfully exported data to {FilePath}", filePath);
                return filePath;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error exporting data to {Format}", format);
                throw;
            }
        }
        
        /// <summary>
        /// Exports a JSON serializable object to a JSON file
        /// </summary>
        public async Task<string> ExportToJsonAsync<T>(T data, string filePath = null, ExportOptions options = null)
        {
            if (data == null)
            {
                throw new ArgumentNullException(nameof(data));
            }
            
            options ??= new ExportOptions();
            
            // Generate file path if not provided
            if (string.IsNullOrEmpty(filePath))
            {
                string timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
                string fileName = $"Export_{timestamp}.json";
                filePath = Path.Combine(
                    options.ExportDirectory ?? Path.Combine(AppContext.BaseDirectory, "Exports"),
                    fileName);
            }
            
            // Ensure directory exists
            Directory.CreateDirectory(Path.GetDirectoryName(filePath));
            
            _logger.LogInformation("Exporting data to JSON file: {FilePath}", filePath);
            
            try
            {
                var jsonOptions = new JsonSerializerOptions
                {
                    WriteIndented = options.PrettyPrint
                };
                
                string json = JsonSerializer.Serialize(data, jsonOptions);
                await File.WriteAllTextAsync(filePath, json);
                
                _logger.LogInformation("Successfully exported data to JSON file: {FilePath}", filePath);
                return filePath;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error exporting data to JSON");
                throw;
            }
        }
        
        /// <summary>
        /// Gets formatted value for export based on column formatting
        /// </summary>
        private string FormatValue(object value, ColumnFormatting formatting = null)
        {
            if (value == null || value == DBNull.Value)
            {
                return string.Empty;
            }
            
            if (formatting != null && !string.IsNullOrEmpty(formatting.FormatString))
            {
                // Handle numeric formatting
                if (value is int or long or float or double or decimal)
                {
                    if (formatting.IsCurrency)
                    {
                        return string.Format("{0:C}", value);
                    }
                    return string.Format("{0:" + formatting.FormatString + "}", value);
                }
                
                // Handle date formatting
                if (value is DateTime dateValue)
                {
                    return dateValue.ToString(formatting.FormatString);
                }
            }
            
            return value.ToString();
        }
        
        /// <summary>
        /// Exports data to CSV format
        /// </summary>
        private async Task ExportToCsvAsync(DataTable data, string filePath, ExportOptions options)
        {
            using var writer = new StreamWriter(filePath, false, Encoding.UTF8);
            
            // Write header row
            if (options.IncludeHeaders)
            {
                var headers = data.Columns.Cast<DataColumn>()
                    .Select(column => EscapeCsvField(column.ColumnName))
                    .ToArray();
                
                await writer.WriteLineAsync(string.Join(",", headers));
            }
            
            // Write data rows
            foreach (DataRow row in data.Rows)
            {
                var fields = data.Columns.Cast<DataColumn>()
                    .Select(column => EscapeCsvField(FormatValue(row[column])))
                    .ToArray();
                
                await writer.WriteLineAsync(string.Join(",", fields));
            }
        }
        
        /// <summary>
        /// Escapes a field value for CSV format
        /// </summary>
        private string EscapeCsvField(string field)
        {
            if (string.IsNullOrEmpty(field))
            {
                return string.Empty;
            }
            
            bool containsSpecial = field.Contains(',') || field.Contains('\"') || field.Contains('\n');
            
            if (containsSpecial)
            {
                return $"\"{field.Replace("\"", "\"\"")}\"";
            }
            
            return field;
        }
        
        /// <summary>
        /// Exports data to plain text format
        /// </summary>
        private async Task ExportToTextAsync(DataTable data, string filePath, ExportOptions options)
        {
            using var writer = new StreamWriter(filePath, false, Encoding.UTF8);
            
            // Calculate column widths
            var columnWidths = new Dictionary<string, int>();
            foreach (DataColumn column in data.Columns)
            {
                // Start with column name length
                int maxWidth = column.ColumnName.Length;
                
                // Check data widths
                foreach (DataRow row in data.Rows)
                {
                    string value = FormatValue(row[column]);
                    maxWidth = Math.Max(maxWidth, value.Length);
                }
                
                // Cap width
                maxWidth = Math.Min(maxWidth, 50);
                columnWidths[column.ColumnName] = maxWidth;
            }
            
            // Write header row
            if (options.IncludeHeaders)
            {
                var headerLine = string.Join(" | ", data.Columns.Cast<DataColumn>()
                    .Select(column => column.ColumnName.PadRight(columnWidths[column.ColumnName])));
                
                await writer.WriteLineAsync(headerLine);
                
                // Write separator
                var separator = string.Join("-+-", data.Columns.Cast<DataColumn>()
                    .Select(column => new string('-', columnWidths[column.ColumnName])));
                
                await writer.WriteLineAsync(separator);
            }
            
            // Write data rows
            foreach (DataRow row in data.Rows)
            {
                var rowLine = string.Join(" | ", data.Columns.Cast<DataColumn>()
                    .Select(column => FormatValue(row[column]).PadRight(columnWidths[column.ColumnName])));
                
                await writer.WriteLineAsync(rowLine);
            }
        }
        
        /// <summary>
        /// Exports data to Excel format (requires EPPlus or other Excel libraries)
        /// </summary>
        private async Task ExportToExcelAsync(DataTable data, string filePath, ExportOptions options)
        {
            // Note: In a real implementation, this would use a library like EPPlus
            // For this sample, we'll create a simple HTML table that Excel can open
            using var writer = new StreamWriter(filePath + ".html", false, Encoding.UTF8);
            
            await writer.WriteLineAsync("<!DOCTYPE html>");
            await writer.WriteLineAsync("<html>");
            await writer.WriteLineAsync("<head>");
            await writer.WriteLineAsync("<meta charset=\"UTF-8\">");
            await writer.WriteLineAsync("<title>Excel Export</title>");
            await writer.WriteLineAsync("</head>");
            await writer.WriteLineAsync("<body>");
            await writer.WriteLineAsync("<table border=\"1\">");
            
            // Write header row
            if (options.IncludeHeaders)
            {
                await writer.WriteLineAsync("<thead>");
                await writer.WriteLineAsync("<tr>");
                
                foreach (DataColumn column in data.Columns)
                {
                    await writer.WriteLineAsync($"<th>{column.ColumnName}</th>");
                }
                
                await writer.WriteLineAsync("</tr>");
                await writer.WriteLineAsync("</thead>");
            }
            
            // Write data rows
            await writer.WriteLineAsync("<tbody>");
            
            foreach (DataRow row in data.Rows)
            {
                await writer.WriteLineAsync("<tr>");
                
                foreach (DataColumn column in data.Columns)
                {
                    string value = FormatValue(row[column]);
                    await writer.WriteLineAsync($"<td>{value}</td>");
                }
                
                await writer.WriteLineAsync("</tr>");
            }
            
            await writer.WriteLineAsync("</tbody>");
            await writer.WriteLineAsync("</table>");
            await writer.WriteLineAsync("</body>");
            await writer.WriteLineAsync("</html>");
            
            _logger.LogWarning("Excel export is simulated as HTML. Please install EPPlus or another Excel library for native Excel support.");
            
            // Note: In a real application, we would return the actual Excel file path without the .html extension
            // For now, we're returning the HTML file path as a placeholder
        }
        
        /// <summary>
        /// Exports data to PDF format (requires a PDF library)
        /// </summary>
        private async Task ExportToPdfAsync(DataTable data, string filePath, ExportOptions options)
        {
            // Note: In a real implementation, this would use a library like iTextSharp or similar
            // For this sample, we'll create a simple HTML file as a placeholder
            using var writer = new StreamWriter(filePath + ".html", false, Encoding.UTF8);
            
            await writer.WriteLineAsync("<!DOCTYPE html>");
            await writer.WriteLineAsync("<html>");
            await writer.WriteLineAsync("<head>");
            await writer.WriteLineAsync("<meta charset=\"UTF-8\">");
            await writer.WriteLineAsync("<title>PDF Export</title>");
            await writer.WriteLineAsync("<style>body { font-family: Arial, sans-serif; }</style>");
            await writer.WriteLineAsync("</head>");
            await writer.WriteLineAsync("<body>");
            
            if (!string.IsNullOrEmpty(options.ReportTitle))
            {
                await writer.WriteLineAsync($"<h1>{options.ReportTitle}</h1>");
            }
            
            await writer.WriteLineAsync("<table border=\"1\" cellpadding=\"5\" style=\"border-collapse: collapse;\">");
            
            // Write header row
            if (options.IncludeHeaders)
            {
                await writer.WriteLineAsync("<thead>");
                await writer.WriteLineAsync("<tr style=\"background-color: #f2f2f2;\">");
                
                foreach (DataColumn column in data.Columns)
                {
                    await writer.WriteLineAsync($"<th>{column.ColumnName}</th>");
                }
                
                await writer.WriteLineAsync("</tr>");
                await writer.WriteLineAsync("</thead>");
            }
            
            // Write data rows
            await writer.WriteLineAsync("<tbody>");
            
            foreach (DataRow row in data.Rows)
            {
                await writer.WriteLineAsync("<tr>");
                
                foreach (DataColumn column in data.Columns)
                {
                    string value = FormatValue(row[column]);
                    await writer.WriteLineAsync($"<td>{value}</td>");
                }
                
                await writer.WriteLineAsync("</tr>");
            }
            
            await writer.WriteLineAsync("</tbody>");
            await writer.WriteLineAsync("</table>");
            
            if (!string.IsNullOrEmpty(options.ReportFooter))
            {
                await writer.WriteLineAsync($"<p>{options.ReportFooter}</p>");
            }
            
            await writer.WriteLineAsync("</body>");
            await writer.WriteLineAsync("</html>");
            
            _logger.LogWarning("PDF export is simulated as HTML. Please install iTextSharp or another PDF library for native PDF support.");
            
            // Note: In a real application, we would return the actual PDF file path without the .html extension
            // For now, we're returning the HTML file path as a placeholder
        }
        
        /// <summary>
        /// Gets the appropriate file extension for the export format
        /// </summary>
        private string GetFileExtension(ExportFormat format)
        {
            return format switch
            {
                ExportFormat.Excel => ".xlsx",
                ExportFormat.Pdf => ".pdf",
                ExportFormat.Csv => ".csv",
                ExportFormat.Text => ".txt",
                _ => ".dat"
            };
        }
    }
}
