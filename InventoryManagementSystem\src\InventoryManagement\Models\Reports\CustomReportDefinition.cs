using System;
using System.Collections.Generic;

namespace InventoryManagement.Models.Reports
{
    /// <summary>
    /// Defines a custom user-defined report
    /// </summary>
    public class CustomReportDefinition
    {
        /// <summary>
        /// Unique identifier for the report
        /// </summary>
        public Guid Id { get; set; } = Guid.NewGuid();
        
        /// <summary>
        /// Name of the report
        /// </summary>
        public string Name { get; set; }
        
        /// <summary>
        /// Description of what the report shows
        /// </summary>
        public string Description { get; set; }
        
        /// <summary>
        /// Category for grouping reports
        /// </summary>
        public string Category { get; set; }
        
        /// <summary>
        /// SQL query to generate the report data
        /// </summary>
        public string SqlQuery { get; set; }
        
        /// <summary>
        /// List of parameters that can be provided for the report
        /// </summary>
        public List<ReportParameter> Parameters { get; set; } = new List<ReportParameter>();
        
        /// <summary>
        /// User who created the report
        /// </summary>
        public string CreatedBy { get; set; }
        
        /// <summary>
        /// When the report was created
        /// </summary>
        public DateTime CreatedDate { get; set; } = DateTime.Now;
        
        /// <summary>
        /// User who last modified the report
        /// </summary>
        public string ModifiedBy { get; set; }
        
        /// <summary>
        /// When the report was last modified
        /// </summary>
        public DateTime ModifiedDate { get; set; } = DateTime.Now;
        
        /// <summary>
        /// Default formatting options for columns
        /// </summary>
        public Dictionary<string, ColumnFormatting> ColumnFormats { get; set; } = new Dictionary<string, ColumnFormatting>();
        
        /// <summary>
        /// Whether this report is a system report (cannot be deleted)
        /// </summary>
        public bool IsSystemReport { get; set; }
        
        /// <summary>
        /// Default columns to display in the report
        /// </summary>
        public List<string> DefaultDisplayColumns { get; set; } = new List<string>();
        
        /// <summary>
        /// Default sort column
        /// </summary>
        public string DefaultSortColumn { get; set; }
        
        /// <summary>
        /// Whether to sort ascending by default
        /// </summary>
        public bool DefaultSortAscending { get; set; } = true;
    }
}
