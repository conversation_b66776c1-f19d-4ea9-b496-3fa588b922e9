using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using InventoryManagement.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace InventoryManagement.Services.Validation.Rules
{
    /// <summary>
    /// Rule to check for orphaned records in the database
    /// </summary>
    public class OrphanedRecordsRule : IDataIntegrityRule
    {
        private readonly AppDbContext _dbContext;
        private readonly ILogger<OrphanedRecordsRule> _logger;
        
        public string Name => "Orphaned Records Check";
        
        public string Description => "Verifies that there are no orphaned records with invalid foreign keys";
        
        public string EntityType => "Database";
        
        public OrphanedRecordsRule(
            AppDbContext dbContext,
            ILogger<OrphanedRecordsRule> logger)
        {
            _dbContext = dbContext ?? throw new ArgumentNullException(nameof(dbContext));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }
        
        /// <summary>
        /// Validates that there are no orphaned records with invalid foreign keys
        /// </summary>
        public async Task<List<DataIntegrityIssue>> ValidateAsync()
        {
            _logger.LogInformation("Running {RuleName}", Name);
            var issues = new List<DataIntegrityIssue>();
            
            try
            {
                // Check for transactions with invalid item references
                var orphanedTransactions = await _dbContext.Transactions
                    .Where(t => !_dbContext.Items.Any(i => i.Id == t.ItemId))
                    .ToListAsync();
                
                foreach (var transaction in orphanedTransactions)
                {
                    issues.Add(new DataIntegrityIssue
                    {
                        EntityType = "Transaction",
                        EntityId = transaction.Id.ToString(),
                        PropertyName = "ItemId",
                        RuleName = Name,
                        Severity = IssueSeverity.Error,
                        Message = $"Transaction references a non-existent item",
                        Details = $"Transaction ID: {transaction.Id}, Invalid ItemId: {transaction.ItemId}, " +
                                  $"Date: {transaction.TransactionDate}, Quantity: {transaction.Quantity}",
                        ResolutionSuggestion = "Either restore the missing item or mark the transaction as invalid.",
                        RemediationQuery = $"UPDATE Transactions SET IsValid = false, " +
                                           $"Notes = CONCAT(Notes, ' - Invalid item reference detected during validation') " +
                                           $"WHERE Id = '{transaction.Id}'",
                        CanAutoFix = true
                    });
                }
                
                _logger.LogInformation("Found {Count} orphaned transactions", orphanedTransactions.Count);
                
                // Check for inventory reconciliation records with invalid item references
                var orphanedReconciliations = await _dbContext.InventoryReconciliations
                    .Where(r => !_dbContext.Items.Any(i => i.Id == r.ItemId))
                    .ToListAsync();
                
                foreach (var reconciliation in orphanedReconciliations)
                {
                    issues.Add(new DataIntegrityIssue
                    {
                        EntityType = "InventoryReconciliation",
                        EntityId = reconciliation.Id.ToString(),
                        PropertyName = "ItemId",
                        RuleName = Name,
                        Severity = IssueSeverity.Error,
                        Message = $"Inventory reconciliation references a non-existent item",
                        Details = $"Reconciliation ID: {reconciliation.Id}, Invalid ItemId: {reconciliation.ItemId}, " +
                                  $"Date: {reconciliation.ReconciliationDate}",
                        ResolutionSuggestion = "Either restore the missing item or remove the reconciliation record.",
                        CanAutoFix = false
                    });
                }
                
                _logger.LogInformation("Found {Count} orphaned reconciliations", orphanedReconciliations.Count);
                
                // Check for defective items with invalid item references
                var orphanedDefectiveItems = await _dbContext.DefectiveItems
                    .Where(d => !_dbContext.Items.Any(i => i.Id == d.ItemId))
                    .ToListAsync();
                
                foreach (var defectiveItem in orphanedDefectiveItems)
                {
                    issues.Add(new DataIntegrityIssue
                    {
                        EntityType = "DefectiveItem",
                        EntityId = defectiveItem.Id.ToString(),
                        PropertyName = "ItemId",
                        RuleName = Name,
                        Severity = IssueSeverity.Error,
                        Message = $"Defective item record references a non-existent item",
                        Details = $"Defective Item ID: {defectiveItem.Id}, Invalid ItemId: {defectiveItem.ItemId}, " +
                                  $"Date: {defectiveItem.ReportDate}",
                        ResolutionSuggestion = "Either restore the missing item or remove the defective item record.",
                        CanAutoFix = false
                    });
                }
                
                _logger.LogInformation("Found {Count} orphaned defective item records", orphanedDefectiveItems.Count);
                
                // Check for item exchanges with invalid item references
                var orphanedItemExchanges = await _dbContext.ItemExchanges
                    .Where(e => !_dbContext.Items.Any(i => i.Id == e.OriginalItemId) || 
                                !_dbContext.Items.Any(i => i.Id == e.NewItemId))
                    .ToListAsync();
                
                foreach (var exchange in orphanedItemExchanges)
                {
                    string invalidReference = !_dbContext.Items.Any(i => i.Id == exchange.OriginalItemId) ? 
                        $"Invalid OriginalItemId: {exchange.OriginalItemId}" : 
                        $"Invalid NewItemId: {exchange.NewItemId}";
                    
                    issues.Add(new DataIntegrityIssue
                    {
                        EntityType = "ItemExchange",
                        EntityId = exchange.Id.ToString(),
                        PropertyName = "ItemId",
                        RuleName = Name,
                        Severity = IssueSeverity.Error,
                        Message = $"Item exchange record references a non-existent item",
                        Details = $"Exchange ID: {exchange.Id}, {invalidReference}, " +
                                  $"Exchange Date: {exchange.ExchangeDate}",
                        ResolutionSuggestion = "Either restore the missing item(s) or remove the exchange record.",
                        CanAutoFix = false
                    });
                }
                
                _logger.LogInformation("Found {Count} orphaned item exchange records", orphanedItemExchanges.Count);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error running orphaned records rule");
                issues.Add(new DataIntegrityIssue
                {
                    EntityType = EntityType,
                    PropertyName = "System",
                    RuleName = Name,
                    Severity = IssueSeverity.Error,
                    Message = "Failed to check for orphaned records",
                    Details = ex.ToString(),
                    CanAutoFix = false
                });
            }
            
            return issues;
        }
    }
}
