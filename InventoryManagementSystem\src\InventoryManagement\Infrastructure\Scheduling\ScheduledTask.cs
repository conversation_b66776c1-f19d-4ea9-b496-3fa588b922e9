using System;
using System.Collections.Generic;

namespace InventoryManagement.Infrastructure.Scheduling
{
    /// <summary>
    /// Represents a task that can be scheduled for automated execution
    /// </summary>
    public class ScheduledTask
    {
        /// <summary>
        /// Unique identifier for this task
        /// </summary>
        public Guid Id { get; set; } = Guid.NewGuid();
        
        /// <summary>
        /// Name of the task
        /// </summary>
        public string Name { get; set; }
        
        /// <summary>
        /// Description of what the task does
        /// </summary>
        public string Description { get; set; }
        
        /// <summary>
        /// Type name of the task handler class
        /// </summary>
        public string TaskHandlerTypeName { get; set; }
        
        /// <summary>
        /// Task parameters stored as serialized JSON
        /// </summary>
        public string TaskParametersJson { get; set; }
        
        /// <summary>
        /// Recurrence schedule for the task
        /// </summary>
        public TaskSchedule Schedule { get; set; } = new TaskSchedule();
        
        /// <summary>
        /// Whether this task is currently enabled
        /// </summary>
        public bool IsEnabled { get; set; } = true;
        
        /// <summary>
        /// Last time the task was executed
        /// </summary>
        public DateTime? LastRunTime { get; set; }
        
        /// <summary>
        /// Status of the last run
        /// </summary>
        public TaskExecutionStatus LastRunStatus { get; set; } = TaskExecutionStatus.None;
        
        /// <summary>
        /// Error message from the last run if it failed
        /// </summary>
        public string LastErrorMessage { get; set; }
        
        /// <summary>
        /// Next scheduled run time
        /// </summary>
        public DateTime? NextRunTime { get; set; }
        
        /// <summary>
        /// Maximum number of retry attempts if execution fails
        /// </summary>
        public int MaxRetries { get; set; } = 3;
        
        /// <summary>
        /// Current number of retries for the current execution
        /// </summary>
        public int CurrentRetryCount { get; set; }
        
        /// <summary>
        /// Timeout in seconds for task execution
        /// </summary>
        public int TimeoutSeconds { get; set; } = 300;
        
        /// <summary>
        /// Whether to run this task at system startup
        /// </summary>
        public bool RunAtStartup { get; set; }
        
        /// <summary>
        /// User who created the task
        /// </summary>
        public string CreatedBy { get; set; }
        
        /// <summary>
        /// When the task was created
        /// </summary>
        public DateTime CreatedDate { get; set; } = DateTime.Now;
    }
    
    /// <summary>
    /// Status of task execution
    /// </summary>
    public enum TaskExecutionStatus
    {
        /// <summary>
        /// Task has never been executed
        /// </summary>
        None,
        
        /// <summary>
        /// Task is currently running
        /// </summary>
        Running,
        
        /// <summary>
        /// Task completed successfully
        /// </summary>
        Success,
        
        /// <summary>
        /// Task failed
        /// </summary>
        Failed,
        
        /// <summary>
        /// Task timed out
        /// </summary>
        TimedOut,
        
        /// <summary>
        /// Task was cancelled
        /// </summary>
        Cancelled
    }
}
